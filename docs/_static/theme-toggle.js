/**
 * TurdParty Documentation Theme Toggle
 * Handles dark/light mode switching with OS preference detection
 */

class ThemeManager {
    constructor() {
        this.storageKey = 'turdparty-theme';
        this.themes = {
            DARK: 'dark',
            LIGHT: 'light',
            AUTO: 'auto'
        };
        
        this.init();
    }
    
    init() {
        // Set default theme based on system preference or stored preference
        const storedTheme = localStorage.getItem(this.storageKey);
        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        
        let initialTheme;
        if (storedTheme) {
            initialTheme = storedTheme;
        } else {
            // Default to dark mode, but respect system preference
            initialTheme = systemPrefersDark ? this.themes.DARK : this.themes.DARK; // Force dark as default
        }
        
        this.setTheme(initialTheme);
        this.createToggleButton();
        this.setupSystemThemeListener();
    }
    
    setTheme(theme) {
        const root = document.documentElement;
        
        // Remove existing theme attributes
        root.removeAttribute('data-theme');
        
        if (theme === this.themes.AUTO) {
            // Let CSS media queries handle auto theme
            const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            root.setAttribute('data-theme', systemPrefersDark ? this.themes.DARK : this.themes.LIGHT);
        } else {
            root.setAttribute('data-theme', theme);
        }
        
        // Store preference
        localStorage.setItem(this.storageKey, theme);
        
        // Update toggle button
        this.updateToggleButton(theme);
        
        // Dispatch custom event for other components
        window.dispatchEvent(new CustomEvent('themeChanged', { 
            detail: { theme, actualTheme: root.getAttribute('data-theme') }
        }));
    }
    
    getCurrentTheme() {
        return localStorage.getItem(this.storageKey) || this.themes.DARK;
    }
    
    getActualTheme() {
        return document.documentElement.getAttribute('data-theme') || this.themes.DARK;
    }
    
    toggleTheme() {
        const currentTheme = this.getCurrentTheme();
        let nextTheme;
        
        switch (currentTheme) {
            case this.themes.DARK:
                nextTheme = this.themes.LIGHT;
                break;
            case this.themes.LIGHT:
                nextTheme = this.themes.AUTO;
                break;
            case this.themes.AUTO:
                nextTheme = this.themes.DARK;
                break;
            default:
                nextTheme = this.themes.DARK;
        }
        
        this.setTheme(nextTheme);
    }
    
    createToggleButton() {
        // Create theme toggle button
        const toggleButton = document.createElement('button');
        toggleButton.id = 'theme-toggle';
        toggleButton.className = 'theme-toggle-btn';
        toggleButton.setAttribute('aria-label', 'Toggle theme');
        toggleButton.setAttribute('title', 'Toggle between dark, light, and auto themes');
        
        // Add to header or create floating button
        const header = document.querySelector('.wy-nav-top');
        if (header) {
            const container = document.createElement('div');
            container.className = 'theme-toggle-container';
            container.appendChild(toggleButton);
            header.appendChild(container);
        } else {
            // Fallback: create floating button
            toggleButton.className += ' theme-toggle-floating';
            document.body.appendChild(toggleButton);
        }
        
        toggleButton.addEventListener('click', () => this.toggleTheme());
        
        this.toggleButton = toggleButton;
        this.updateToggleButton(this.getCurrentTheme());
    }
    
    updateToggleButton(theme) {
        if (!this.toggleButton) return;
        
        const icons = {
            [this.themes.DARK]: '🌙',
            [this.themes.LIGHT]: '☀️',
            [this.themes.AUTO]: '🌓'
        };
        
        const labels = {
            [this.themes.DARK]: 'Dark Mode',
            [this.themes.LIGHT]: 'Light Mode',
            [this.themes.AUTO]: 'Auto Mode'
        };
        
        this.toggleButton.innerHTML = `
            <span class="theme-icon">${icons[theme]}</span>
            <span class="theme-label">${labels[theme]}</span>
        `;
        
        this.toggleButton.setAttribute('title', `Current: ${labels[theme]}. Click to cycle themes.`);
    }
    
    setupSystemThemeListener() {
        // Listen for system theme changes
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        mediaQuery.addEventListener('change', (e) => {
            if (this.getCurrentTheme() === this.themes.AUTO) {
                this.setTheme(this.themes.AUTO); // Refresh auto theme
            }
        });
    }
}

// RAG Status Integration for Mermaid Diagrams
class RAGStatusManager {
    constructor() {
        this.statusColors = {
            operational: '#28a745',    // Green
            degraded: '#ffc107',       // Amber
            outage: '#dc3545',         // Red
            maintenance: '#007bff',    // Blue
            unknown: '#6c757d'         // Gray
        };
        
        this.init();
    }
    
    init() {
        // Wait for Mermaid to be available
        if (typeof mermaid !== 'undefined') {
            this.setupMermaidTheme();
        } else {
            // Wait for Mermaid to load
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(() => this.setupMermaidTheme(), 1000);
            });
        }
        
        // Listen for theme changes
        window.addEventListener('themeChanged', (e) => {
            this.updateMermaidTheme(e.detail.actualTheme);
        });
    }
    
    setupMermaidTheme() {
        if (typeof mermaid === 'undefined') return;
        
        const isDark = document.documentElement.getAttribute('data-theme') === 'dark';
        
        mermaid.initialize({
            theme: isDark ? 'dark' : 'default',
            themeVariables: {
                primaryColor: '#3498DB',
                primaryTextColor: isDark ? '#e0e0e0' : '#212529',
                primaryBorderColor: '#444444',
                lineColor: isDark ? '#666666' : '#333333',
                secondaryColor: '#2ECC71',
                tertiaryColor: '#E74C3C',
                background: isDark ? '#2d2d2d' : '#ffffff',
                mainBkg: isDark ? '#2d2d2d' : '#ffffff',
                secondBkg: isDark ? '#3a3a3a' : '#f8f9fa',
                tertiaryBkg: isDark ? '#1a1a1a' : '#e9ecef'
            }
        });
        
        // Re-render existing diagrams
        this.updateExistingDiagrams();
    }
    
    updateMermaidTheme(theme) {
        if (typeof mermaid === 'undefined') return;
        
        const isDark = theme === 'dark';
        
        mermaid.initialize({
            theme: isDark ? 'dark' : 'default',
            themeVariables: {
                primaryColor: '#3498DB',
                primaryTextColor: isDark ? '#e0e0e0' : '#212529',
                primaryBorderColor: '#444444',
                lineColor: isDark ? '#666666' : '#333333',
                secondaryColor: '#2ECC71',
                tertiaryColor: '#E74C3C',
                background: isDark ? '#2d2d2d' : '#ffffff',
                mainBkg: isDark ? '#2d2d2d' : '#ffffff',
                secondBkg: isDark ? '#3a3a3a' : '#f8f9fa',
                tertiaryBkg: isDark ? '#1a1a1a' : '#e9ecef'
            }
        });
        
        this.updateExistingDiagrams();
    }
    
    updateExistingDiagrams() {
        // Find all mermaid diagrams and re-render them
        const diagrams = document.querySelectorAll('.mermaid');
        diagrams.forEach((diagram, index) => {
            const originalContent = diagram.getAttribute('data-original-content') || diagram.textContent;
            diagram.setAttribute('data-original-content', originalContent);
            
            // Clear and re-render
            diagram.innerHTML = originalContent;
            diagram.removeAttribute('data-processed');
            
            if (typeof mermaid !== 'undefined') {
                mermaid.init(undefined, diagram);
            }
        });
    }
    
    // Method to add RAG status to service nodes
    addRAGStatus(diagramId, serviceStatuses) {
        const diagram = document.getElementById(diagramId);
        if (!diagram) return;
        
        // This would integrate with your status API
        // serviceStatuses = { 'api': 'operational', 'database': 'degraded', etc. }
        
        Object.entries(serviceStatuses).forEach(([service, status]) => {
            const color = this.statusColors[status] || this.statusColors.unknown;
            
            // Find service nodes and update their styling
            const serviceNodes = diagram.querySelectorAll(`[id*="${service}"]`);
            serviceNodes.forEach(node => {
                node.style.fill = color;
                node.setAttribute('data-status', status);
                node.setAttribute('title', `${service}: ${status}`);
            });
        });
    }
}

// Initialize theme management when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.themeManager = new ThemeManager();
    window.ragStatusManager = new RAGStatusManager();
    
    // Expose global functions for external use
    window.setTheme = (theme) => window.themeManager.setTheme(theme);
    window.toggleTheme = () => window.themeManager.toggleTheme();
    window.updateServiceStatus = (diagramId, statuses) => 
        window.ragStatusManager.addRAGStatus(diagramId, statuses);
});

// CSS for theme toggle button (injected via JavaScript)
const themeToggleCSS = `
.theme-toggle-container {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
}

.theme-toggle-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.theme-toggle-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.05);
}

.theme-toggle-floating {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.theme-icon {
    font-size: 16px;
}

.theme-label {
    font-weight: 500;
}

@media (max-width: 768px) {
    .theme-label {
        display: none;
    }
    
    .theme-toggle-btn {
        padding: 8px;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        justify-content: center;
    }
}
`;

// Inject CSS
const style = document.createElement('style');
style.textContent = themeToggleCSS;
document.head.appendChild(style);
