/* Custom CSS for TurdParty Documentation - Dark Mode Default */

/* Dark Mode Color Scheme (Default) */
:root {
    --turdparty-primary: #3498DB;
    --turdparty-secondary: #2ECC71;
    --turdparty-accent: #E74C3C;
    --turdparty-warning: #F39C12;
    --turdparty-success: #27AE60;
    --turdparty-info: #17A2B8;

    /* Dark theme colors */
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3a3a3a;
    --text-primary: #e0e0e0;
    --text-secondary: #b0b0b0;
    --text-muted: #888888;
    --border-color: #444444;
    --code-bg: #1e1e1e;
    --code-text: #f8f8f2;

    /* RAG Status Colors */
    --status-red: #dc3545;
    --status-amber: #ffc107;
    --status-green: #28a745;
    --status-blue: #007bff;
    --status-purple: #6f42c1;
    --status-orange: #fd7e14;
}

/* Light Mode Override (when explicitly requested) */
[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #212529;
    --text-secondary: #495057;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
    --code-bg: #f8f9fa;
    --code-text: #212529;
}

/* Auto-detect system preference */
@media (prefers-color-scheme: light) {
    :root:not([data-theme="dark"]) {
        --bg-primary: #ffffff;
        --bg-secondary: #f8f9fa;
        --bg-tertiary: #e9ecef;
        --text-primary: #212529;
        --text-secondary: #495057;
        --text-muted: #6c757d;
        --border-color: #dee2e6;
        --code-bg: #f8f9fa;
        --code-text: #212529;
    }
}

/* Base Body and HTML Styling */
html, body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Header Customization */
.wy-nav-top {
    background: linear-gradient(135deg, var(--turdparty-primary), var(--turdparty-secondary));
    color: white;
}

.wy-nav-top a {
    color: white;
    font-weight: bold;
}

/* Sidebar Customization */
.wy-nav-side {
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
}

.wy-menu-vertical a {
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.wy-menu-vertical a:hover {
    background-color: var(--turdparty-primary);
    color: white;
}

.wy-menu-vertical li.current a {
    background-color: var(--turdparty-secondary);
    border-right: 3px solid var(--turdparty-accent);
    color: white;
}

/* Content Area */
.wy-nav-content {
    background: var(--bg-primary);
}

.wy-nav-content-wrap {
    background: var(--bg-primary);
}

/* Code Blocks */
.highlight {
    background: var(--turdparty-code) !important;
    border-radius: 8px;
    border: 1px solid #444;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.highlight pre {
    background: transparent !important;
    color: #F8F8F2;
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
    font-size: 14px;
    line-height: 1.4;
}

/* Inline Code */
code.literal {
    background: var(--turdparty-light);
    color: var(--turdparty-primary);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
    font-size: 0.9em;
    border: 1px solid #DDD;
}

/* API Endpoint Styling */
.api-endpoint {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-family: monospace;
    font-weight: bold;
}

.websocket-endpoint {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-family: monospace;
    font-weight: bold;
}

.http-method {
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    font-weight: bold;
    font-size: 0.8em;
    text-transform: uppercase;
}

.http-method.get {
    background: var(--turdparty-secondary);
    color: white;
}

.http-method.post {
    background: var(--turdparty-primary);
    color: white;
}

.http-method.put {
    background: var(--turdparty-warning);
    color: white;
}

.http-method.delete {
    background: var(--turdparty-accent);
    color: white;
}

.status-code {
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    font-weight: bold;
    font-size: 0.9em;
}

.status-code.success {
    background: #D5EDDA;
    color: #155724;
    border: 1px solid #C3E6CB;
}

.status-code.error {
    background: #F8D7DA;
    color: #721C24;
    border: 1px solid #F5C6CB;
}

/* Tables */
.wy-table-responsive table td,
.wy-table-responsive table th {
    white-space: normal;
    padding: 12px 8px;
}

.wy-table-responsive table th {
    background: var(--turdparty-primary);
    color: white;
    font-weight: bold;
}

.wy-table-responsive table tr:nth-child(even) {
    background: #F8F9FA;
}

.wy-table-responsive table tr:hover {
    background: #E8F4FD;
}

/* Admonitions */
.admonition {
    border-radius: 8px;
    border-left: 4px solid;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 20px 0;
}

.admonition.note {
    border-left-color: var(--turdparty-primary);
    background: #E3F2FD;
}

.admonition.warning {
    border-left-color: var(--turdparty-warning);
    background: #FFF3E0;
}

.admonition.danger {
    border-left-color: var(--turdparty-accent);
    background: #FFEBEE;
}

.admonition.tip {
    border-left-color: var(--turdparty-secondary);
    background: #E8F5E8;
}

.admonition-title {
    font-weight: bold;
    margin-bottom: 8px;
}

/* Buttons and Links */
a {
    color: var(--turdparty-primary);
    text-decoration: none;
}

a:hover {
    color: var(--turdparty-secondary);
    text-decoration: underline;
}

.btn {
    background: var(--turdparty-primary);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: background 0.3s ease;
}

.btn:hover {
    background: var(--turdparty-secondary);
    color: white;
    text-decoration: none;
}

/* Headers */
h1, h2, h3, h4, h5, h6 {
    color: var(--turdparty-dark);
    font-weight: 600;
}

h1 {
    border-bottom: 3px solid var(--turdparty-primary);
    padding-bottom: 10px;
}

h2 {
    border-bottom: 2px solid var(--turdparty-secondary);
    padding-bottom: 8px;
}

/* Navigation Breadcrumbs */
.wy-breadcrumbs {
    background: white;
    border-bottom: 1px solid #E1E4E5;
    padding: 12px 0;
}

.wy-breadcrumbs a {
    color: var(--turdparty-primary);
}

/* Search */
.wy-side-nav-search {
    background: var(--turdparty-dark);
}

.wy-side-nav-search input[type=text] {
    border-radius: 4px;
    border: 1px solid #555;
    background: #444;
    color: white;
}

.wy-side-nav-search input[type=text]:focus {
    border-color: var(--turdparty-primary);
    box-shadow: 0 0 0 2px rgba(41, 128, 185, 0.2);
}

/* Footer */
.rst-footer-buttons {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #E1E4E5;
}

/* Custom Classes for Documentation */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.feature-card {
    background: white;
    border: 1px solid #E1E4E5;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.feature-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.feature-card h3 {
    color: var(--turdparty-primary);
    margin-top: 0;
}

.endpoint-method {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: monospace;
    font-weight: bold;
    font-size: 0.8em;
    margin-right: 8px;
    text-transform: uppercase;
}

.endpoint-method.get { background: #28a745; color: white; }
.endpoint-method.post { background: #007bff; color: white; }
.endpoint-method.put { background: #ffc107; color: black; }
.endpoint-method.delete { background: #dc3545; color: white; }
.endpoint-method.patch { background: #6f42c1; color: white; }

.endpoint-url {
    font-family: monospace;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid #dee2e6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .feature-grid {
        grid-template-columns: 1fr;
    }
    
    .wy-table-responsive table {
        font-size: 0.9em;
    }
    
    .highlight pre {
        font-size: 12px;
    }
}

/* Print Styles */
@media print {
    .wy-nav-side,
    .wy-nav-top,
    .rst-footer-buttons {
        display: none !important;
    }
    
    .wy-nav-content {
        margin-left: 0 !important;
    }
    
    .highlight {
        background: white !important;
        border: 1px solid #ccc !important;
    }
    
    .highlight pre {
        color: black !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .wy-nav-content {
        background: #1a1a1a;
        color: #e0e0e0;
    }
    
    .feature-card {
        background: #2d2d2d;
        border-color: #444;
        color: #e0e0e0;
    }
    
    .wy-table-responsive table tr:nth-child(even) {
        background: #2a2a2a;
    }
    
    .wy-table-responsive table tr:hover {
        background: #3a3a3a;
    }
}

/* Animation for Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Syntax Highlighting Improvements */
.highlight .k { color: #66D9EF; } /* Keywords */
.highlight .s { color: #A6E22E; } /* Strings */
.highlight .n { color: #F8F8F2; } /* Names */
.highlight .c { color: #75715E; } /* Comments */
.highlight .nb { color: #AE81FF; } /* Built-ins */
.highlight .mi { color: #AE81FF; } /* Numbers */
