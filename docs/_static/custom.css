/* Custom CSS for TurdParty Documentation - Improved Dark Mode */

/* Enhanced Dark Mode Color Scheme (Default) */
:root {
    --turdparty-primary: #4A9EFF;
    --turdparty-secondary: #00D2FF;
    --turdparty-accent: #FF6B6B;
    --turdparty-warning: #FFB347;
    --turdparty-success: #51CF66;
    --turdparty-info: #339AF0;

    /* Improved Dark theme colors with better contrast */
    --bg-primary: #0d1117;
    --bg-secondary: #161b22;
    --bg-tertiary: #21262d;
    --bg-quaternary: #30363d;
    --text-primary: #f0f6fc;
    --text-secondary: #c9d1d9;
    --text-muted: #8b949e;
    --text-link: #58a6ff;
    --text-link-hover: #79c0ff;
    --border-color: #30363d;
    --border-muted: #21262d;
    --code-bg: #0d1117;
    --code-text: #e6edf3;
    --sidebar-bg: #0d1117;
    --sidebar-current: #1f6feb;
    --sidebar-hover: #21262d;

    /* RAG Status Colors */
    --status-red: #f85149;
    --status-amber: #d29922;
    --status-green: #3fb950;
    --status-blue: #58a6ff;
    --status-purple: #a5a5ff;
    --status-orange: #ff8c42;
}

/* Light Mode Override (when explicitly requested) */
[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f6f8fa;
    --bg-tertiary: #f1f3f4;
    --bg-quaternary: #e1e4e8;
    --text-primary: #24292f;
    --text-secondary: #57606a;
    --text-muted: #656d76;
    --text-link: #0969da;
    --text-link-hover: #0550ae;
    --border-color: #d0d7de;
    --border-muted: #d8dee4;
    --code-bg: #f6f8fa;
    --code-text: #24292f;
    --sidebar-bg: #f6f8fa;
    --sidebar-current: #0969da;
    --sidebar-hover: #f1f3f4;
}

/* Auto-detect system preference */
@media (prefers-color-scheme: light) {
    :root:not([data-theme="dark"]) {
        --bg-primary: #ffffff;
        --bg-secondary: #f6f8fa;
        --bg-tertiary: #f1f3f4;
        --bg-quaternary: #e1e4e8;
        --text-primary: #24292f;
        --text-secondary: #57606a;
        --text-muted: #656d76;
        --text-link: #0969da;
        --text-link-hover: #0550ae;
        --border-color: #d0d7de;
        --border-muted: #d8dee4;
        --code-bg: #f6f8fa;
        --code-text: #24292f;
        --sidebar-bg: #f6f8fa;
        --sidebar-current: #0969da;
        --sidebar-hover: #f1f3f4;
    }
}

/* Enhanced Admonition Styling for Dark Mode */
.admonition {
    margin: 1.5em 0;
    padding: 1em;
    border-radius: 8px;
    border-left: 4px solid;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.admonition.note {
    border-left-color: var(--turdparty-info);
    background: rgba(23, 162, 184, 0.15);
    border: 1px solid rgba(23, 162, 184, 0.3);
}

.admonition.warning {
    border-left-color: var(--turdparty-warning);
    background: rgba(243, 156, 18, 0.15);
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.admonition.important {
    border-left-color: var(--turdparty-accent);
    background: rgba(231, 76, 60, 0.15);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.admonition.tip {
    border-left-color: var(--turdparty-success);
    background: rgba(39, 174, 96, 0.15);
    border: 1px solid rgba(39, 174, 96, 0.3);
}

.admonition.caution {
    border-left-color: var(--turdparty-warning);
    background: rgba(243, 156, 18, 0.15);
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.admonition.danger {
    border-left-color: var(--turdparty-accent);
    background: rgba(231, 76, 60, 0.15);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.admonition-title {
    font-weight: bold;
    margin-bottom: 0.5em;
    color: var(--text-primary);
    font-size: 1.1em;
}

.admonition p {
    margin: 0.5em 0;
    color: var(--text-primary);
}

.admonition p:last-child {
    margin-bottom: 0;
}

/* Base Body and HTML Styling */
html, body {
    background-color: var(--bg-primary) !important;
    color: var(--text-primary) !important;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* RTD Theme Dark Mode Overrides */
.rst-content {
    background: var(--bg-primary) !important;
    color: var(--text-primary) !important;
}

/* Header Customization */
.wy-nav-top {
    background: linear-gradient(135deg, var(--turdparty-primary), var(--turdparty-secondary)) !important;
    color: white !important;
    border-bottom: 1px solid var(--border-color);
}

.wy-nav-top a {
    color: white !important;
    font-weight: bold;
}

/* Improved Sidebar Customization */
.wy-nav-side {
    background: var(--sidebar-bg) !important;
    border-right: 1px solid var(--border-color);
    width: 300px;
}

.wy-side-nav-search {
    background: var(--sidebar-bg) !important;
    border-bottom: 1px solid var(--border-color);
}

.wy-side-nav-search > a {
    color: var(--text-primary) !important;
    font-size: 1.1em;
    font-weight: 600;
}

.wy-menu-vertical {
    background: var(--sidebar-bg) !important;
}

.wy-menu-vertical a {
    color: var(--text-secondary) !important;
    transition: all 0.2s ease;
    border-bottom: 1px solid transparent;
    padding: 8px 16px;
}

.wy-menu-vertical a:hover {
    background-color: var(--sidebar-hover) !important;
    color: var(--text-primary) !important;
    border-left: 3px solid var(--turdparty-primary);
}

.wy-menu-vertical li.current > a {
    background-color: var(--sidebar-current) !important;
    color: white !important;
    border-left: 4px solid var(--turdparty-accent);
    font-weight: 600;
}

.wy-menu-vertical li.current a {
    background-color: var(--sidebar-current) !important;
    color: white !important;
}

.wy-menu-vertical li.toctree-l2.current > a {
    background: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
    border-left: 3px solid var(--turdparty-secondary);
}

.wy-menu-vertical li.toctree-l3.current > a {
    background: var(--bg-quaternary) !important;
    color: var(--text-secondary) !important;
    border-left: 2px solid var(--turdparty-info);
}

/* Content Area */
.wy-nav-content {
    background: var(--bg-primary) !important;
    margin-left: 300px;
}

.wy-nav-content-wrap {
    background: var(--bg-primary) !important;
}

/* Improved Code Blocks */
.highlight {
    background: var(--code-bg) !important;
    border-radius: 8px;
    border: 1px solid var(--border-color);
    box-shadow: 0 2px 8px rgba(0,0,0,0.3);
    margin: 16px 0;
    overflow: hidden;
}

.highlight pre {
    background: transparent !important;
    color: var(--code-text) !important;
    font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
    font-size: 14px;
    line-height: 1.5;
    padding: 16px;
    margin: 0;
    overflow-x: auto;
}

/* Inline Code */
code.literal, .rst-content code.literal {
    background: var(--bg-tertiary) !important;
    color: var(--turdparty-primary) !important;
    padding: 3px 6px;
    border-radius: 4px;
    font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
    font-size: 0.9em;
    border: 1px solid var(--border-color);
    font-weight: 500;
}

/* Code block line numbers */
.highlight .linenos {
    background: var(--bg-secondary) !important;
    color: var(--text-muted) !important;
    border-right: 1px solid var(--border-color);
    padding-right: 8px;
    margin-right: 8px;
}

/* API Endpoint Styling */
.api-endpoint {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-family: monospace;
    font-weight: bold;
}

.websocket-endpoint {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-family: monospace;
    font-weight: bold;
}

.http-method {
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    font-weight: bold;
    font-size: 0.8em;
    text-transform: uppercase;
}

.http-method.get {
    background: var(--turdparty-secondary);
    color: white;
}

.http-method.post {
    background: var(--turdparty-primary);
    color: white;
}

.http-method.put {
    background: var(--turdparty-warning);
    color: white;
}

.http-method.delete {
    background: var(--turdparty-accent);
    color: white;
}

.status-code {
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    font-weight: bold;
    font-size: 0.9em;
}

.status-code.success {
    background: #D5EDDA;
    color: #155724;
    border: 1px solid #C3E6CB;
}

.status-code.error {
    background: #F8D7DA;
    color: #721C24;
    border: 1px solid #F5C6CB;
}

/* Improved Tables */
.wy-table-responsive table,
.rst-content table.docutils {
    background: var(--bg-secondary) !important;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.wy-table-responsive table td,
.wy-table-responsive table th,
.rst-content table.docutils td,
.rst-content table.docutils th {
    white-space: normal;
    padding: 12px 16px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-primary) !important;
}

.wy-table-responsive table th,
.rst-content table.docutils th {
    background: var(--turdparty-primary) !important;
    color: white !important;
    font-weight: 600;
    text-align: left;
}

.wy-table-responsive table tr:nth-child(even),
.rst-content table.docutils tr:nth-child(even) {
    background: var(--bg-tertiary) !important;
}

.wy-table-responsive table tr:hover,
.rst-content table.docutils tr:hover {
    background: var(--bg-quaternary) !important;
}

/* Enhanced Admonitions for Dark Mode */
.admonition, .rst-content .admonition {
    border-radius: 8px !important;
    border-left: 4px solid !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3) !important;
    margin: 20px 0 !important;
    padding: 16px !important;
    background: rgba(45, 45, 45, 0.8) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: #e0e0e0 !important;
}

.admonition.note, .rst-content .admonition.note {
    border-left-color: #17a2b8 !important;
    background: rgba(23, 162, 184, 0.15) !important;
    border-color: rgba(23, 162, 184, 0.3) !important;
}

.admonition.warning, .rst-content .admonition.warning {
    border-left-color: #ffc107 !important;
    background: rgba(255, 193, 7, 0.15) !important;
    border-color: rgba(255, 193, 7, 0.3) !important;
}

.admonition.danger, .rst-content .admonition.danger {
    border-left-color: #dc3545 !important;
    background: rgba(220, 53, 69, 0.15) !important;
    border-color: rgba(220, 53, 69, 0.3) !important;
}

.admonition.important, .rst-content .admonition.important {
    border-left-color: #dc3545 !important;
    background: rgba(220, 53, 69, 0.15) !important;
    border-color: rgba(220, 53, 69, 0.3) !important;
}

.admonition.tip, .rst-content .admonition.tip {
    border-left-color: #28a745 !important;
    background: rgba(40, 167, 69, 0.15) !important;
    border-color: rgba(40, 167, 69, 0.3) !important;
}

.admonition.caution, .rst-content .admonition.caution {
    border-left-color: #ffc107 !important;
    background: rgba(255, 193, 7, 0.15) !important;
    border-color: rgba(255, 193, 7, 0.3) !important;
}

.admonition-title, .rst-content .admonition-title {
    font-weight: bold !important;
    margin-bottom: 12px !important;
    color: #ffffff !important;
    font-size: 1.1em !important;
    background: transparent !important;
}

.admonition p, .rst-content .admonition p {
    margin: 8px 0 !important;
    color: #e0e0e0 !important;
}

.admonition p:last-child, .rst-content .admonition p:last-child {
    margin-bottom: 0 !important;
}

/* Improved Links and Buttons */
a, .rst-content a {
    color: var(--text-link) !important;
    text-decoration: none;
    transition: color 0.2s ease;
}

a:hover, .rst-content a:hover {
    color: var(--text-link-hover) !important;
    text-decoration: underline;
}

a:visited, .rst-content a:visited {
    color: var(--turdparty-info) !important;
}

.btn {
    background: var(--turdparty-primary);
    color: white !important;
    padding: 10px 20px;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
    font-weight: 500;
    border: 1px solid transparent;
}

.btn:hover {
    background: var(--turdparty-secondary);
    color: white !important;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

/* Text and Paragraph Styling */
p, .rst-content p {
    color: var(--text-primary) !important;
    line-height: 1.6;
    margin-bottom: 16px;
}

.rst-content {
    color: var(--text-primary) !important;
}

/* List Styling */
ul, ol, .rst-content ul, .rst-content ol {
    color: var(--text-primary) !important;
}

li, .rst-content li {
    color: var(--text-primary) !important;
    margin-bottom: 4px;
}

/* Improved Headers */
h1, h2, h3, h4, h5, h6,
.rst-content h1, .rst-content h2, .rst-content h3,
.rst-content h4, .rst-content h5, .rst-content h6 {
    color: var(--text-primary) !important;
    font-weight: 600;
    margin-top: 24px;
    margin-bottom: 16px;
}

h1, .rst-content h1 {
    border-bottom: 3px solid var(--turdparty-primary);
    padding-bottom: 12px;
    font-size: 2.2em;
    color: var(--text-primary) !important;
}

h2, .rst-content h2 {
    border-bottom: 2px solid var(--turdparty-secondary);
    padding-bottom: 8px;
    font-size: 1.8em;
    color: var(--text-primary) !important;
}

h3, .rst-content h3 {
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 6px;
    font-size: 1.4em;
    color: var(--text-primary) !important;
}

h4, h5, h6, .rst-content h4, .rst-content h5, .rst-content h6 {
    color: var(--text-secondary) !important;
}

/* Navigation Breadcrumbs */
.wy-breadcrumbs {
    background: var(--bg-primary) !important;
    border-bottom: 1px solid var(--border-color);
    padding: 12px 0;
}

.wy-breadcrumbs a {
    color: var(--text-link) !important;
}

.wy-breadcrumbs li {
    color: var(--text-secondary) !important;
}

/* Improved Search */
.wy-side-nav-search input[type=text] {
    border-radius: 6px;
    border: 1px solid var(--border-color) !important;
    background: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
    padding: 8px 12px;
    font-size: 14px;
    transition: all 0.2s ease;
}

.wy-side-nav-search input[type=text]:focus {
    border-color: var(--turdparty-primary) !important;
    box-shadow: 0 0 0 3px rgba(74, 158, 255, 0.2);
    outline: none;
}

.wy-side-nav-search input[type=text]::placeholder {
    color: var(--text-muted) !important;
}

/* Footer */
.rst-footer-buttons {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #E1E4E5;
}

/* Custom Classes for Documentation */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.feature-card {
    background: white;
    border: 1px solid #E1E4E5;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.feature-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.feature-card h3 {
    color: var(--turdparty-primary);
    margin-top: 0;
}

.endpoint-method {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: monospace;
    font-weight: bold;
    font-size: 0.8em;
    margin-right: 8px;
    text-transform: uppercase;
}

.endpoint-method.get { background: #28a745; color: white; }
.endpoint-method.post { background: #007bff; color: white; }
.endpoint-method.put { background: #ffc107; color: black; }
.endpoint-method.delete { background: #dc3545; color: white; }
.endpoint-method.patch { background: #6f42c1; color: white; }

.endpoint-url {
    font-family: monospace;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid #dee2e6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .feature-grid {
        grid-template-columns: 1fr;
    }
    
    .wy-table-responsive table {
        font-size: 0.9em;
    }
    
    .highlight pre {
        font-size: 12px;
    }

    .wy-nav-content {
        margin-left: 0 !important;
    }

    .wy-nav-side {
        left: -300px;
    }
}

/* Print Styles */
@media print {
    .wy-nav-side,
    .wy-nav-top,
    .rst-footer-buttons {
        display: none !important;
    }
    
    .wy-nav-content {
        margin-left: 0 !important;
    }
    
    .highlight {
        background: white !important;
        border: 1px solid #ccc !important;
    }
    
    .highlight pre {
        color: black !important;
    }
}

/* Enhanced Dark Mode Support and RTD Theme Overrides */
.rst-content .section {
    background: var(--bg-primary) !important;
    color: var(--text-primary) !important;
}

.rst-content .toctree-wrapper {
    background: var(--bg-secondary) !important;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 16px;
}

.rst-content .toctree-wrapper p.caption {
    color: var(--text-primary) !important;
    font-weight: 600;
    margin-bottom: 12px;
}

.rst-content .toctree-wrapper ul {
    background: transparent !important;
}

.rst-content .toctree-wrapper ul li a {
    color: var(--text-link) !important;
    padding: 4px 0;
    display: block;
}

.rst-content .toctree-wrapper ul li a:hover {
    color: var(--text-link-hover) !important;
    background: var(--bg-tertiary) !important;
    padding-left: 8px;
    transition: all 0.2s ease;
}

/* Footer Improvements */
.rst-footer-buttons {
    background: var(--bg-secondary) !important;
    border-top: 1px solid var(--border-color);
    border-radius: 8px 8px 0 0;
    padding: 20px;
    margin-top: 40px;
}

.rst-footer-buttons .btn {
    background: var(--bg-tertiary) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color);
}

.rst-footer-buttons .btn:hover {
    background: var(--turdparty-primary) !important;
    color: white !important;
    border-color: var(--turdparty-primary);
}

/* Scrollbar Styling for Dark Mode */
::-webkit-scrollbar {
    width: 12px;
    height: 12px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 6px;
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 6px;
    border: 2px solid var(--bg-secondary);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-muted);
}

::-webkit-scrollbar-corner {
    background: var(--bg-secondary);
}

/* Animation for Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Enhanced Syntax Highlighting for Dark Mode */
.highlight .k, .highlight .kc, .highlight .kd, .highlight .kn, .highlight .kp, .highlight .kr, .highlight .kt {
    color: #ff7b72 !important; /* Keywords - red */
}
.highlight .s, .highlight .s1, .highlight .s2, .highlight .sb, .highlight .sc, .highlight .sd, .highlight .se, .highlight .sh, .highlight .si, .highlight .sx {
    color: #a5d6ff !important; /* Strings - light blue */
}
.highlight .n, .highlight .na, .highlight .nc, .highlight .nd, .highlight .ne, .highlight .nf, .highlight .ni, .highlight .nl, .highlight .nn, .highlight .no, .highlight .nt, .highlight .nv, .highlight .nx, .highlight .py {
    color: #e6edf3 !important; /* Names - white */
}
.highlight .c, .highlight .c1, .highlight .cm, .highlight .cp, .highlight .cs {
    color: #8b949e !important; /* Comments - gray */
}
.highlight .nb, .highlight .bp {
    color: #79c0ff !important; /* Built-ins - purple */
}
.highlight .mi, .highlight .mf, .highlight .mh, .highlight .mo {
    color: #79c0ff !important; /* Numbers - purple */
}
.highlight .o, .highlight .ow {
    color: #ff7b72 !important; /* Operators - red */
}
.highlight .p {
    color: #e6edf3 !important; /* Punctuation - white */
}
.highlight .gd {
    color: #ffa198 !important; /* Deleted - light red */
    background-color: #490202 !important;
}
.highlight .gi {
    color: #56d364 !important; /* Inserted - green */
    background-color: #0f5132 !important;
}
.highlight .gh {
    color: #1f6feb !important; /* Generic heading - blue */
    font-weight: bold;
}
.highlight .gu {
    color: #8b949e !important; /* Generic subheading - gray */
    font-weight: bold;
}

/* Additional Theme Toggle Button Improvements */
.theme-toggle-container {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1000;
}

.theme-toggle-btn {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    border-radius: 20px !important;
    color: white !important;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    font-size: 14px;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    font-family: inherit;
}

.theme-toggle-btn:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

.theme-toggle-floating {
    position: fixed !important;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-color) !important;
}

.theme-icon {
    font-size: 16px;
    line-height: 1;
}

.theme-label {
    font-weight: 500;
    white-space: nowrap;
}

/* Mobile responsive theme toggle */
@media (max-width: 768px) {
    .theme-label {
        display: none;
    }

    .theme-toggle-btn {
        padding: 8px !important;
        border-radius: 50% !important;
        width: 40px;
        height: 40px;
        justify-content: center;
    }

    .theme-toggle-container {
        right: 10px;
    }
}

/* Focus states for accessibility */
.theme-toggle-btn:focus {
    outline: 2px solid var(--turdparty-primary);
    outline-offset: 2px;
}

/* Ensure proper contrast in light mode */
[data-theme="light"] .theme-toggle-btn {
    background: rgba(0, 0, 0, 0.1) !important;
    border-color: rgba(0, 0, 0, 0.2) !important;
    color: #333 !important;
}

[data-theme="light"] .theme-toggle-btn:hover {
    background: rgba(0, 0, 0, 0.2) !important;
}
