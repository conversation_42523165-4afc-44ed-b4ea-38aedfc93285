/* Custom CSS for TurdParty Documentation - Dark Mode Default */

/* Dark Mode Color Scheme (Default) */
:root {
    --turdparty-primary: #3498DB;
    --turdparty-secondary: #2ECC71;
    --turdparty-accent: #E74C3C;
    --turdparty-warning: #F39C12;
    --turdparty-success: #27AE60;
    --turdparty-info: #17A2B8;

    /* Dark theme colors */
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #3a3a3a;
    --text-primary: #e0e0e0;
    --text-secondary: #b0b0b0;
    --text-muted: #888888;
    --border-color: #444444;
    --code-bg: #1e1e1e;
    --code-text: #f8f8f2;

    /* RAG Status Colors */
    --status-red: #dc3545;
    --status-amber: #ffc107;
    --status-green: #28a745;
    --status-blue: #007bff;
    --status-purple: #6f42c1;
    --status-orange: #fd7e14;
}

/* Light Mode Override (when explicitly requested) */
[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #212529;
    --text-secondary: #495057;
    --text-muted: #6c757d;
    --border-color: #dee2e6;
    --code-bg: #f8f9fa;
    --code-text: #212529;
}

/* Auto-detect system preference */
@media (prefers-color-scheme: light) {
    :root:not([data-theme="dark"]) {
        --bg-primary: #ffffff;
        --bg-secondary: #f8f9fa;
        --bg-tertiary: #e9ecef;
        --text-primary: #212529;
        --text-secondary: #495057;
        --text-muted: #6c757d;
        --border-color: #dee2e6;
        --code-bg: #f8f9fa;
        --code-text: #212529;
    }
}

/* Enhanced Admonition Styling for Dark Mode */
.admonition {
    margin: 1.5em 0;
    padding: 1em;
    border-radius: 8px;
    border-left: 4px solid;
    background: var(--bg-tertiary);
    color: var(--text-primary);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.admonition.note {
    border-left-color: var(--turdparty-info);
    background: rgba(23, 162, 184, 0.15);
    border: 1px solid rgba(23, 162, 184, 0.3);
}

.admonition.warning {
    border-left-color: var(--turdparty-warning);
    background: rgba(243, 156, 18, 0.15);
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.admonition.important {
    border-left-color: var(--turdparty-accent);
    background: rgba(231, 76, 60, 0.15);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.admonition.tip {
    border-left-color: var(--turdparty-success);
    background: rgba(39, 174, 96, 0.15);
    border: 1px solid rgba(39, 174, 96, 0.3);
}

.admonition.caution {
    border-left-color: var(--turdparty-warning);
    background: rgba(243, 156, 18, 0.15);
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.admonition.danger {
    border-left-color: var(--turdparty-accent);
    background: rgba(231, 76, 60, 0.15);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

.admonition-title {
    font-weight: bold;
    margin-bottom: 0.5em;
    color: var(--text-primary);
    font-size: 1.1em;
}

.admonition p {
    margin: 0.5em 0;
    color: var(--text-primary);
}

.admonition p:last-child {
    margin-bottom: 0;
}

/* Base Body and HTML Styling */
html, body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Header Customization */
.wy-nav-top {
    background: linear-gradient(135deg, var(--turdparty-primary), var(--turdparty-secondary));
    color: white;
}

.wy-nav-top a {
    color: white;
    font-weight: bold;
}

/* Sidebar Customization */
.wy-nav-side {
    background: var(--bg-secondary);
    border-right: 1px solid var(--border-color);
}

.wy-menu-vertical a {
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.wy-menu-vertical a:hover {
    background-color: var(--turdparty-primary);
    color: white;
}

.wy-menu-vertical li.current a {
    background-color: var(--turdparty-secondary);
    border-right: 3px solid var(--turdparty-accent);
    color: white;
}

/* Content Area */
.wy-nav-content {
    background: var(--bg-primary);
}

.wy-nav-content-wrap {
    background: var(--bg-primary);
}

/* Code Blocks */
.highlight {
    background: var(--turdparty-code) !important;
    border-radius: 8px;
    border: 1px solid #444;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.highlight pre {
    background: transparent !important;
    color: #F8F8F2;
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
    font-size: 14px;
    line-height: 1.4;
}

/* Inline Code */
code.literal {
    background: var(--turdparty-light);
    color: var(--turdparty-primary);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
    font-size: 0.9em;
    border: 1px solid #DDD;
}

/* API Endpoint Styling */
.api-endpoint {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-family: monospace;
    font-weight: bold;
}

.websocket-endpoint {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 2px 8px;
    border-radius: 4px;
    font-family: monospace;
    font-weight: bold;
}

.http-method {
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    font-weight: bold;
    font-size: 0.8em;
    text-transform: uppercase;
}

.http-method.get {
    background: var(--turdparty-secondary);
    color: white;
}

.http-method.post {
    background: var(--turdparty-primary);
    color: white;
}

.http-method.put {
    background: var(--turdparty-warning);
    color: white;
}

.http-method.delete {
    background: var(--turdparty-accent);
    color: white;
}

.status-code {
    padding: 2px 6px;
    border-radius: 3px;
    font-family: monospace;
    font-weight: bold;
    font-size: 0.9em;
}

.status-code.success {
    background: #D5EDDA;
    color: #155724;
    border: 1px solid #C3E6CB;
}

.status-code.error {
    background: #F8D7DA;
    color: #721C24;
    border: 1px solid #F5C6CB;
}

/* Tables */
.wy-table-responsive table td,
.wy-table-responsive table th {
    white-space: normal;
    padding: 12px 8px;
}

.wy-table-responsive table th {
    background: var(--turdparty-primary);
    color: white;
    font-weight: bold;
}

.wy-table-responsive table tr:nth-child(even) {
    background: #F8F9FA;
}

.wy-table-responsive table tr:hover {
    background: #E8F4FD;
}

/* Enhanced Admonitions for Dark Mode */
.admonition, .rst-content .admonition {
    border-radius: 8px !important;
    border-left: 4px solid !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.3) !important;
    margin: 20px 0 !important;
    padding: 16px !important;
    background: rgba(45, 45, 45, 0.8) !important;
    border: 1px solid rgba(255, 255, 255, 0.1) !important;
    color: #e0e0e0 !important;
}

.admonition.note, .rst-content .admonition.note {
    border-left-color: #17a2b8 !important;
    background: rgba(23, 162, 184, 0.15) !important;
    border-color: rgba(23, 162, 184, 0.3) !important;
}

.admonition.warning, .rst-content .admonition.warning {
    border-left-color: #ffc107 !important;
    background: rgba(255, 193, 7, 0.15) !important;
    border-color: rgba(255, 193, 7, 0.3) !important;
}

.admonition.danger, .rst-content .admonition.danger {
    border-left-color: #dc3545 !important;
    background: rgba(220, 53, 69, 0.15) !important;
    border-color: rgba(220, 53, 69, 0.3) !important;
}

.admonition.important, .rst-content .admonition.important {
    border-left-color: #dc3545 !important;
    background: rgba(220, 53, 69, 0.15) !important;
    border-color: rgba(220, 53, 69, 0.3) !important;
}

.admonition.tip, .rst-content .admonition.tip {
    border-left-color: #28a745 !important;
    background: rgba(40, 167, 69, 0.15) !important;
    border-color: rgba(40, 167, 69, 0.3) !important;
}

.admonition.caution, .rst-content .admonition.caution {
    border-left-color: #ffc107 !important;
    background: rgba(255, 193, 7, 0.15) !important;
    border-color: rgba(255, 193, 7, 0.3) !important;
}

.admonition-title, .rst-content .admonition-title {
    font-weight: bold !important;
    margin-bottom: 12px !important;
    color: #ffffff !important;
    font-size: 1.1em !important;
    background: transparent !important;
}

.admonition p, .rst-content .admonition p {
    margin: 8px 0 !important;
    color: #e0e0e0 !important;
}

.admonition p:last-child, .rst-content .admonition p:last-child {
    margin-bottom: 0 !important;
}

/* Buttons and Links */
a {
    color: var(--turdparty-primary);
    text-decoration: none;
}

a:hover {
    color: var(--turdparty-secondary);
    text-decoration: underline;
}

.btn {
    background: var(--turdparty-primary);
    color: white;
    padding: 8px 16px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    transition: background 0.3s ease;
}

.btn:hover {
    background: var(--turdparty-secondary);
    color: white;
    text-decoration: none;
}

/* Headers */
h1, h2, h3, h4, h5, h6 {
    color: var(--turdparty-dark);
    font-weight: 600;
}

h1 {
    border-bottom: 3px solid var(--turdparty-primary);
    padding-bottom: 10px;
}

h2 {
    border-bottom: 2px solid var(--turdparty-secondary);
    padding-bottom: 8px;
}

/* Navigation Breadcrumbs */
.wy-breadcrumbs {
    background: white;
    border-bottom: 1px solid #E1E4E5;
    padding: 12px 0;
}

.wy-breadcrumbs a {
    color: var(--turdparty-primary);
}

/* Search */
.wy-side-nav-search {
    background: var(--turdparty-dark);
}

.wy-side-nav-search input[type=text] {
    border-radius: 4px;
    border: 1px solid #555;
    background: #444;
    color: white;
}

.wy-side-nav-search input[type=text]:focus {
    border-color: var(--turdparty-primary);
    box-shadow: 0 0 0 2px rgba(41, 128, 185, 0.2);
}

/* Footer */
.rst-footer-buttons {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #E1E4E5;
}

/* Custom Classes for Documentation */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.feature-card {
    background: white;
    border: 1px solid #E1E4E5;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.feature-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.feature-card h3 {
    color: var(--turdparty-primary);
    margin-top: 0;
}

.endpoint-method {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-family: monospace;
    font-weight: bold;
    font-size: 0.8em;
    margin-right: 8px;
    text-transform: uppercase;
}

.endpoint-method.get { background: #28a745; color: white; }
.endpoint-method.post { background: #007bff; color: white; }
.endpoint-method.put { background: #ffc107; color: black; }
.endpoint-method.delete { background: #dc3545; color: white; }
.endpoint-method.patch { background: #6f42c1; color: white; }

.endpoint-url {
    font-family: monospace;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    border: 1px solid #dee2e6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .feature-grid {
        grid-template-columns: 1fr;
    }
    
    .wy-table-responsive table {
        font-size: 0.9em;
    }
    
    .highlight pre {
        font-size: 12px;
    }
}

/* Print Styles */
@media print {
    .wy-nav-side,
    .wy-nav-top,
    .rst-footer-buttons {
        display: none !important;
    }
    
    .wy-nav-content {
        margin-left: 0 !important;
    }
    
    .highlight {
        background: white !important;
        border: 1px solid #ccc !important;
    }
    
    .highlight pre {
        color: black !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .wy-nav-content {
        background: #1a1a1a;
        color: #e0e0e0;
    }
    
    .feature-card {
        background: #2d2d2d;
        border-color: #444;
        color: #e0e0e0;
    }
    
    .wy-table-responsive table tr:nth-child(even) {
        background: #2a2a2a;
    }
    
    .wy-table-responsive table tr:hover {
        background: #3a3a3a;
    }
}

/* Animation for Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Syntax Highlighting Improvements */
.highlight .k { color: #66D9EF; } /* Keywords */
.highlight .s { color: #A6E22E; } /* Strings */
.highlight .n { color: #F8F8F2; } /* Names */
.highlight .c { color: #75715E; } /* Comments */
.highlight .nb { color: #AE81FF; } /* Built-ins */
.highlight .mi { color: #AE81FF; } /* Numbers */
