# TurdParty Testing Guide

This document provides comprehensive information about the testing framework and strategies implemented for the TurdParty project.

## 🧪 Testing Overview

TurdParty implements a multi-layered testing strategy to ensure code quality, security, and reliability:

- **Unit Tests** - Fast, isolated component tests
- **Integration Tests** - Service interaction tests
- **Security Tests** - Vulnerability and security validation
- **Performance Tests** - Load and performance validation
- **BEHAVE Tests** - Business scenario validation (BDD)
- **End-to-End Tests** - Complete user workflow validation
- **Edge Case Tests** - Boundary and error condition testing

## 📁 Test Structure

```
tests/
├── conftest.py                 # Shared fixtures and configuration
├── requirements.txt            # Testing dependencies
├── config/
│   └── pytest.ini            # Pytest configuration
├── unit/                      # Unit tests
│   ├── test_elk_logger.py
│   ├── test_file_injection_service.py
│   ├── test_routes.py
│   └── test_worker_services.py
├── integration/               # Integration tests
│   ├── test_elk_integration.py
│   ├── test_file_injection.py
│   └── test_minio_integration.py
├── security/                  # Security tests
│   ├── test_security_validation.py
│   └── test_container_security.py
├── performance/               # Performance tests
│   └── test_load_performance.py
├── edge_cases/               # Edge case tests
│   └── test_edge_cases.py
├── features/                 # BEHAVE BDD tests
│   ├── file_injection.feature
│   └── steps/
│       └── file_injection_steps.py
└── e2e/                     # End-to-end tests
    └── test_complete_workflow.py
```

## 🚀 Quick Start

### Run All Tests
```bash
./scripts/run_tests.sh
```

### Run Specific Test Types
```bash
# Unit tests only
./scripts/run_tests.sh --unit-only

# With performance tests
./scripts/run_tests.sh --with-performance

# Fast tests (unit only, no linting)
./scripts/run_tests.sh --unit-only --no-linting
```

### Run Tests in CI/CD
```bash
./scripts/ci_test_runner.sh
```

## 🔧 Test Configuration

### Environment Variables
```bash
# Test environment
export TEST_MODE=true
export IN_DOCKER=true
export DEBUG=false

# Coverage threshold
export COVERAGE_THRESHOLD=80

# Parallel execution
export PARALLEL_WORKERS=4

# Service URLs (for integration tests)
export API_URL=http://api:8000
export ELASTICSEARCH_URL=http://elasticsearch:9200
export MINIO_ENDPOINT=minio:9000
```

### Pytest Configuration
Key pytest settings in `tests/config/pytest.ini`:
- Parallel execution with `pytest-xdist`
- Coverage reporting with `pytest-cov`
- Custom markers for test categorisation
- Timeout settings for long-running tests
- Comprehensive logging configuration

## 📊 Coverage Management

### Generate Coverage Reports
```bash
# Run tests with coverage
pytest tests/unit/ --cov=api --cov-report=html

# Manage coverage reports
./scripts/manage_coverage.sh full
```

### Coverage Thresholds
- **Minimum Coverage**: 80%
- **Target Coverage**: 90%+
- **Critical Components**: 95%+

### Coverage Exclusions
Coverage reports exclude:
- Test files themselves
- Configuration files
- Migration scripts
- Development utilities

## 🛡️ Security Testing

### Security Test Categories

#### Input Validation
- Path traversal prevention
- Command injection prevention
- File upload validation
- XSS prevention
- SQL injection prevention

#### Container Security
- User privilege validation
- Filesystem permission checks
- Capability restrictions
- Mount point security
- Network isolation

#### Authentication & Authorization
- JWT token validation
- Rate limiting
- Session management
- Access control

#### Data Security
- Sensitive data handling
- Temporary file cleanup
- Hash verification
- Encryption validation

### Run Security Tests
```bash
# All security tests
pytest tests/security/ -v

# Specific security categories
pytest tests/security/test_security_validation.py -k "input_validation"
pytest tests/security/test_container_security.py -k "container_isolation"
```

## ⚡ Performance Testing

### Performance Test Categories

#### Load Testing
- Concurrent request handling
- File upload performance
- Database query performance
- Cache performance

#### Resource Testing
- Memory usage validation
- CPU utilisation limits
- File descriptor limits
- Disk space management

#### Stress Testing
- High concurrency scenarios
- Large file handling
- Resource exhaustion scenarios
- Recovery testing

### Run Performance Tests
```bash
# Performance tests
pytest tests/performance/ -v

# Load testing with Locust
locust -f tests/performance/locustfile.py --host=http://api:8000
```

## 🎭 BEHAVE Testing (BDD)

### Feature Testing
BEHAVE tests validate business scenarios:
- File upload workflows
- VM injection processes
- ELK logging pipelines
- Error handling scenarios
- Security validations

### Run BEHAVE Tests
```bash
# All BEHAVE tests
behave tests/features/

# Specific feature
behave tests/features/file_injection.feature

# With specific tags
behave tests/features/ --tags=@critical
```

## 🌐 End-to-End Testing

### E2E Test Coverage
- Complete user workflows
- UI interaction testing
- Cross-browser compatibility
- Responsive design validation
- Accessibility compliance
- Performance metrics

### Run E2E Tests
```bash
# All E2E tests
pytest tests/e2e/ -v

# With browser UI (non-headless)
pytest tests/e2e/ --headed

# Specific browser
pytest tests/e2e/ --browser=firefox
```

## 🔍 Edge Case Testing

### Edge Case Categories
- Boundary conditions
- Resource exhaustion
- Network failures
- Data corruption
- Timing issues
- Concurrency problems

### Run Edge Case Tests
```bash
pytest tests/edge_cases/ -v
```

## 📈 Test Reporting

### Coverage Reports
- **HTML Report**: `test-results/htmlcov/index.html`
- **XML Report**: `test-results/coverage.xml`
- **JSON Report**: `test-results/coverage.json`

### Test Result Reports
- **Unit Tests**: `test-results/unit-test-report.html`
- **Integration Tests**: `test-results/integration-test-report.html`
- **Security Tests**: `test-results/security-test-report.html`
- **Performance Tests**: `test-results/performance-test-report.html`
- **BEHAVE Tests**: `test-results/behave-report.html`
- **E2E Tests**: `test-results/e2e-test-report.html`

### CI/CD Integration
Test results are automatically published to:
- GitHub Actions (via workflow outputs)
- GitLab CI (via artifacts)
- Jenkins (via plugins)
- Coverage badges and metrics

## 🛠️ Development Workflow

### Pre-commit Testing
```bash
# Quick validation before commit
pytest tests/unit/ -x --ff

# Full validation
./scripts/run_tests.sh --unit-only
```

### Test-Driven Development
1. Write failing test
2. Implement minimal code to pass
3. Refactor while maintaining tests
4. Ensure coverage requirements

### Continuous Integration
1. Automated test execution on PR
2. Coverage validation
3. Security scanning
4. Performance regression detection
5. Deployment gates based on test results

## 🐛 Debugging Tests

### Debug Failed Tests
```bash
# Run with verbose output
pytest tests/unit/test_file_injection.py -v -s

# Run specific test with debugging
pytest tests/unit/test_file_injection.py::test_create_injection -v -s --pdb

# Run with coverage and debugging
pytest tests/unit/ --cov=api --cov-report=term-missing -v
```

### Test Isolation
```bash
# Run tests in isolation
pytest tests/unit/test_file_injection.py --forked

# Clear cache between runs
pytest --cache-clear
```

## 📋 Test Maintenance

### Regular Maintenance Tasks
- Update test dependencies
- Review and update test data
- Validate test performance
- Update security test scenarios
- Refresh E2E test selectors

### Test Quality Metrics
- Test execution time
- Test flakiness detection
- Coverage trend analysis
- Security test effectiveness
- Performance regression tracking

## 🔗 Related Documentation

- [Development Setup](./DEVELOPMENT.md)
- [Security Guidelines](./SECURITY.md)
- [Performance Monitoring](./PERFORMANCE.md)
- [CI/CD Pipeline](./CICD.md)
- [Troubleshooting](./TROUBLESHOOTING.md)

## 📞 Support

For testing-related questions or issues:
1. Check existing test documentation
2. Review test failure logs
3. Consult team testing guidelines
4. Create issue with test reproduction steps
