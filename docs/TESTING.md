# TurdParty Testing Guide

## 🎉 Modern Testing Stack Overview

TurdParty uses a **world-class, industry-standard testing framework** designed for reliability, performance, and maintainability. Our comprehensive testing strategy includes:

### 🛠️ Core Testing Tools
- **pytest** - Modern test execution framework
- **Hypothesis** - Property-based testing for edge case discovery
- **pytest-benchmark** - Performance regression testing
- **Locust** - Realistic load testing scenarios
- **Ruff** - Fast Python linting and formatting
- **MyPy** - Static type checking
- **Bandit** - Security vulnerability scanning
- **pre-commit** - Automated quality gates

### 🧪 Test Categories
- **Unit Tests** - Fast, isolated component validation (36 tests)
- **Property-Based Tests** - Hypothesis-driven edge case testing (9 tests)
- **Performance Tests** - Benchmark and regression testing
- **Security Tests** - Vulnerability and attack vector validation
- **Integration Tests** - Service interaction and API testing
- **Load Tests** - Realistic user behaviour simulation

## 📁 Modern Test Structure

```
tests/
├── conftest.py                    # Shared fixtures and configuration
├── unit/                         # Unit tests (36 tests) ✅
│   ├── test_basic.py            # Basic functionality validation
│   ├── test_models_validation.py # Pydantic model testing
│   ├── test_file_injection_service.py
│   ├── test_routes.py
│   └── test_performance_edge_cases.py
├── property/                     # Property-based tests (9 tests) ✅
│   └── test_property_based.py   # Hypothesis-driven testing
├── performance/                  # Performance benchmarks ✅
│   ├── test_benchmarks.py       # pytest-benchmark tests
│   └── test_load_performance.py # Load testing scenarios
├── security/                     # Security validation ✅
│   ├── test_security_validation.py
│   └── test_container_security.py
├── integration/                  # Integration tests ✅
│   ├── test_file_injection_api.py
│   └── test_elk_integration.py
├── load/                        # Load testing ✅
│   └── locustfile.py           # Locust scenarios
├── features/                    # BDD tests (legacy)
│   ├── file_injection.feature
│   └── steps/
└── e2e/                        # End-to-end tests
    └── test_complete_workflow.py
```

### 🎯 Test Status Summary
| Test Type | Count | Status | Performance |
|-----------|-------|--------|-------------|
| **Unit Tests** | 36 | ✅ PASSING | 0.06s |
| **Property Tests** | 9 | ✅ PASSING | 1.02s |
| **Performance** | Benchmarks | ✅ WORKING | 1.54M ops/s |
| **Code Quality** | Ruff | ✅ CLEAN | 90% issues fixed |

## 🚀 Quick Start

### Modern Test Execution

```bash
# Run all unit tests
python -m pytest tests/unit/ -v

# Run specific test types
python -m pytest tests/property/ -v          # Property-based tests
python -m pytest tests/performance/ --benchmark-only  # Performance tests
python -m pytest tests/security/ -v         # Security tests

# Run with coverage
python -m pytest tests/unit/ --cov=api --cov=services --cov-report=html
```

### Using the Modern Test Runner

```bash
# Show all available commands
./scripts/test_runner.sh help

# Run complete test suite
./scripts/test_runner.sh all

# Run specific test types
./scripts/test_runner.sh unit         # Unit tests
./scripts/test_runner.sh property    # Property-based tests
./scripts/test_runner.sh performance # Performance benchmarks
./scripts/test_runner.sh security    # Security tests
./scripts/test_runner.sh lint        # Code quality checks
```

### Development Environment (Nix)
```bash
# Enter Nix development shell with all tools
nix-shell

# Modern testing aliases available:
# tp-test, tp-test-property, tp-benchmark, tp-lint, tp-security
```

## 🔧 Modern Test Configuration

### pytest Configuration (`pyproject.toml`)

```toml
[tool.pytest.ini_options]
minversion = "7.0"
testpaths = ["tests"]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "performance: Performance tests",
    "security: Security tests",
    "property: Property-based tests",
    "benchmark: Benchmark tests",
]
```

### Test Markers

Use markers to run specific test categories:

```bash
# Run only unit tests
python -m pytest -m unit

# Run performance tests
python -m pytest -m performance --benchmark-only

# Run security tests
python -m pytest -m security

# Skip slow tests
python -m pytest -m "not slow"
```

### Environment Variables
```bash
# Test configuration
export TEST_MODE=true
export COVERAGE_THRESHOLD=80
export PARALLEL_WORKERS=4

# Service endpoints (for integration tests)
export API_BASE_URL=http://localhost:8000
export DATABASE_URL=postgresql://test:test@localhost/test_db
export REDIS_URL=redis://localhost:6379/1
```

## 📊 Coverage Management

### Generate Coverage Reports
```bash
# Run tests with coverage
pytest tests/unit/ --cov=api --cov-report=html

# Manage coverage reports
./scripts/manage_coverage.sh full
```

### Coverage Thresholds
- **Minimum Coverage**: 80%
- **Target Coverage**: 90%+
- **Critical Components**: 95%+

### Coverage Exclusions
Coverage reports exclude:
- Test files themselves
- Configuration files
- Migration scripts
- Development utilities

## 🛡️ Security Testing

### Security Test Categories

#### Input Validation
- Path traversal prevention
- Command injection prevention
- File upload validation
- XSS prevention
- SQL injection prevention

#### Container Security
- User privilege validation
- Filesystem permission checks
- Capability restrictions
- Mount point security
- Network isolation

#### Authentication & Authorization
- JWT token validation
- Rate limiting
- Session management
- Access control

#### Data Security
- Sensitive data handling
- Temporary file cleanup
- Hash verification
- Encryption validation

### Run Security Tests
```bash
# All security tests
pytest tests/security/ -v

# Specific security categories
pytest tests/security/test_security_validation.py -k "input_validation"
pytest tests/security/test_container_security.py -k "container_isolation"
```

## ⚡ Performance Testing

### Performance Test Categories

#### Load Testing
- Concurrent request handling
- File upload performance
- Database query performance
- Cache performance

#### Resource Testing
- Memory usage validation
- CPU utilisation limits
- File descriptor limits
- Disk space management

#### Stress Testing
- High concurrency scenarios
- Large file handling
- Resource exhaustion scenarios
- Recovery testing

### Run Performance Tests
```bash
# Performance tests
pytest tests/performance/ -v

# Load testing with Locust
locust -f tests/performance/locustfile.py --host=http://api:8000
```

## 🎭 BEHAVE Testing (BDD)

### Feature Testing
BEHAVE tests validate business scenarios:
- File upload workflows
- VM injection processes
- ELK logging pipelines
- Error handling scenarios
- Security validations

### Run BEHAVE Tests
```bash
# All BEHAVE tests
behave tests/features/

# Specific feature
behave tests/features/file_injection.feature

# With specific tags
behave tests/features/ --tags=@critical
```

## 🌐 End-to-End Testing

### E2E Test Coverage
- Complete user workflows
- UI interaction testing
- Cross-browser compatibility
- Responsive design validation
- Accessibility compliance
- Performance metrics

### Run E2E Tests
```bash
# All E2E tests
pytest tests/e2e/ -v

# With browser UI (non-headless)
pytest tests/e2e/ --headed

# Specific browser
pytest tests/e2e/ --browser=firefox
```

## 🔍 Edge Case Testing

### Edge Case Categories
- Boundary conditions
- Resource exhaustion
- Network failures
- Data corruption
- Timing issues
- Concurrency problems

### Run Edge Case Tests
```bash
pytest tests/edge_cases/ -v
```

## 📈 Test Reporting

### Coverage Reports
- **HTML Report**: `test-results/htmlcov/index.html`
- **XML Report**: `test-results/coverage.xml`
- **JSON Report**: `test-results/coverage.json`

### Test Result Reports
- **Unit Tests**: `test-results/unit-test-report.html`
- **Integration Tests**: `test-results/integration-test-report.html`
- **Security Tests**: `test-results/security-test-report.html`
- **Performance Tests**: `test-results/performance-test-report.html`
- **BEHAVE Tests**: `test-results/behave-report.html`
- **E2E Tests**: `test-results/e2e-test-report.html`

### CI/CD Integration
Test results are automatically published to:
- GitHub Actions (via workflow outputs)
- GitLab CI (via artifacts)
- Jenkins (via plugins)
- Coverage badges and metrics

## 🛠️ Development Workflow

### Pre-commit Testing
```bash
# Quick validation before commit
pytest tests/unit/ -x --ff

# Full validation
./scripts/run_tests.sh --unit-only
```

### Test-Driven Development
1. Write failing test
2. Implement minimal code to pass
3. Refactor while maintaining tests
4. Ensure coverage requirements

### Continuous Integration
1. Automated test execution on PR
2. Coverage validation
3. Security scanning
4. Performance regression detection
5. Deployment gates based on test results

## 🐛 Debugging Tests

### Debug Failed Tests
```bash
# Run with verbose output
pytest tests/unit/test_file_injection.py -v -s

# Run specific test with debugging
pytest tests/unit/test_file_injection.py::test_create_injection -v -s --pdb

# Run with coverage and debugging
pytest tests/unit/ --cov=api --cov-report=term-missing -v
```

### Test Isolation
```bash
# Run tests in isolation
pytest tests/unit/test_file_injection.py --forked

# Clear cache between runs
pytest --cache-clear
```

## 📋 Test Maintenance

### Regular Maintenance Tasks
- Update test dependencies
- Review and update test data
- Validate test performance
- Update security test scenarios
- Refresh E2E test selectors

### Test Quality Metrics
- Test execution time
- Test flakiness detection
- Coverage trend analysis
- Security test effectiveness
- Performance regression tracking

## 🔗 Related Documentation

- [Development Setup](./DEVELOPMENT.md)
- [Security Guidelines](./SECURITY.md)
- [Performance Monitoring](./PERFORMANCE.md)
- [CI/CD Pipeline](./CICD.md)
- [Troubleshooting](./TROUBLESHOOTING.md)

## 📞 Support

For testing-related questions or issues:
1. Check existing test documentation
2. Review test failure logs
3. Consult team testing guidelines
4. Create issue with test reproduction steps
