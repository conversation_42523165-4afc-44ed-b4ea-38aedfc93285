====================
🎨 Admonition Styles Test
====================

This page demonstrates all admonition types with improved dark-mode visibility.

.. contents:: Table of Contents
   :local:
   :depth: 2

📝 Note Admonitions
==================

.. admonition:: Note
   :class: note

   **Target Audience**: This guide is designed for developers and technical professionals who want to understand and deploy the TurdParty stack.

.. note::
   This is a standard note admonition with important information for users.

⚠️ Warning Admonitions
======================

.. admonition:: Development Security
   :class: warning

   ⚠️ **Development Environment Only**
   
   The default configuration is designed for development and testing:
   
   - Default passwords are used
   - No authentication is required
   - Services are accessible without encryption
   - Network isolation is minimal
   
   **Never use development configuration in production!**

.. warning::
   This is a standard warning about potential issues or important considerations.

🚨 Important/Danger Admonitions
==============================

.. admonition:: Production Hardening
   :class: important

   🔒 **Production Deployment Requirements**
   
   Before production deployment:
   
   - [ ] Change all default passwords
   - [ ] Enable authentication (OAuth2/JWT)
   - [ ] Configure SSL/TLS certificates
   - [ ] Implement network firewalls
   - [ ] Enable audit logging
   - [ ] Set up monitoring and alerting
   - [ ] Configure backup and recovery
   - [ ] Perform security assessment

.. important::
   This contains critical information that must not be overlooked.

.. danger::
   This warns about dangerous operations that could cause data loss or security issues.

💡 Tip Admonitions
==================

.. tip::
   This provides helpful tips and best practices for users.

.. admonition:: Prerequisites Check
   :class: tip

   ✅ **System Requirements**: 16GB+ RAM, 8+ CPU cores, 500GB+ storage  
   ✅ **Docker & Docker Compose**: Latest versions installed  
   ✅ **Vagrant & VirtualBox**: For full VM analysis (optional)  
   ✅ **Git**: For repository cloning  
   ✅ **Network Configuration**: Local domain routing configured  

⚠️ Caution Admonitions
======================

.. caution::
   Exercise caution when performing these operations as they may affect system stability.

🔧 Custom Admonitions
====================

.. admonition:: Prerequisites Check
   :class: note

   ✅ **System Requirements**: 16GB+ RAM, 8+ CPU cores, 500GB+ storage  
   ✅ **Docker & Docker Compose**: Latest versions installed  
   ✅ **Vagrant & VirtualBox**: For full VM analysis (optional)  
   ✅ **Git**: For repository cloning  
   ✅ **Network Configuration**: Local domain routing configured  

.. admonition:: Port Conflicts
   :class: warning

   **Issue**: Port 80/443 already in use  
   **Solution**: Stop conflicting services or change Traefik ports

.. admonition:: Memory Issues
   :class: warning

   **Issue**: Services failing to start due to memory  
   **Solution**: Increase Docker memory limits or reduce concurrent services

.. admonition:: Network Issues
   :class: warning

   **Issue**: Services not accessible via domain names  
   **Solution**: Verify /etc/hosts configuration and Docker networks

📊 Styling Verification
======================

All admonition types should now have:

- **Dark backgrounds** with proper contrast
- **Colored left borders** indicating the type
- **Readable text** in light colors
- **Proper spacing** and padding
- **Consistent styling** across all types

The improvements include:

1. **Enhanced contrast** for dark mode compatibility
2. **Stronger borders** with type-specific colors
3. **Better typography** with improved readability
4. **Consistent spacing** across all admonition types
5. **RTD theme overrides** to ensure proper rendering
