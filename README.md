# TurdParty Binary Analysis Framework - Collaboration repo

A collaborative development environment for VM management, file handling, and AppImage deployment with a **world-class modern testing framework**.

## 🎉 Current Status: Modern Testing Framework Complete

[![Tests](https://img.shields.io/badge/Tests-21%2F21%20Passing-brightgreen)](tests/)
[![Python](https://img.shields.io/badge/Python-3.12.8-blue)](https://python.org)
[![Testing](https://img.shields.io/badge/Testing-pytest%20%2B%20Hypothesis-green)](https://pytest.org)
[![Performance](https://img.shields.io/badge/Performance-1.30M%20ops%2Fs-red)](tests/performance/)
[![Code Quality](https://img.shields.io/badge/Code%20Quality-Zero%20Warnings-yellow)](https://github.com/astral-sh/ruff)

### ✅ What's Working and Tested

- **21 comprehensive tests** across multiple testing paradigms
- **Zero warnings** - Clean, modern Python codebase
- **1.30M operations/second** - High-performance model creation
- **Complete Nix development environment** with all dependencies
- **Comprehensive Sphinx documentation** with interactive examples

## 🏗️ System Architecture

TurdParty implements a comprehensive binary analysis pipeline with VM isolation, data persistence, and real-time monitoring:

```mermaid
graph TB
    %% External Layer
    subgraph "🌐 External Interface"
        USER[👤 User/Client]
        TRAEFIK[🔀 Traefik Proxy<br/>Authentication & Routing]
    end

    %% API Layer
    subgraph "🚀 API Layer"
        API[🐍 FastAPI Service<br/>Port: 8000<br/>File Upload & Management]
    end

    %% Storage Layer
    subgraph "💾 Storage Layer"
        MINIO[🗄️ MinIO Object Storage<br/>Port: 9000-9001<br/>File Storage with UUID]
        POSTGRES[🐘 PostgreSQL Database<br/>Port: 5432<br/>Metadata & State]
        REDIS[⚡ Redis Cache<br/>Port: 6379<br/>Session & Queue Management]
    end

    %% Processing Layer
    subgraph "🔬 Analysis Layer"
        subgraph "🖥️ VM Infrastructure"
            VAGRANT[📦 Vagrant VMs<br/>Ubuntu/Windows<br/>Isolated Execution]
            DOCKER[🐳 Docker Containers<br/>Fallback VMs<br/>Network Isolated]
        end

        subgraph "📊 Monitoring"
            FIBRATUS[👁️ Fibratus Agent<br/>Runtime Monitoring<br/>Process Tracking]
            VMMONITOR[🔍 VM Monitor<br/>Resource Tracking<br/>Health Checks]
        end
    end

    %% ELK Stack
    subgraph "📈 ELK Stack - Data Pipeline"
        ELASTICSEARCH[🔍 Elasticsearch<br/>Port: 9200<br/>Search & Analytics]
        LOGSTASH[🔄 Logstash<br/>Port: 5044, 5001, 8081<br/>Data Processing]
        KIBANA[📊 Kibana<br/>Port: 5601<br/>Visualisation Dashboard]
    end

    %% Testing Infrastructure
    subgraph "🧪 Testing Infrastructure"
        PYTEST[🔬 pytest + Hypothesis<br/>Property-Based Testing]
        BENCHMARK[⚡ Performance Tests<br/>1.30M ops/s]
        INTEGRATION[🔗 Integration Tests<br/>End-to-End Validation]
        SECURITY[🛡️ Security Tests<br/>Container Isolation]
    end

    %% Development Environment
    subgraph "🛠️ Development Environment"
        NIX[❄️ Nix Shell<br/>Reproducible Environment]
        DEVCONTAINER[📦 Dev Container<br/>Docker-in-Docker]
        DOCS[📚 Sphinx Documentation<br/>Interactive Examples]
    end

    %% Data Flow Connections
    USER --> TRAEFIK
    TRAEFIK --> API

    API --> MINIO
    API --> POSTGRES
    API --> REDIS

    API --> VAGRANT
    API --> DOCKER

    VAGRANT --> FIBRATUS
    DOCKER --> VMMONITOR

    FIBRATUS --> LOGSTASH
    VMMONITOR --> LOGSTASH
    API --> LOGSTASH

    LOGSTASH --> ELASTICSEARCH
    ELASTICSEARCH --> KIBANA

    %% Testing Connections
    PYTEST -.-> API
    BENCHMARK -.-> API
    INTEGRATION -.-> VAGRANT
    INTEGRATION -.-> DOCKER
    SECURITY -.-> DOCKER

    %% Development Connections
    NIX -.-> API
    DEVCONTAINER -.-> API
    DOCS -.-> API

    %% Styling
    classDef external fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef api fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef storage fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef processing fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef elk fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef testing fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef dev fill:#e0f2f1,stroke:#004d40,stroke-width:2px

    class USER,TRAEFIK external
    class API api
    class MINIO,POSTGRES,REDIS storage
    class VAGRANT,DOCKER,FIBRATUS,VMMONITOR processing
    class ELASTICSEARCH,LOGSTASH,KIBANA elk
    class PYTEST,BENCHMARK,INTEGRATION,SECURITY testing
    class NIX,DEVCONTAINER,DOCS dev
```

### 🔄 Data Flow Pipeline

1. **File Upload** → User uploads binary via API
2. **Storage** → File stored in MinIO with UUID, metadata in PostgreSQL
3. **VM Injection** → File injected into isolated VM (Vagrant/Docker)
4. **Monitoring** → Fibratus captures runtime behaviour
5. **Data Pipeline** → Events flow through Logstash to Elasticsearch
6. **Visualisation** → Kibana provides real-time dashboards
7. **Analysis** → Complete audit trail with cryptographic integrity

### 🛡️ Security Features

- **VM Isolation** - Network-isolated containers and VMs
- **Cryptographic Integrity** - Blake3 and SHA256 hashing
- **Resource Limits** - Memory and CPU constraints
- **Security Testing** - Automated vulnerability scanning
- **Container Security** - Read-only filesystems where possible

### 📊 Performance Characteristics

- **High Throughput** - 1.30M model operations/second
- **Fast VM Boot** - 12-14 second container startup
- **Efficient Storage** - Sub-second file operations
- **Real-time Monitoring** - Live event streaming to ELK
- **Scalable Architecture** - Horizontal scaling ready

### 🚀 Service Status

| Service | Port | Status | Purpose |
|---------|------|--------|---------|
| **Elasticsearch** | 9200 | ✅ Healthy | Search & Analytics Engine |
| **MinIO** | 9000-9001 | ✅ Healthy | Object Storage with UUID |
| **PostgreSQL** | 5432 | ✅ Healthy | Metadata & State Database |
| **Redis** | 6379 | ✅ Healthy | Cache & Session Management |
| **Kibana** | 5601 | 🔄 Starting | Data Visualisation Dashboard |
| **Logstash** | 5044, 5001, 8081 | 🔄 Starting | Data Processing Pipeline |
| **API Service** | 8000 | 🔄 Starting | FastAPI Backend |
| **VM Monitor** | - | 🔄 Starting | Runtime Monitoring Agent |

**Quick Health Check:**
```bash
# Check Elasticsearch
curl http://localhost:9200/_cluster/health

# Check MinIO
curl http://localhost:9000/minio/health/live

# Check API (when running)
curl http://localhost:8000/health
```

## Development Workflow

### Branching Strategy

We use a simplified branching model based on the `master` branch:

```
master (main branch)
├── feature/your-feature-name
├── bugfix/issue-description
└── hotfix/critical-fix
```

#### Branch Types

- **`master`** - Main development branch, always deployable
- **`feature/*`** - New features and enhancements
- **`bugfix/*`** - Bug fixes and corrections
- **`hotfix/*`** - Critical fixes that need immediate attention

### Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd turdparty-collab
   ```

2. **Create your feature branch**
   ```bash
   git checkout master
   git pull origin master
   git checkout -b feature/your-feature-name
   ```

3. **Make your changes and commit**
   ```bash
   git add .
   git commit -m "feat: add your feature description"
   ```

4. **Push and create pull request**
   ```bash
   git push origin feature/your-feature-name
   ```

### Commit Message Convention

Use conventional commits for clear history:

- `feat:` - New features
- `fix:` - Bug fixes
- `docs:` - Documentation changes
- `style:` - Code style changes (formatting, etc.)
- `refactor:` - Code refactoring
- `test:` - Adding or updating tests
- `chore:` - Maintenance tasks

### Pull Request Process

1. **Create a pull request** from your feature branch to `master`
2. **Ensure your branch is up to date** with master
3. **Add a clear description** of what your changes do
4. **Request review** from at least one team member
5. **Address feedback** and update your branch as needed
6. **Merge** once approved (use "Squash and merge" for clean history)

### Code Review Guidelines

#### For Authors
- Keep pull requests focused and reasonably sized
- Write clear commit messages and PR descriptions
- Test your changes thoroughly before requesting review
- Be responsive to feedback and questions

#### For Reviewers
- Review code for functionality, readability, and maintainability
- Test the changes locally when possible
- Provide constructive feedback
- Approve when satisfied with the changes

### Development Environment

The project includes a comprehensive development container with Docker-in-Docker support, pre-configured tools, and helpful scripts.

#### Features
- **Docker-in-Docker**: Full Docker functionality within the container
- **Pre-configured Tools**: Python 3.10, Node.js 18, Git, GitHub CLI
- **VS Code Integration**: Extensions and settings optimised for development
- **Code Quality**: Ruff for linting & formatting, mypy for type checking, pre-commit hooks
- **Helper Scripts**: Simplified commands for common development tasks

#### Prerequisites
- Docker and Docker Compose
- Git
- VS Code with Dev Containers extension (recommended)
- Node.js (for frontend development)
- Python 3.8+ (for backend development)

#### Development Container (Recommended)
For the best development experience, use the provided devcontainer:

```bash
# Clone the repository
git clone <repository-url>
cd turdparty-collab

# Open in VS Code
code .

# When prompted, click "Reopen in Container"
# Or use Command Palette: "Dev Containers: Reopen in Container"

# Start services using the helper script
./dev.sh start
```

#### Manual Setup
```bash
# Copy environment variables
cp .env.example .env

# Start development environment
docker-compose up -d

# Access services
# API: http://localhost:8000
# API Documentation: http://localhost:8000/docs
# Elasticsearch: http://localhost:9200
# Kibana: http://localhost:5601
```

### Core Features

#### File Injection
Upload and inject files with automatic logging to ELK stack:

```bash
# Upload a file for injection
curl -X POST "http://localhost:8000/api/v1/file_injection/" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@your_file.sh" \
  -F "target_path=/app/scripts/your_file.sh" \
  -F "permissions=0755" \
  -F "description=Test script injection"

# Process the injection
curl -X POST "http://localhost:8000/api/v1/file_injection/{injection_id}/process"

# Check injection status
curl "http://localhost:8000/api/v1/file_injection/{injection_id}/status"
```

#### ELK Stack Monitoring
All file injection activities are automatically logged to the ELK stack:
- **Elasticsearch**: Stores all logs and events
- **Logstash**: Processes and enriches log data
- **Kibana**: Provides visualisation and search interface

### 🧪 Modern Testing Framework

TurdParty features a **world-class testing framework** with industry-standard tools and comprehensive coverage:

#### 📊 Test Suite Status

| Test Category | Count | Status | Performance | Coverage |
|---------------|-------|--------|-------------|----------|
| **Unit Tests** | 12/12 | ✅ PASSING | 0.37s | Core functionality |
| **Property Tests** | 9/9 | ✅ PASSING | 1.64s | Edge case discovery |
| **Performance Tests** | 1/1 | ✅ PASSING | 1.30M ops/s | Model creation |
| **Code Quality** | All | ✅ CLEAN | Fast | Zero warnings |

#### 🛠️ Testing Tools

- **pytest 8.3.3** - Modern test execution framework
- **Hypothesis 6.112.2** - Property-based testing for edge case discovery
- **pytest-benchmark 4.0.0** - Performance regression testing
- **Ruff** - Fast Python linting and formatting (zero issues)
- **MyPy** - Static type checking
- **Bandit** - Security vulnerability scanning

#### 🚀 Quick Start

**Using Nix (Recommended):**
```bash
# Enter development environment
nix-shell

# Run all tests
pytest tests/unit/ tests/property/ -v

# Run performance benchmarks
pytest tests/performance/ --benchmark-only

# Check code quality
ruff check . && ruff format .
```

**Individual test categories:**
```bash
# Unit tests (12 tests)
nix-shell --run "pytest tests/unit/test_basic.py -v"

# Property-based tests (9 tests)
nix-shell --run "pytest tests/property/test_property_based.py -v"

# Performance benchmarks
nix-shell --run "pytest tests/performance/test_benchmarks.py::TestModelPerformance::test_file_injection_create_performance --benchmark-only"
```

#### 📚 Documentation

Comprehensive testing documentation available:
```bash
# Build and serve documentation
cd tests/docs
./serve_docs.sh

# Or build manually
nix-shell -p python312 python312Packages.sphinx python312Packages.sphinx-rtd-theme --run "python -m sphinx -b html . _build/html"
```

#### 🎯 Test Categories

1. **Unit Tests** (`tests/unit/test_basic.py`)
   - Basic Python functionality validation
   - File operations and path handling
   - Hash calculations and cryptographic functions
   - Type annotations and modern Python patterns

2. **Property-Based Tests** (`tests/property/test_property_based.py`)
   - Automated edge case discovery with Hypothesis
   - Model validation across thousands of input combinations
   - Data integrity and consistency verification
   - Boundary condition testing

3. **Performance Tests** (`tests/performance/test_benchmarks.py`)
   - Model creation performance (1.30M operations/second)
   - Serialization and deserialization benchmarks
   - Memory usage and optimisation validation

#### ✅ Quality Assurance

- **Zero warnings** across all test runs
- **Fast execution** - Complete test suite in under 3 seconds
- **Comprehensive coverage** of core functionality
- **Automated quality gates** with pre-commit hooks

### Communication

- Use GitHub issues for bug reports and feature requests
- Use pull request comments for code-specific discussions
- Keep discussions focused and professional
- Tag relevant team members when needed

### Getting Help

- Check existing issues and documentation first
- Create a GitHub issue for bugs or feature requests
- Ask questions in pull request comments
- Reach out to team members for guidance

---

*This project follows British English conventions in all documentation.*
