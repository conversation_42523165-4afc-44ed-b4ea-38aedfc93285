# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

### Added
- Initial collaboration-focused repository setup
- Simplified branching strategy documentation
- Development workflow guidelines

### Changed

### Deprecated

### Removed

### Fixed

### Security

## [1.0.0] - 2024-05-14

### Added
- Celery integration for asynchronous task processing
  - Created task queues for file operations, VM lifecycle, VM injection, and monitoring
  - Implemented task status tracking and monitoring
  - Added Celery workers for each queue type
  - Added <PERSON>is as message broker and result backend
- Docker container namespacing with `turdparty_` prefix
  - Updated all Docker containers to use consistent naming convention
  - Created dedicated Docker networks for each environment
  - Updated scripts to work with the new container naming convention
- Comprehensive documentation
  - Added Docker namespacing documentation
  - Added Celery integration testing documentation
  - Updated existing documentation to reflect changes

### Changed
- Renamed all Docker containers from `dockerwrapper-*` to `turdparty_*`
- Updated Docker Compose files to use the new naming convention
- Updated scripts to use the new container names
- Improved testing infrastructure with Docker-based integration tests
- Enhanced README with updated information

### Fixed
- Fixed Celery integration tests
- Fixed database connection issues in Docker containers
- Fixed dependency conflicts with pydantic versions

### Removed
- Transition documentation (no longer needed)
- Old Docker container naming convention

## [0.9.0] - 2024-05-01

### Added
- Initial implementation of Celery task processing
- Basic Docker container setup
- File upload functionality
- VM management functionality
- File injection functionality

---

## Changelog Guidelines

### Types of Changes

- **Added** for new features
- **Changed** for changes in existing functionality
- **Deprecated** for soon-to-be removed features
- **Removed** for now removed features
- **Fixed** for any bug fixes
- **Security** in case of vulnerabilities

### Version Format

This project uses [Semantic Versioning](https://semver.org/):
- **MAJOR** version for incompatible API changes
- **MINOR** version for backwards-compatible functionality additions
- **PATCH** version for backwards-compatible bug fixes

### Entry Format

Each entry should:
- Use present tense ("Add feature" not "Added feature")
- Be concise but descriptive
- Include relevant context when helpful
- Group related changes together
- Use British English spelling and conventions

### Example Entry

```markdown
## [1.2.0] - 2024-06-15

### Added
- User authentication system with JWT tokens
- Password reset functionality via email
- Role-based access control for admin features

### Changed
- Updated API response format for consistency
- Improved error handling across all endpoints

### Fixed
- Resolved memory leak in file upload process
- Fixed timezone handling in date calculations

### Security
- Updated dependencies to address security vulnerabilities
- Implemented rate limiting on authentication endpoints
```
