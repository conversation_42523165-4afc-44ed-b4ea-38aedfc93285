// TurdParty Status Page JavaScript

class StatusPage {
    constructor() {
        this.services = [
            {
                name: 'Elasticsearch',
                port: '9200',
                endpoint: 'http://localhost:9200/_cluster/health',
                description: 'Search & Analytics Engine',
                icon: '🔍'
            },
            {
                name: '<PERSON><PERSON>',
                port: '9000-9001',
                endpoint: 'http://localhost:9000/minio/health/live',
                description: 'Object Storage with UUID',
                icon: '🗄️'
            },
            {
                name: 'PostgreSQL',
                port: '5432',
                endpoint: 'http://localhost:8000/api/v1/health/database',
                description: 'Metadata & State Database',
                icon: '🐘'
            },
            {
                name: 'Redis',
                port: '6379',
                endpoint: 'http://localhost:8000/api/v1/health/redis',
                description: 'Cache & Session Management',
                icon: '⚡'
            },
            {
                name: 'Kibana',
                port: '5601',
                endpoint: 'http://localhost:5601/api/status',
                description: 'Data Visualisation Dashboard',
                icon: '📊'
            },
            {
                name: 'Logstash',
                port: '5044, 5001, 8081',
                endpoint: 'http://localhost:8000/api/v1/health/logstash',
                description: 'Data Processing Pipeline',
                icon: '🔄'
            },
            {
                name: 'API Service',
                port: '8000',
                endpoint: 'http://localhost:8000/api/v1/health',
                description: 'FastAPI Backend',
                icon: '🐍'
            },
            {
                name: 'VM Monitor',
                port: '-',
                endpoint: 'http://localhost:8000/api/v1/health/vm-monitor',
                description: 'Runtime Monitoring Agent',
                icon: '🔍'
            },
            {
                name: 'Status Dashboard',
                port: '8090',
                endpoint: 'http://localhost:8090/health',
                description: 'System Monitoring Dashboard',
                icon: '📊'
            }
        ];

        this.activities = [];
        this.init();
    }

    async init() {
        this.initMermaid();
        this.renderServices();
        this.startHealthChecks();
        this.renderActivities();
        this.updateMetrics();
    }

    initMermaid() {
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#3498db',
                primaryTextColor: '#2c3e50',
                primaryBorderColor: '#2980b9',
                lineColor: '#34495e',
                sectionBkgColor: '#ecf0f1',
                altSectionBkgColor: '#bdc3c7',
                gridColor: '#95a5a6',
                secondaryColor: '#e74c3c',
                tertiaryColor: '#f39c12'
            }
        });
    }

    renderServices() {
        const servicesGrid = document.getElementById('services-grid');
        servicesGrid.innerHTML = '';

        this.services.forEach(service => {
            const serviceCard = this.createServiceCard(service);
            servicesGrid.appendChild(serviceCard);
        });
    }

    createServiceCard(service) {
        const card = document.createElement('div');
        card.className = 'service-card';
        card.innerHTML = `
            <div class="service-header">
                <div class="service-name">
                    ${service.icon} ${service.name}
                </div>
                <div class="service-status checking" id="status-${service.name.toLowerCase().replace(/\s+/g, '-')}">
                    Checking...
                </div>
            </div>
            <div class="service-details">
                <div>${service.description}</div>
                <div class="service-port">Port: ${service.port}</div>
            </div>
        `;
        return card;
    }

    async startHealthChecks() {
        // Initial check
        await this.checkAllServices();
        
        // Set up periodic checks every 30 seconds
        setInterval(() => {
            this.checkAllServices();
        }, 30000);
    }

    async checkAllServices() {
        const promises = this.services.map(service => this.checkService(service));
        const results = await Promise.allSettled(promises);
        
        this.updateOverallStatus(results);
    }

    async checkService(service) {
        const statusElement = document.getElementById(`status-${service.name.toLowerCase().replace(/\s+/g, '-')}`);
        
        try {
            // For demo purposes, simulate health checks
            // In production, these would be actual API calls
            const isHealthy = await this.simulateHealthCheck(service);
            
            if (isHealthy) {
                statusElement.textContent = 'Operational';
                statusElement.className = 'service-status operational';
                this.addActivity(service.name, 'success', 'Service is operational');
            } else {
                statusElement.textContent = 'Degraded';
                statusElement.className = 'service-status degraded';
                this.addActivity(service.name, 'warning', 'Service experiencing issues');
            }
            
            return { service: service.name, status: isHealthy ? 'operational' : 'degraded' };
        } catch (error) {
            statusElement.textContent = 'Outage';
            statusElement.className = 'service-status outage';
            this.addActivity(service.name, 'error', 'Service is down');
            return { service: service.name, status: 'outage' };
        }
    }

    async simulateHealthCheck(service) {
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, Math.random() * 1000));
        
        // Simulate different health states based on service
        const healthyServices = ['Elasticsearch', 'MinIO', 'PostgreSQL', 'Redis'];
        const degradedServices = ['Kibana', 'Logstash'];
        
        if (healthyServices.includes(service.name)) {
            return Math.random() > 0.1; // 90% uptime
        } else if (degradedServices.includes(service.name)) {
            return Math.random() > 0.3; // 70% uptime
        } else {
            return Math.random() > 0.2; // 80% uptime
        }
    }

    updateOverallStatus(results) {
        const statusIndicator = document.getElementById('status-indicator');
        const statusText = document.getElementById('status-text');
        
        const operational = results.filter(r => r.value?.status === 'operational').length;
        const total = results.length;
        
        if (operational === total) {
            statusIndicator.className = 'status-indicator operational';
            statusText.textContent = 'All Systems Operational';
        } else if (operational > total * 0.7) {
            statusIndicator.className = 'status-indicator degraded';
            statusText.textContent = 'Some Systems Degraded';
        } else {
            statusIndicator.className = 'status-indicator outage';
            statusText.textContent = 'Major Service Outage';
        }
    }

    addActivity(serviceName, type, message) {
        const activity = {
            service: serviceName,
            type: type,
            message: message,
            timestamp: new Date()
        };
        
        this.activities.unshift(activity);
        
        // Keep only last 20 activities
        if (this.activities.length > 20) {
            this.activities = this.activities.slice(0, 20);
        }
        
        this.renderActivities();
    }

    renderActivities() {
        const activityFeed = document.getElementById('activity-feed');
        activityFeed.innerHTML = '';
        
        if (this.activities.length === 0) {
            activityFeed.innerHTML = '<p style="text-align: center; color: #666;">No recent activity</p>';
            return;
        }
        
        this.activities.forEach(activity => {
            const activityItem = document.createElement('div');
            activityItem.className = 'activity-item';
            
            const iconClass = activity.type === 'success' ? 'success' : 
                             activity.type === 'warning' ? 'warning' : 'error';
            const icon = activity.type === 'success' ? '✅' : 
                        activity.type === 'warning' ? '⚠️' : '❌';
            
            activityItem.innerHTML = `
                <div class="activity-icon ${iconClass}">
                    ${icon}
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.service}: ${activity.message}</div>
                    <div class="activity-time">${this.formatTime(activity.timestamp)}</div>
                </div>
            `;
            
            activityFeed.appendChild(activityItem);
        });
    }

    formatTime(date) {
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) {
            return 'Just now';
        } else if (diff < 3600000) {
            return `${Math.floor(diff / 60000)} minutes ago`;
        } else {
            return date.toLocaleTimeString();
        }
    }

    updateMetrics() {
        // Update performance metrics periodically
        setInterval(() => {
            // Simulate metric updates
            const throughput = document.getElementById('throughput');
            const vmBoot = document.getElementById('vm-boot');
            const fileOps = document.getElementById('file-ops');
            const testSuccess = document.getElementById('test-success');
            
            // Add slight variations to make it look live
            const baseValues = {
                throughput: 1.30,
                vmBoot: 13,
                fileOps: 0.8,
                testSuccess: 63
            };
            
            throughput.textContent = `${(baseValues.throughput + (Math.random() - 0.5) * 0.1).toFixed(2)}M ops/s`;
            vmBoot.textContent = `${Math.round(baseValues.vmBoot + (Math.random() - 0.5) * 2)}s`;
            fileOps.textContent = `${(baseValues.fileOps + (Math.random() - 0.5) * 0.2).toFixed(1)}s`;
            testSuccess.textContent = `${baseValues.testSuccess}/63`;
        }, 5000);
    }
}

// Initialize the status page when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new StatusPage();
});
