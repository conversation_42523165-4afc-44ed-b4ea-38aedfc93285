<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TurdParty System Status</title>
    <link rel="stylesheet" href="assets/css/status.css">
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="status-header">
            <div class="header-content">
                <h1>🔬 TurdParty System Status</h1>
                <p class="subtitle">Binary Analysis Platform - Real-time Service Monitoring</p>
                <div class="overall-status" id="overall-status">
                    <span class="status-indicator operational" id="status-indicator"></span>
                    <span class="status-text" id="status-text">All Systems Operational</span>
                </div>
            </div>
        </header>

        <!-- System Architecture -->
        <section class="architecture-section">
            <h2>🏗️ System Architecture</h2>
            <div class="mermaid-container">
                <div class="mermaid" id="architecture-diagram">
                    graph TB
                        %% External Layer
                        subgraph "🌐 External Interface"
                            USER[👤 User/Client]
                            TRAEFIK[🔀 Traefik Proxy<br/>Authentication & Routing]
                        end

                        %% API Layer
                        subgraph "🚀 API Layer"
                            API[🐍 FastAPI Service<br/>Port: 8000<br/>File Upload & Management]
                            STATUS[📊 Status Dashboard<br/>Port: 8090<br/>System Monitoring]
                        end

                        %% Storage Layer
                        subgraph "💾 Storage Layer"
                            MINIO[🗄️ MinIO Object Storage<br/>Port: 9000-9001<br/>File Storage with UUID]
                            POSTGRES[🐘 PostgreSQL Database<br/>Port: 5432<br/>Metadata & State]
                            REDIS[⚡ Redis Cache<br/>Port: 6379<br/>Session & Queue Management]
                        end

                        %% Processing Layer
                        subgraph "🔬 Analysis Layer"
                            subgraph "🖥️ VM Infrastructure"
                                VAGRANT[📦 Vagrant VMs<br/>Ubuntu/Windows<br/>Isolated Execution]
                                DOCKER[🐳 Docker Containers<br/>Fallback VMs<br/>Network Isolated]
                            end
                            
                            subgraph "📊 Monitoring"
                                FIBRATUS[👁️ Fibratus Agent<br/>Runtime Monitoring<br/>Process Tracking]
                                VMMONITOR[🔍 VM Monitor<br/>Resource Tracking<br/>Health Checks]
                            end
                        end

                        %% ELK Stack
                        subgraph "📈 ELK Stack - Data Pipeline"
                            ELASTICSEARCH[🔍 Elasticsearch<br/>Port: 9200<br/>Search & Analytics]
                            LOGSTASH[🔄 Logstash<br/>Port: 5044, 5001, 8081<br/>Data Processing]
                            KIBANA[📊 Kibana<br/>Port: 5601<br/>Visualisation Dashboard]
                        end

                        %% Data Flow Connections
                        USER --> TRAEFIK
                        TRAEFIK --> API
                        TRAEFIK --> STATUS

                        API --> MINIO
                        API --> POSTGRES
                        API --> REDIS

                        STATUS --> API
                        STATUS --> MINIO
                        STATUS --> POSTGRES
                        STATUS --> REDIS
                        
                        API --> VAGRANT
                        API --> DOCKER
                        
                        VAGRANT --> FIBRATUS
                        DOCKER --> VMMONITOR
                        
                        FIBRATUS --> LOGSTASH
                        VMMONITOR --> LOGSTASH
                        API --> LOGSTASH
                        
                        LOGSTASH --> ELASTICSEARCH
                        ELASTICSEARCH --> KIBANA

                        %% Styling
                        classDef external fill:#e1f5fe,stroke:#01579b,stroke-width:2px
                        classDef api fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
                        classDef storage fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
                        classDef processing fill:#fff3e0,stroke:#e65100,stroke-width:2px
                        classDef elk fill:#fce4ec,stroke:#880e4f,stroke-width:2px

                        class USER,TRAEFIK external
                        class API,STATUS api
                        class MINIO,POSTGRES,REDIS storage
                        class VAGRANT,DOCKER,FIBRATUS,VMMONITOR processing
                        class ELASTICSEARCH,LOGSTASH,KIBANA elk
                </div>
            </div>
        </section>

        <!-- Service Status Grid -->
        <section class="services-section">
            <h2>🚀 Service Status</h2>
            <div class="services-grid" id="services-grid">
                <!-- Services will be populated by JavaScript -->
            </div>
        </section>

        <!-- Performance Metrics -->
        <section class="metrics-section">
            <h2>📊 Performance Metrics</h2>
            <div class="metrics-grid">
                <div class="metric-card">
                    <h3>Throughput</h3>
                    <div class="metric-value" id="throughput">1.30M ops/s</div>
                    <div class="metric-label">Model Operations</div>
                </div>
                <div class="metric-card">
                    <h3>VM Boot Time</h3>
                    <div class="metric-value" id="vm-boot">12-14s</div>
                    <div class="metric-label">Container Startup</div>
                </div>
                <div class="metric-card">
                    <h3>File Operations</h3>
                    <div class="metric-value" id="file-ops">&lt;1s</div>
                    <div class="metric-label">Upload/Download</div>
                </div>
                <div class="metric-card">
                    <h3>Test Success</h3>
                    <div class="metric-value" id="test-success">63/63</div>
                    <div class="metric-label">Tests Passing</div>
                </div>
            </div>
        </section>

        <!-- Recent Activity -->
        <section class="activity-section">
            <h2>📈 Recent Activity</h2>
            <div class="activity-feed" id="activity-feed">
                <!-- Activity will be populated by JavaScript -->
            </div>
        </section>

        <!-- Footer -->
        <footer class="status-footer">
            <div class="footer-content">
                <p>&copy; 2024 TurdParty Binary Analysis Platform</p>
                <div class="footer-links">
                    <a href="/docs">Documentation</a>
                    <a href="/api/v1/health">API Health</a>
                    <a href="http://localhost:5601">Kibana Dashboard</a>
                </div>
            </div>
        </footer>
    </div>

    <script src="assets/js/status.js"></script>
</body>
</html>
