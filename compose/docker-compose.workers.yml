# TurdParty Worker Services
# Phase 2: Celery workers for file processing, VM management, and injection

services:
  # File Operations Worker
  worker-file:
    container_name: turdpartycollab_worker_file
    build:
      context: ../services/workers
      dockerfile: Dockerfile.celery
    environment:
      # Database
      - DATABASE_URL=********************************************/turdparty
      
      # MinIO
      - MINIO_HOST=storage
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - MINIO_SECURE=false
      
      # Redis/Celery
      - REDIS_HOST=cache
      - REDIS_PORT=6379
      - REDIS_DB=0
      
      # Worker configuration
      - CELERY_WORKER_NAME=file_worker
      - CELERY_QUEUES=file_ops,default
      - CELERY_CONCURRENCY=2
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock  # For Docker-in-Docker
      - ../data/worker_logs:/app/logs
      - ../data/temp_files:/app/tmp
    depends_on:
      - cache
      - database
      - storage
    networks:
      - turdpartycollab_network
    restart: unless-stopped
    command: ["celery", "-A", "celery_app", "worker", "--loglevel=info", "--queues=file_ops,default", "--concurrency=2", "--hostname=file_worker@%h"]

  # VM Management Worker
  worker-vm:
    container_name: turdpartycollab_worker_vm
    build:
      context: ../services/workers
      dockerfile: Dockerfile.celery
    environment:
      # Database
      - DATABASE_URL=********************************************/turdparty
      
      # Redis/Celery
      - REDIS_HOST=cache
      - REDIS_PORT=6379
      - REDIS_DB=0
      
      # Worker configuration
      - CELERY_WORKER_NAME=vm_worker
      - CELERY_QUEUES=vm_ops,default
      - CELERY_CONCURRENCY=1
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock  # For Docker-in-Docker VM creation
      - ../data/worker_logs:/app/logs
      - ../data/temp_files:/app/tmp
    depends_on:
      - cache
      - database
    networks:
      - turdpartycollab_network
    restart: unless-stopped
    privileged: true  # Required for VM operations
    command: ["celery", "-A", "celery_app", "worker", "--loglevel=info", "--queues=vm_ops,default", "--concurrency=1", "--hostname=vm_worker@%h"]

  # File Injection Worker
  worker-injection:
    container_name: turdpartycollab_worker_injection
    build:
      context: ../services/workers
      dockerfile: Dockerfile.celery
    environment:
      # Database
      - DATABASE_URL=********************************************/turdparty
      
      # MinIO
      - MINIO_HOST=storage
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - MINIO_SECURE=false
      
      # Redis/Celery
      - REDIS_HOST=cache
      - REDIS_PORT=6379
      - REDIS_DB=0
      
      # Worker configuration
      - CELERY_WORKER_NAME=injection_worker
      - CELERY_QUEUES=injection_ops,default
      - CELERY_CONCURRENCY=2
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock  # For Docker container access
      - ../data/worker_logs:/app/logs
      - ../data/temp_files:/app/tmp
    depends_on:
      - cache
      - database
      - storage
    networks:
      - turdpartycollab_network
    restart: unless-stopped
    command: ["celery", "-A", "celery_app", "worker", "--loglevel=info", "--queues=injection_ops,default", "--concurrency=2", "--hostname=injection_worker@%h"]

  # Celery Flower (Task Monitoring)
  task-monitor:
    container_name: turdpartycollab_task_monitor
    build:
      context: ../services/workers
      dockerfile: Dockerfile.celery
    ports:
      - "5555:5555"
    environment:
      # Redis/Celery
      - REDIS_HOST=cache
      - REDIS_PORT=6379
      - REDIS_DB=0
      
      # Flower configuration
      - FLOWER_PORT=5555
      - FLOWER_BASIC_AUTH=admin:turdparty123
    depends_on:
      - cache
    networks:
      - turdpartycollab_network
    restart: unless-stopped
    command: ["celery", "-A", "celery_app", "flower", "--port=5555", "--basic_auth=admin:turdparty123"]

# Use existing network from core services
networks:
  turdpartycollab_network:
    external: true
