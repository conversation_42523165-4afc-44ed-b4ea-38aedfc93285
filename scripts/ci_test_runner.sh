#!/bin/bash

# CI/CD Test Runner for TurdParty
# Comprehensive test execution for continuous integration pipelines

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/.." && pwd)"
TEST_RESULTS_DIR="$PROJECT_ROOT/test-results"
COVERAGE_THRESHOLD=${COVERAGE_THRESHOLD:-80}
PARALLEL_WORKERS=${PARALLEL_WORKERS:-4}
MAX_TEST_DURATION=${MAX_TEST_DURATION:-1800}  # 30 minutes

# CI Environment Detection
CI_SYSTEM="unknown"
if [ "$GITHUB_ACTIONS" = "true" ]; then
    CI_SYSTEM="github"
elif [ "$GITLAB_CI" = "true" ]; then
    CI_SYSTEM="gitlab"
elif [ "$JENKINS_URL" ]; then
    CI_SYSTEM="jenkins"
elif [ "$BUILDKITE" = "true" ]; then
    CI_SYSTEM="buildkite"
fi

# Functions
print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ $1${NC}"
}

setup_ci_environment() {
    print_header "Setting Up CI Environment"
    
    # Set environment variables for CI
    export CI=true
    export PYTHONPATH="$PROJECT_ROOT:$PYTHONPATH"
    export TEST_MODE=true
    export DEBUG=false
    
    # Create necessary directories
    mkdir -p "$TEST_RESULTS_DIR"
    mkdir -p "$TEST_RESULTS_DIR/screenshots"
    mkdir -p "$TEST_RESULTS_DIR/videos"
    mkdir -p "$TEST_RESULTS_DIR/logs"
    
    # Set up test database if needed
    if [ "$IN_DOCKER" = "true" ]; then
        export DATABASE_URL="************************************/test_turdparty"
        export REDIS_URL="redis://redis:6379/1"
        export ELASTICSEARCH_URL="http://elasticsearch:9200"
        export MINIO_ENDPOINT="minio:9000"
    fi
    
    print_success "CI environment configured for $CI_SYSTEM"
}

install_dependencies() {
    print_header "Installing Dependencies"
    
    # Install Python dependencies
    if [ -f "$PROJECT_ROOT/requirements.txt" ]; then
        pip install -r "$PROJECT_ROOT/requirements.txt"
        print_success "Main dependencies installed"
    fi
    
    if [ -f "$PROJECT_ROOT/tests/requirements.txt" ]; then
        pip install -r "$PROJECT_ROOT/tests/requirements.txt"
        print_success "Test dependencies installed"
    fi
    
    # Install Playwright browsers if needed
    if command -v playwright &> /dev/null; then
        playwright install chromium
        print_success "Playwright browsers installed"
    fi
    
    # Install additional tools
    if ! command -v bc &> /dev/null; then
        if command -v apt-get &> /dev/null; then
            apt-get update && apt-get install -y bc
        elif command -v yum &> /dev/null; then
            yum install -y bc
        fi
    fi
}

wait_for_services() {
    print_header "Waiting for Services"
    
    if [ "$IN_DOCKER" != "true" ]; then
        print_warning "Not in Docker - skipping service checks"
        return 0
    fi
    
    # Wait for database
    print_info "Waiting for PostgreSQL..."
    for i in {1..30}; do
        if pg_isready -h postgres -p 5432 -U test &>/dev/null; then
            print_success "PostgreSQL is ready"
            break
        fi
        sleep 2
    done
    
    # Wait for Redis
    print_info "Waiting for Redis..."
    for i in {1..30}; do
        if redis-cli -h redis ping &>/dev/null; then
            print_success "Redis is ready"
            break
        fi
        sleep 2
    done
    
    # Wait for Elasticsearch
    print_info "Waiting for Elasticsearch..."
    for i in {1..60}; do
        if curl -f -s http://elasticsearch:9200/_cluster/health &>/dev/null; then
            print_success "Elasticsearch is ready"
            break
        fi
        sleep 3
    done
    
    # Wait for MinIO
    print_info "Waiting for MinIO..."
    for i in {1..30}; do
        if curl -f -s http://minio:9000/minio/health/live &>/dev/null; then
            print_success "MinIO is ready"
            break
        fi
        sleep 2
    done
    
    # Wait for API
    print_info "Waiting for API..."
    for i in {1..60}; do
        if curl -f -s http://api:8000/health &>/dev/null; then
            print_success "API is ready"
            break
        fi
        sleep 3
    done
}

run_linting_and_security() {
    print_header "Running Code Quality and Security Checks"
    
    # Run ruff for linting
    if command -v ruff &> /dev/null; then
        echo "Running ruff linting..."
        ruff check . --output-format=json > "$TEST_RESULTS_DIR/ruff-report.json" || true
        ruff check . --output-format=text > "$TEST_RESULTS_DIR/ruff-report.txt" || true
        
        # Check if there are any errors
        if ruff check . --quiet; then
            print_success "Ruff linting passed"
        else
            print_warning "Ruff linting found issues"
        fi
    fi
    
    # Run bandit for security
    if command -v bandit &> /dev/null; then
        echo "Running bandit security scan..."
        bandit -r . -f json -o "$TEST_RESULTS_DIR/bandit-report.json" || true
        bandit -r . -f txt -o "$TEST_RESULTS_DIR/bandit-report.txt" || true
        print_success "Security scan completed"
    fi
    
    # Run safety for dependency vulnerabilities
    if command -v safety &> /dev/null; then
        echo "Running safety dependency check..."
        safety check --json --output "$TEST_RESULTS_DIR/safety-report.json" || true
        safety check --output "$TEST_RESULTS_DIR/safety-report.txt" || true
        print_success "Dependency vulnerability scan completed"
    fi
}

run_unit_tests() {
    print_header "Running Unit Tests"
    
    timeout "$MAX_TEST_DURATION" pytest tests/unit/ \
        -v \
        --tb=short \
        --cov=api \
        --cov=services \
        --cov-report=html:"$TEST_RESULTS_DIR/htmlcov" \
        --cov-report=xml:"$TEST_RESULTS_DIR/coverage.xml" \
        --cov-report=json:"$TEST_RESULTS_DIR/coverage.json" \
        --cov-report=term-missing \
        --junit-xml="$TEST_RESULTS_DIR/unit-test-results.xml" \
        --html="$TEST_RESULTS_DIR/unit-test-report.html" \
        --self-contained-html \
        -n "$PARALLEL_WORKERS" \
        --dist=worksteal \
        --maxfail=10 \
        --durations=10
    
    print_success "Unit tests completed"
}

run_integration_tests() {
    if [ "$IN_DOCKER" != "true" ]; then
        print_warning "Skipping integration tests (not in Docker)"
        return 0
    fi
    
    print_header "Running Integration Tests"
    
    timeout "$MAX_TEST_DURATION" pytest tests/integration/ \
        -v \
        --tb=short \
        --junit-xml="$TEST_RESULTS_DIR/integration-test-results.xml" \
        --html="$TEST_RESULTS_DIR/integration-test-report.html" \
        --self-contained-html \
        --maxfail=5
    
    print_success "Integration tests completed"
}

run_security_tests() {
    if [ "$IN_DOCKER" != "true" ]; then
        print_warning "Skipping security tests (not in Docker)"
        return 0
    fi
    
    print_header "Running Security Tests"
    
    timeout "$MAX_TEST_DURATION" pytest tests/security/ \
        -v \
        --tb=short \
        --junit-xml="$TEST_RESULTS_DIR/security-test-results.xml" \
        --html="$TEST_RESULTS_DIR/security-test-report.html" \
        --self-contained-html \
        --maxfail=3
    
    print_success "Security tests completed"
}

run_performance_tests() {
    if [ "$IN_DOCKER" != "true" ]; then
        print_warning "Skipping performance tests (not in Docker)"
        return 0
    fi
    
    print_header "Running Performance Tests"
    
    timeout "$MAX_TEST_DURATION" pytest tests/performance/ \
        -v \
        --tb=short \
        --junit-xml="$TEST_RESULTS_DIR/performance-test-results.xml" \
        --html="$TEST_RESULTS_DIR/performance-test-report.html" \
        --self-contained-html \
        --maxfail=2
    
    print_success "Performance tests completed"
}

run_behave_tests() {
    if [ "$IN_DOCKER" != "true" ]; then
        print_warning "Skipping BEHAVE tests (not in Docker)"
        return 0
    fi
    
    print_header "Running BEHAVE Tests"
    
    timeout "$MAX_TEST_DURATION" behave tests/features/ \
        --format=json \
        --outfile="$TEST_RESULTS_DIR/behave-results.json" \
        --format=html \
        --outfile="$TEST_RESULTS_DIR/behave-report.html" \
        --no-capture \
        --no-capture-stderr
    
    print_success "BEHAVE tests completed"
}

run_e2e_tests() {
    if [ "$IN_DOCKER" != "true" ]; then
        print_warning "Skipping E2E tests (not in Docker)"
        return 0
    fi
    
    print_header "Running End-to-End Tests"
    
    timeout "$MAX_TEST_DURATION" pytest tests/e2e/ \
        -v \
        --tb=short \
        --junit-xml="$TEST_RESULTS_DIR/e2e-test-results.xml" \
        --html="$TEST_RESULTS_DIR/e2e-test-report.html" \
        --self-contained-html \
        --screenshot=only-on-failure \
        --video=retain-on-failure \
        --maxfail=3
    
    print_success "E2E tests completed"
}

check_coverage_threshold() {
    print_header "Checking Coverage Threshold"
    
    if [ ! -f "$TEST_RESULTS_DIR/coverage.xml" ]; then
        print_error "No coverage data found"
        return 1
    fi
    
    coverage_percentage=$(python3 -c "
import xml.etree.ElementTree as ET
try:
    tree = ET.parse('$TEST_RESULTS_DIR/coverage.xml')
    root = tree.getroot()
    coverage = float(root.attrib['line-rate']) * 100
    print(f'{coverage:.1f}')
except Exception as e:
    print('0.0')
    exit(1)
")
    
    echo "Coverage: ${coverage_percentage}%"
    echo "Threshold: ${COVERAGE_THRESHOLD}%"
    
    if (( $(echo "$coverage_percentage >= $COVERAGE_THRESHOLD" | bc -l) )); then
        print_success "Coverage meets threshold"
        return 0
    else
        print_error "Coverage below threshold"
        return 1
    fi
}

generate_ci_artifacts() {
    print_header "Generating CI Artifacts"
    
    # Generate test summary
    cat > "$TEST_RESULTS_DIR/ci-summary.json" << EOF
{
    "ci_system": "$CI_SYSTEM",
    "timestamp": "$(date -Iseconds)",
    "git_commit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
    "git_branch": "$(git branch --show-current 2>/dev/null || echo 'unknown')",
    "coverage_threshold": $COVERAGE_THRESHOLD,
    "parallel_workers": $PARALLEL_WORKERS,
    "in_docker": "$IN_DOCKER"
}
EOF
    
    # Generate coverage badge if coverage data exists
    if [ -f "$TEST_RESULTS_DIR/coverage.xml" ]; then
        "$SCRIPT_DIR/manage_coverage.sh" badge
    fi
    
    # Create artifact archive
    if command -v tar &> /dev/null; then
        tar -czf "$TEST_RESULTS_DIR/test-artifacts.tar.gz" -C "$TEST_RESULTS_DIR" .
        print_success "Test artifacts archived"
    fi
    
    print_success "CI artifacts generated"
}

publish_results() {
    print_header "Publishing Results"
    
    case "$CI_SYSTEM" in
        "github")
            # GitHub Actions specific publishing
            if [ "$GITHUB_ACTIONS" = "true" ]; then
                echo "::group::Test Results"
                cat "$TEST_RESULTS_DIR/ci-summary.json"
                echo "::endgroup::"
                
                # Set outputs
                if [ -f "$TEST_RESULTS_DIR/coverage.xml" ]; then
                    coverage=$(python3 -c "
import xml.etree.ElementTree as ET
tree = ET.parse('$TEST_RESULTS_DIR/coverage.xml')
root = tree.getroot()
print(f'{float(root.attrib[\"line-rate\"]) * 100:.1f}')
")
                    echo "coverage=$coverage" >> $GITHUB_OUTPUT
                fi
            fi
            ;;
        "gitlab")
            # GitLab CI specific publishing
            echo "Publishing to GitLab CI..."
            ;;
        "jenkins")
            # Jenkins specific publishing
            echo "Publishing to Jenkins..."
            ;;
        *)
            echo "Publishing results for $CI_SYSTEM..."
            ;;
    esac
    
    print_success "Results published"
}

cleanup() {
    print_header "Cleaning Up"
    
    # Clean up temporary files
    find . -name "*.pyc" -delete 2>/dev/null || true
    find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true
    
    # Clean up test databases
    if [ "$IN_DOCKER" = "true" ]; then
        # Reset test database
        psql -h postgres -U test -d test_turdparty -c "DROP SCHEMA public CASCADE; CREATE SCHEMA public;" 2>/dev/null || true
    fi
    
    print_success "Cleanup completed"
}

main() {
    print_header "TurdParty CI/CD Test Runner"
    print_info "CI System: $CI_SYSTEM"
    print_info "Docker: $IN_DOCKER"
    print_info "Coverage Threshold: $COVERAGE_THRESHOLD%"
    
    # Parse command line arguments
    RUN_UNIT=true
    RUN_INTEGRATION=true
    RUN_SECURITY=true
    RUN_PERFORMANCE=false
    RUN_BEHAVE=true
    RUN_E2E=true
    RUN_LINTING=true
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --unit-only)
                RUN_INTEGRATION=false
                RUN_SECURITY=false
                RUN_PERFORMANCE=false
                RUN_BEHAVE=false
                RUN_E2E=false
                shift
                ;;
            --no-linting)
                RUN_LINTING=false
                shift
                ;;
            --with-performance)
                RUN_PERFORMANCE=true
                shift
                ;;
            --fast)
                RUN_INTEGRATION=false
                RUN_SECURITY=false
                RUN_PERFORMANCE=false
                RUN_E2E=false
                shift
                ;;
            *)
                echo "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Set up trap for cleanup
    trap cleanup EXIT
    
    # Run CI pipeline
    setup_ci_environment
    install_dependencies
    wait_for_services
    
    if [ "$RUN_LINTING" = true ]; then
        run_linting_and_security
    fi
    
    if [ "$RUN_UNIT" = true ]; then
        run_unit_tests
    fi
    
    if [ "$RUN_INTEGRATION" = true ]; then
        run_integration_tests
    fi
    
    if [ "$RUN_SECURITY" = true ]; then
        run_security_tests
    fi
    
    if [ "$RUN_PERFORMANCE" = true ]; then
        run_performance_tests
    fi
    
    if [ "$RUN_BEHAVE" = true ]; then
        run_behave_tests
    fi
    
    if [ "$RUN_E2E" = true ]; then
        run_e2e_tests
    fi
    
    # Check coverage and generate artifacts
    check_coverage_threshold
    generate_ci_artifacts
    publish_results
    
    print_header "CI/CD Pipeline Completed Successfully"
}

# Run main function
main "$@"
