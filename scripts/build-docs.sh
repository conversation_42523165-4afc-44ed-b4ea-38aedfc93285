#!/bin/bash

# Build TurdParty Documentation
# This script builds the Sphinx documentation for the TurdParty API

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Building TurdParty API Documentation..."

# Check if we're in the right directory
if [ ! -f "docs/conf.py" ]; then
    print_error "docs/conf.py not found. Please run this script from the project root."
    exit 1
fi

# Create necessary directories
mkdir -p docs/_build
mkdir -p docs/_static

# Create a simple HTML documentation without Sphinx for now
print_status "Creating simple HTML documentation..."

cat > docs/_build/index.html << 'EOF'
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TurdParty VM WebSocket API Documentation</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .header {
            background: linear-gradient(135deg, #2980B9, #27AE60);
            color: white;
            padding: 40px 20px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .content {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2980B9;
            border-bottom: 2px solid #27AE60;
            padding-bottom: 10px;
        }
        .api-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .api-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            transition: transform 0.2s ease;
        }
        .api-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .api-card h3 {
            margin-top: 0;
            color: #2980B9;
        }
        .endpoint {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
        }
        .method {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-weight: bold;
            font-size: 0.8em;
            margin-right: 10px;
        }
        .method.get { background: #28a745; color: white; }
        .method.post { background: #007bff; color: white; }
        .method.delete { background: #dc3545; color: white; }
        .method.ws { background: #6f42c1; color: white; }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✅ ";
            margin-right: 10px;
        }
        .quick-start {
            background: #e8f5e8;
            border: 1px solid #27AE60;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .code-block {
            background: #1e1e1e;
            color: #f8f8f2;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        .nav-links {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .nav-link {
            background: #2980B9;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        .nav-link:hover {
            background: #27AE60;
            color: white;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 TurdParty VM WebSocket API</h1>
        <p>Modern Malware Analysis Platform with Real-time VM Management</p>
    </div>

    <div class="content">
        <div class="nav-links">
            <a href="http://localhost:8000/docs" class="nav-link">📚 Interactive API Docs</a>
            <a href="http://localhost:8000/redoc" class="nav-link">📖 ReDoc Documentation</a>
            <a href="http://kibana.turdparty.localhost" class="nav-link">📊 Kibana Dashboard</a>
            <a href="http://status.turdparty.localhost" class="nav-link">🔍 System Status</a>
        </div>

        <div class="section">
            <h2>🎯 Overview</h2>
            <p>The TurdParty VM WebSocket API provides comprehensive virtual machine management capabilities for malware analysis, featuring real-time monitoring, command execution, and file operations through REST APIs and WebSocket connections.</p>
            
            <ul class="feature-list">
                <li><strong>Real-time VM Management</strong> - Create, monitor, and control VMs</li>
                <li><strong>WebSocket Streaming</strong> - Live metrics and command execution</li>
                <li><strong>ECS Logging</strong> - Structured logging for monitoring</li>
                <li><strong>Multi-VM Support</strong> - Docker containers and Vagrant VMs</li>
                <li><strong>Security-First Design</strong> - Built for malware analysis</li>
                <li><strong>Performance Optimised</strong> - Concurrent request handling</li>
            </ul>
        </div>

        <div class="section">
            <h2>🚀 Quick Start</h2>
            <div class="quick-start">
                <h3>1. Check API Health</h3>
                <div class="code-block">curl http://localhost:8000/health</div>
                
                <h3>2. Create a VM</h3>
                <div class="code-block">curl -X POST http://localhost:8000/api/v1/vms/ \
  -H "Content-Type: application/json" \
  -d '{
    "name": "analysis-vm",
    "template": "ubuntu:20.04",
    "vm_type": "docker",
    "memory_mb": 1024,
    "cpus": 2,
    "domain": "TurdParty"
  }'</div>
                
                <h3>3. Monitor VM Metrics (WebSocket)</h3>
                <div class="code-block">wscat -c ws://localhost:8000/api/v1/vms/{vm_id}/metrics/stream</div>
            </div>
        </div>

        <div class="section">
            <h2>📡 API Endpoints</h2>
            <div class="api-grid">
                <div class="api-card">
                    <h3>VM Management</h3>
                    <div class="endpoint">
                        <span class="method get">GET</span> /api/v1/vms/
                    </div>
                    <div class="endpoint">
                        <span class="method post">POST</span> /api/v1/vms/
                    </div>
                    <div class="endpoint">
                        <span class="method get">GET</span> /api/v1/vms/{vm_id}
                    </div>
                    <div class="endpoint">
                        <span class="method delete">DELETE</span> /api/v1/vms/{vm_id}
                    </div>
                </div>

                <div class="api-card">
                    <h3>WebSocket Streams</h3>
                    <div class="endpoint">
                        <span class="method ws">WS</span> /api/v1/vms/{vm_id}/metrics/stream
                    </div>
                    <div class="endpoint">
                        <span class="method ws">WS</span> /api/v1/vms/{vm_id}/commands/execute
                    </div>
                    <div class="endpoint">
                        <span class="method ws">WS</span> /api/v1/vms/{vm_id}/files/upload
                    </div>
                </div>

                <div class="api-card">
                    <h3>System Information</h3>
                    <div class="endpoint">
                        <span class="method get">GET</span> /health
                    </div>
                    <div class="endpoint">
                        <span class="method get">GET</span> /api/v1/status
                    </div>
                    <div class="endpoint">
                        <span class="method get">GET</span> /api/v1/vms/templates
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔌 WebSocket Features</h2>
            <div class="api-grid">
                <div class="api-card">
                    <h3>📊 Real-time Metrics</h3>
                    <p>Stream live VM performance data including CPU, memory, disk I/O, and network statistics.</p>
                </div>
                <div class="api-card">
                    <h3>💻 Command Execution</h3>
                    <p>Execute commands interactively with real-time output streaming and exit code reporting.</p>
                </div>
                <div class="api-card">
                    <h3>📁 File Operations</h3>
                    <p>Upload files with progress tracking and monitor file system changes in real-time.</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🛠️ System Architecture</h2>
            <p>The TurdParty system consists of several integrated components:</p>
            <ul class="feature-list">
                <li><strong>FastAPI Server</strong> - REST API and WebSocket endpoints</li>
                <li><strong>VM Management Service</strong> - Docker and Vagrant VM control</li>
                <li><strong>ELK Stack</strong> - Elasticsearch, Logstash, and Kibana for logging</li>
                <li><strong>Traefik Proxy</strong> - Reverse proxy and load balancing</li>
                <li><strong>gRPC Interface</strong> - Communication with Vagrant VMs</li>
            </ul>
        </div>

        <div class="section">
            <h2>📈 Monitoring & Logging</h2>
            <p>Comprehensive monitoring and logging capabilities:</p>
            <ul class="feature-list">
                <li><strong>ECS-compliant Logging</strong> - Structured logs in Elastic Common Schema</li>
                <li><strong>Real-time Dashboards</strong> - Kibana visualizations and alerts</li>
                <li><strong>Performance Metrics</strong> - API response times and system resources</li>
                <li><strong>WebSocket Analytics</strong> - Connection tracking and data flow monitoring</li>
            </ul>
        </div>

        <div class="section">
            <h2>🔗 Access Points</h2>
            <div class="api-grid">
                <div class="api-card">
                    <h3>🌐 Web Interfaces</h3>
                    <ul>
                        <li><a href="http://localhost:8000/docs">Swagger UI</a></li>
                        <li><a href="http://kibana.turdparty.localhost">Kibana Dashboard</a></li>
                        <li><a href="http://status.turdparty.localhost">Status Page</a></li>
                    </ul>
                </div>
                <div class="api-card">
                    <h3>🔧 Development</h3>
                    <ul>
                        <li>API Server: localhost:8000</li>
                        <li>Elasticsearch: localhost:9200</li>
                        <li>Vagrant gRPC: localhost:40000</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
EOF

print_success "HTML documentation created at docs/_build/index.html"

# Create a simple README for the docs
cat > docs/README.md << 'EOF'
# TurdParty API Documentation

This directory contains the comprehensive documentation for the TurdParty VM WebSocket API.

## Quick Access

- **Interactive API Documentation**: http://localhost:8000/docs
- **ReDoc Documentation**: http://localhost:8000/redoc
- **HTML Documentation**: Open `_build/index.html` in your browser
- **Kibana Dashboard**: http://kibana.turdparty.localhost

## Building Documentation

To build the full Sphinx documentation:

```bash
# Install documentation dependencies
pip install sphinx sphinx-rtd-theme myst-parser

# Build HTML documentation
make html

# Serve documentation locally
make serve
```

## Documentation Structure

- `api/` - API reference documentation
- `websocket/` - WebSocket endpoint documentation
- `vm/` - Virtual machine management guides
- `monitoring/` - Monitoring and logging documentation
- `installation.rst` - Installation and setup guide
- `_static/` - Static assets (CSS, images)
- `_build/` - Generated documentation output

## Features Documented

- ✅ REST API endpoints with examples
- ✅ WebSocket real-time communication
- ✅ VM management and lifecycle
- ✅ ECS logging and monitoring
- ✅ Installation and configuration
- ✅ Performance optimization
- ✅ Security considerations
- ✅ Troubleshooting guides

## Contributing

When adding new documentation:

1. Follow the existing structure and style
2. Include code examples and use cases
3. Update the table of contents in `index.rst`
4. Test documentation builds locally
5. Ensure all links work correctly

For more information, see the main project README.
EOF

print_success "Documentation README created"

# Create a simple serve script
cat > docs/serve.sh << 'EOF'
#!/bin/bash
echo "Serving TurdParty documentation at http://localhost:8080"
echo "Press Ctrl+C to stop the server"
cd _build && python3 -m http.server 8080
EOF

chmod +x docs/serve.sh

print_success "Documentation serve script created"

print_status "Documentation build complete!"
echo ""
echo "📚 Access your documentation:"
echo "   • HTML: Open docs/_build/index.html in your browser"
echo "   • Serve: Run 'cd docs && ./serve.sh' for local server"
echo "   • API Docs: http://localhost:8000/docs (if API is running)"
echo ""
echo "🎯 Next steps:"
echo "   • Install Sphinx for full documentation: pip install sphinx sphinx-rtd-theme"
echo "   • Build full docs: cd docs && make html"
echo "   • Customize styling in _static/custom.css"
