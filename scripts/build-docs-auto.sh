#!/bin/bash

# Automated Documentation Builder for TurdParty
# Builds Sphinx documentation and sets up auto-rebuild on changes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
DOCS_DIR="docs"
BUILD_DIR="docs/_build/html"
WATCH_MODE=${WATCH_MODE:-false}
AUTO_RELOAD=${AUTO_RELOAD:-false}
BUILD_INTERVAL=${BUILD_INTERVAL:-30}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Sphinx is available
check_sphinx() {
    if command -v sphinx-build >/dev/null 2>&1; then
        return 0
    elif python3 -c "import sphinx" >/dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to install Sphinx if needed
install_sphinx() {
    print_status "Installing Sphinx documentation tools..."
    
    if [ -n "$NIX_SHELL" ]; then
        print_status "Using Nix environment - Sphinx should be available"
        # In Nix shell, we'll use a minimal approach
        return 0
    else
        print_warning "Sphinx not found. Installing minimal documentation tools..."
        pip3 install --user sphinx sphinx-rtd-theme myst-parser || {
            print_error "Failed to install Sphinx. Please install manually:"
            echo "  pip install sphinx sphinx-rtd-theme myst-parser"
            return 1
        }
    fi
}

# Function to build documentation
build_docs() {
    print_status "Building TurdParty documentation..."
    
    # Ensure build directory exists
    mkdir -p "$BUILD_DIR"
    
    if check_sphinx; then
        print_status "Using Sphinx to build documentation..."
        
        # Try to build with Sphinx
        if sphinx-build -b html "$DOCS_DIR" "$BUILD_DIR" -q; then
            print_success "Sphinx documentation built successfully"
            return 0
        elif python3 -m sphinx -b html "$DOCS_DIR" "$BUILD_DIR" -q; then
            print_success "Sphinx documentation built successfully"
            return 0
        else
            print_warning "Sphinx build failed, falling back to simple HTML"
        fi
    fi
    
    # Fallback: Use our simple HTML builder
    print_status "Building simple HTML documentation..."
    
    # Copy our pre-built HTML
    if [ -f "docs/_build/index.html" ]; then
        cp docs/_build/index.html "$BUILD_DIR/"
        print_success "Simple HTML documentation ready"
    else
        # Generate simple HTML
        ./scripts/build-docs.sh
        print_success "Generated simple HTML documentation"
    fi
}

# Function to extract API documentation from running service
extract_api_docs() {
    print_status "Extracting API documentation from running service..."
    
    # Check if API is running
    if curl -s http://localhost:8000/health >/dev/null 2>&1; then
        print_status "API is running, extracting OpenAPI spec..."
        
        # Create API docs directory
        mkdir -p "$BUILD_DIR/api"
        
        # Download OpenAPI spec
        curl -s http://localhost:8000/openapi.json > "$BUILD_DIR/api/openapi.json" 2>/dev/null || {
            print_warning "Could not download OpenAPI spec"
        }
        
        # Create a simple API docs page
        cat > "$BUILD_DIR/api/index.html" << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>TurdParty API Documentation</title>
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@3.25.0/swagger-ui.css" />
    <style>
        body { margin: 0; padding: 0; }
        .swagger-ui .topbar { display: none; }
    </style>
</head>
<body>
    <div id="swagger-ui"></div>
    <script src="https://unpkg.com/swagger-ui-dist@3.25.0/swagger-ui-bundle.js"></script>
    <script>
        SwaggerUIBundle({
            url: './openapi.json',
            dom_id: '#swagger-ui',
            presets: [
                SwaggerUIBundle.presets.apis,
                SwaggerUIBundle.presets.standalone
            ]
        });
    </script>
</body>
</html>
EOF
        
        print_success "API documentation extracted"
    else
        print_warning "API not running - skipping API docs extraction"
    fi
}

# Function to create navigation index
create_navigation() {
    print_status "Creating documentation navigation..."
    
    cat > "$BUILD_DIR/nav.html" << 'EOF'
<div style="background: #2980B9; color: white; padding: 15px; margin-bottom: 20px; border-radius: 5px;">
    <h3 style="margin: 0; color: white;">📚 TurdParty Documentation</h3>
    <div style="margin-top: 10px;">
        <a href="/docs/" style="color: white; margin-right: 15px;">🏠 Home</a>
        <a href="/docs/api/" style="color: white; margin-right: 15px;">📡 API Docs</a>
        <a href="http://localhost:8000/docs" style="color: white; margin-right: 15px;">🔧 Interactive API</a>
        <a href="http://kibana.turdparty.localhost" style="color: white; margin-right: 15px;">📊 Kibana</a>
        <a href="http://status.turdparty.localhost" style="color: white;">🔍 Status</a>
    </div>
</div>
EOF
    
    # Add navigation to main index if it exists
    if [ -f "$BUILD_DIR/index.html" ]; then
        # Insert navigation after body tag
        sed -i '/<body>/r docs/_build/html/nav.html' "$BUILD_DIR/index.html" 2>/dev/null || true
    fi
}

# Function to watch for changes and rebuild
watch_and_rebuild() {
    print_status "Starting documentation watch mode..."
    print_status "Watching for changes in: $DOCS_DIR"
    print_status "Press Ctrl+C to stop watching"
    
    # Initial build
    build_docs
    extract_api_docs
    create_navigation
    
    # Watch for changes
    while true; do
        # Check if any documentation files have changed
        if find "$DOCS_DIR" -name "*.rst" -o -name "*.md" -o -name "*.py" -newer "$BUILD_DIR/index.html" 2>/dev/null | grep -q .; then
            print_status "Documentation changes detected, rebuilding..."
            build_docs
            extract_api_docs
            create_navigation
            print_success "Documentation rebuilt at $(date)"
        fi
        
        # Check if API is available and extract docs
        if [ "$AUTO_RELOAD" = "true" ]; then
            extract_api_docs >/dev/null 2>&1
        fi
        
        sleep "$BUILD_INTERVAL"
    done
}

# Function to setup documentation service
setup_docs_service() {
    print_status "Setting up documentation auto-rebuild service..."
    
    # Create systemd service file (optional)
    cat > /tmp/turdparty-docs.service << EOF
[Unit]
Description=TurdParty Documentation Auto-Builder
After=network.target

[Service]
Type=simple
User=$(whoami)
WorkingDirectory=$(pwd)
Environment=WATCH_MODE=true
Environment=AUTO_RELOAD=true
ExecStart=$(pwd)/scripts/build-docs-auto.sh
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF
    
    print_status "Systemd service file created at /tmp/turdparty-docs.service"
    print_status "To install: sudo cp /tmp/turdparty-docs.service /etc/systemd/system/"
    print_status "To enable: sudo systemctl enable turdparty-docs.service"
    print_status "To start: sudo systemctl start turdparty-docs.service"
}

# Function to show documentation access info
show_access_info() {
    print_success "Documentation is now available at:"
    echo ""
    echo "📚 Main Documentation:"
    echo "   • Frontend: http://frontend.turdparty.localhost/docs/"
    echo "   • Direct: file://$(pwd)/$BUILD_DIR/index.html"
    echo ""
    echo "📡 API Documentation:"
    echo "   • Interactive: http://localhost:8000/docs"
    echo "   • ReDoc: http://localhost:8000/redoc"
    echo "   • Extracted: http://frontend.turdparty.localhost/docs/api/"
    echo ""
    echo "🔧 Development:"
    echo "   • Local server: cd docs/_build/html && python3 -m http.server 8080"
    echo "   • Watch mode: WATCH_MODE=true ./scripts/build-docs-auto.sh"
    echo ""
}

# Main execution
main() {
    print_status "TurdParty Documentation Auto-Builder"
    echo "======================================"
    
    # Parse command line arguments
    case "${1:-build}" in
        "build")
            build_docs
            extract_api_docs
            create_navigation
            show_access_info
            ;;
        "watch")
            WATCH_MODE=true
            watch_and_rebuild
            ;;
        "setup")
            setup_docs_service
            ;;
        "install")
            install_sphinx
            ;;
        "help")
            echo "Usage: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  build   - Build documentation once (default)"
            echo "  watch   - Build and watch for changes"
            echo "  setup   - Setup auto-rebuild service"
            echo "  install - Install Sphinx dependencies"
            echo "  help    - Show this help"
            echo ""
            echo "Environment variables:"
            echo "  WATCH_MODE=true     - Enable watch mode"
            echo "  AUTO_RELOAD=true    - Auto-extract API docs"
            echo "  BUILD_INTERVAL=30   - Rebuild interval in seconds"
            ;;
        *)
            print_error "Unknown command: $1"
            echo "Use '$0 help' for usage information"
            exit 1
            ;;
    esac
}

# Handle Ctrl+C gracefully
trap 'print_status "Documentation builder stopped"; exit 0' INT

# Run main function
main "$@"
