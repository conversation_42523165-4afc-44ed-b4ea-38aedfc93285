"""
Concurrent benchmark tests for TurdParty VM WebSocket system
Tests system performance under concurrent load
"""
import asyncio
import time
import pytest
import httpx
import websockets
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any
import statistics


@pytest.mark.benchmark
@pytest.mark.concurrent
@pytest.mark.performance
class TestConcurrentBenchmarks:
    """Concurrent performance benchmarks"""
    
    API_BASE_URL = "http://localhost:8000"
    WS_BASE_URL = "ws://localhost:8000"
    
    @pytest.mark.asyncio
    async def test_concurrent_api_requests(self, benchmark):
        """Benchmark concurrent API requests"""
        
        async def make_request():
            async with httpx.AsyncClient() as client:
                start_time = time.time()
                response = await client.get(f"{self.API_BASE_URL}/health")
                duration = time.time() - start_time
                return response.status_code, duration
        
        def run_concurrent_requests():
            return asyncio.run(self._run_concurrent_tasks(make_request, 20))
        
        results = benchmark(run_concurrent_requests)
        
        # Verify all requests succeeded
        status_codes = [result[0] for result in results]
        durations = [result[1] for result in results]
        
        assert all(code == 200 for code in status_codes)
        assert len(results) == 20
        
        # Performance assertions
        avg_duration = statistics.mean(durations)
        max_duration = max(durations)
        
        assert avg_duration < 0.1, f"Average response time too high: {avg_duration:.3f}s"
        assert max_duration < 0.5, f"Max response time too high: {max_duration:.3f}s"
    
    @pytest.mark.asyncio
    async def test_concurrent_vm_creation(self, benchmark):
        """Benchmark concurrent VM creation"""
        
        async def create_vm(vm_index):
            vm_data = {
                'name': f'benchmark-vm-{vm_index}',
                'template': 'ubuntu:20.04',
                'vm_type': 'docker',
                'memory_mb': 512,
                'cpus': 1,
                'domain': 'TurdParty'
            }
            
            async with httpx.AsyncClient() as client:
                start_time = time.time()
                response = await client.post(f"{self.API_BASE_URL}/api/v1/vms/", json=vm_data)
                duration = time.time() - start_time
                
                if response.status_code == 201:
                    vm_id = response.json().get('vm_id')
                    return vm_id, duration, True
                else:
                    return None, duration, False
        
        def run_concurrent_creation():
            tasks = [create_vm(i) for i in range(10)]
            return asyncio.run(self._run_concurrent_tasks_with_args(tasks))
        
        results = benchmark(run_concurrent_creation)
        
        # Verify results
        successful_creations = [r for r in results if r[2]]
        durations = [r[1] for r in results]
        
        assert len(successful_creations) >= 8, "Too many VM creation failures"
        
        # Performance assertions
        avg_duration = statistics.mean(durations)
        assert avg_duration < 2.0, f"Average VM creation time too high: {avg_duration:.3f}s"
    
    @pytest.mark.asyncio
    async def test_concurrent_websocket_connections(self, benchmark):
        """Benchmark concurrent WebSocket connections"""
        
        # First create a test VM
        vm_id = await self._create_test_vm()
        
        async def websocket_connection_test():
            uri = f"{self.WS_BASE_URL}/api/v1/vms/{vm_id}/metrics/stream?vm_type=docker"
            
            start_time = time.time()
            try:
                async with websockets.connect(uri) as websocket:
                    # Receive a few messages
                    for _ in range(3):
                        message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                        data = json.loads(message)
                        assert 'cpu_percent' in data
                    
                    duration = time.time() - start_time
                    return True, duration
            except Exception as e:
                duration = time.time() - start_time
                return False, duration
        
        def run_concurrent_websockets():
            return asyncio.run(self._run_concurrent_tasks(websocket_connection_test, 5))
        
        results = benchmark(run_concurrent_websockets)
        
        # Verify results
        successful_connections = [r for r in results if r[0]]
        durations = [r[1] for r in results]
        
        assert len(successful_connections) >= 4, "Too many WebSocket connection failures"
        
        # Performance assertions
        avg_duration = statistics.mean(durations)
        assert avg_duration < 5.0, f"Average WebSocket connection time too high: {avg_duration:.3f}s"
    
    @pytest.mark.asyncio
    async def test_mixed_load_benchmark(self, benchmark):
        """Benchmark mixed API and WebSocket load"""
        
        vm_id = await self._create_test_vm()
        
        async def api_task():
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.API_BASE_URL}/api/v1/vms/")
                return response.status_code == 200
        
        async def websocket_task():
            uri = f"{self.WS_BASE_URL}/api/v1/vms/{vm_id}/metrics/stream?vm_type=docker"
            try:
                async with websockets.connect(uri) as websocket:
                    message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                    return json.loads(message) is not None
            except:
                return False
        
        def run_mixed_load():
            # Mix of API and WebSocket tasks
            tasks = []
            for i in range(15):
                if i % 3 == 0:
                    tasks.append(websocket_task())
                else:
                    tasks.append(api_task())
            
            return asyncio.run(asyncio.gather(*tasks))
        
        results = benchmark(run_mixed_load)
        
        # Verify results
        successful_tasks = sum(1 for result in results if result)
        success_rate = successful_tasks / len(results)
        
        assert success_rate >= 0.8, f"Success rate too low: {success_rate:.2%}"
    
    @pytest.mark.asyncio
    async def test_stress_test_api_endpoints(self, benchmark):
        """Stress test API endpoints with high concurrency"""
        
        endpoints = [
            "/health",
            "/api/v1/vms/",
            "/api/v1/vms/templates"
        ]
        
        async def stress_request(endpoint):
            async with httpx.AsyncClient() as client:
                try:
                    response = await client.get(f"{self.API_BASE_URL}{endpoint}")
                    return response.status_code, endpoint
                except Exception as e:
                    return 0, endpoint
        
        def run_stress_test():
            # Create 50 concurrent requests across different endpoints
            tasks = []
            for i in range(50):
                endpoint = endpoints[i % len(endpoints)]
                tasks.append(stress_request(endpoint))
            
            return asyncio.run(asyncio.gather(*tasks))
        
        results = benchmark(run_stress_test)
        
        # Analyze results by endpoint
        endpoint_stats = {}
        for status_code, endpoint in results:
            if endpoint not in endpoint_stats:
                endpoint_stats[endpoint] = {'success': 0, 'total': 0}
            
            endpoint_stats[endpoint]['total'] += 1
            if status_code == 200:
                endpoint_stats[endpoint]['success'] += 1
        
        # Verify performance
        for endpoint, stats in endpoint_stats.items():
            success_rate = stats['success'] / stats['total']
            assert success_rate >= 0.9, f"Endpoint {endpoint} success rate too low: {success_rate:.2%}"
    
    @pytest.mark.asyncio
    async def test_resource_utilization_benchmark(self, benchmark):
        """Benchmark resource utilization under load"""
        
        import psutil
        import os
        
        def measure_resources():
            process = psutil.Process(os.getpid())
            return {
                'cpu_percent': process.cpu_percent(),
                'memory_mb': process.memory_info().rss / 1024 / 1024,
                'open_files': len(process.open_files()),
                'connections': len(process.connections())
            }
        
        async def resource_intensive_task():
            # Create VM and establish WebSocket connection
            vm_data = {
                'name': f'resource-test-vm-{time.time()}',
                'template': 'ubuntu:20.04',
                'vm_type': 'docker',
                'memory_mb': 512,
                'cpus': 1,
                'domain': 'TurdParty'
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{self.API_BASE_URL}/api/v1/vms/", json=vm_data)
                if response.status_code == 201:
                    vm_id = response.json().get('vm_id')
                    
                    # Brief WebSocket connection
                    uri = f"{self.WS_BASE_URL}/api/v1/vms/{vm_id}/metrics/stream?vm_type=docker"
                    try:
                        async with websockets.connect(uri) as websocket:
                            await asyncio.wait_for(websocket.recv(), timeout=1.0)
                    except:
                        pass
                    
                    return True
            return False
        
        def run_resource_test():
            initial_resources = measure_resources()
            
            # Run multiple resource-intensive tasks
            tasks = [resource_intensive_task() for _ in range(8)]
            results = asyncio.run(asyncio.gather(*tasks))
            
            final_resources = measure_resources()
            
            return {
                'initial': initial_resources,
                'final': final_resources,
                'task_results': results
            }
        
        results = benchmark(run_resource_test)
        
        # Verify resource usage is reasonable
        memory_increase = results['final']['memory_mb'] - results['initial']['memory_mb']
        successful_tasks = sum(1 for r in results['task_results'] if r)
        
        assert memory_increase < 100, f"Memory increase too high: {memory_increase:.1f}MB"
        assert successful_tasks >= 6, f"Too many task failures: {successful_tasks}/8"
    
    # Helper methods
    async def _run_concurrent_tasks(self, task_func, count):
        """Run multiple instances of a task concurrently"""
        tasks = [task_func() for _ in range(count)]
        return await asyncio.gather(*tasks)
    
    async def _run_concurrent_tasks_with_args(self, tasks):
        """Run tasks with different arguments concurrently"""
        return await asyncio.gather(*tasks)
    
    async def _create_test_vm(self):
        """Create a test VM for benchmarking"""
        vm_data = {
            'name': f'benchmark-test-vm-{time.time()}',
            'template': 'ubuntu:20.04',
            'vm_type': 'docker',
            'memory_mb': 512,
            'cpus': 1,
            'domain': 'TurdParty'
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{self.API_BASE_URL}/api/v1/vms/", json=vm_data)
            if response.status_code == 201:
                return response.json().get('vm_id')
            else:
                raise Exception(f"Failed to create test VM: {response.status_code}")


@pytest.mark.benchmark
@pytest.mark.stress
class TestStressBenchmarks:
    """High-load stress tests"""
    
    @pytest.mark.asyncio
    async def test_extreme_concurrent_load(self, benchmark):
        """Test system under extreme concurrent load"""
        
        async def extreme_load_task():
            async with httpx.AsyncClient() as client:
                tasks = []
                for _ in range(100):  # 100 concurrent requests
                    task = client.get("http://localhost:8000/health")
                    tasks.append(task)
                
                responses = await asyncio.gather(*tasks, return_exceptions=True)
                successful = sum(1 for r in responses if hasattr(r, 'status_code') and r.status_code == 200)
                return successful, len(responses)
        
        def run_extreme_load():
            return asyncio.run(extreme_load_task())
        
        successful, total = benchmark(run_extreme_load)
        success_rate = successful / total
        
        assert success_rate >= 0.8, f"Success rate under extreme load too low: {success_rate:.2%}"
