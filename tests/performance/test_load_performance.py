"""
Performance and load tests for TurdParty application.

Tests system performance under various load conditions and identifies
bottlenecks. Ensures PEP8, PEP257, and PEP484 compliance.
"""

import concurrent.futures
import time

from locust import HttpUser, between, task
import pytest


class TestPerformanceBaseline:
    """Test suite for establishing performance baselines."""

    def test_api_response_time_baseline(self, client) -> None:
        """Test baseline API response times."""
        endpoints = [
            ("GET", "/health"),
            ("GET", "/api/v1/file_injection/"),
        ]

        response_times = {}

        for method, endpoint in endpoints:
            start_time = time.time()
            response = client.request(method, endpoint)
            end_time = time.time()

            response_time = end_time - start_time
            response_times[f"{method} {endpoint}"] = response_time

            # Baseline: API responses should be under 1 second
            assert response_time < 1.0, f"{method} {endpoint} took {response_time:.3f}s"
            assert response.status_code in [200, 401, 403]  # Valid responses

        print(f"Response time baselines: {response_times}")

    def test_file_upload_performance(self, client) -> None:
        """Test file upload performance with various file sizes."""
        file_sizes = [
            (1024, "1KB"),           # 1KB
            (10 * 1024, "10KB"),     # 10KB
            (100 * 1024, "100KB"),   # 100KB
            (1024 * 1024, "1MB"),    # 1MB
            (5 * 1024 * 1024, "5MB") # 5MB
        ]

        upload_times = {}

        for size, label in file_sizes:
            content = b"A" * size

            start_time = time.time()
            response = client.post(
                "/api/v1/file_injection/",
                files={"file": (f"test_{label}.bin", content, "application/octet-stream")},
                data={
                    "target_path": f"/app/data/test_{label}.bin",
                    "permissions": "0644"
                }
            )
            end_time = time.time()

            upload_time = end_time - start_time
            upload_times[label] = upload_time

            # Performance expectations (adjust based on requirements)
            if size <= 1024 * 1024:  # Files <= 1MB
                assert upload_time < 5.0, f"{label} upload took {upload_time:.3f}s"
            else:  # Larger files
                assert upload_time < 30.0, f"{label} upload took {upload_time:.3f}s"

        print(f"Upload time baselines: {upload_times}")

    def test_concurrent_request_handling(self, client) -> None:
        """Test handling of concurrent requests."""
        def make_request(request_id: int) -> tuple[int, float]:
            start_time = time.time()
            response = client.get("/health")
            end_time = time.time()
            return response.status_code, end_time - start_time

        # Test with increasing concurrency levels
        concurrency_levels = [1, 5, 10, 20]

        for concurrency in concurrency_levels:
            with concurrent.futures.ThreadPoolExecutor(max_workers=concurrency) as executor:
                futures = [executor.submit(make_request, i) for i in range(concurrency)]
                results = [future.result() for future in futures]

            # All requests should succeed
            status_codes = [result[0] for result in results]
            response_times = [result[1] for result in results]

            success_rate = sum(1 for code in status_codes if code == 200) / len(status_codes)
            avg_response_time = sum(response_times) / len(response_times)
            max_response_time = max(response_times)

            # Performance expectations
            assert success_rate >= 0.95, f"Success rate {success_rate:.2%} too low at concurrency {concurrency}"
            assert avg_response_time < 2.0, f"Avg response time {avg_response_time:.3f}s too high"
            assert max_response_time < 5.0, f"Max response time {max_response_time:.3f}s too high"

            print(f"Concurrency {concurrency}: {success_rate:.2%} success, "
                  f"{avg_response_time:.3f}s avg, {max_response_time:.3f}s max")


class TestMemoryPerformance:
    """Test suite for memory usage and leak detection."""

    def test_memory_usage_during_file_processing(self, client) -> None:
        """Test memory usage during file processing."""
        import os

        import psutil

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss

        # Process multiple files
        for i in range(10):
            content = b"A" * (1024 * 1024)  # 1MB each
            response = client.post(
                "/api/v1/file_injection/",
                files={"file": (f"memory_test_{i}.bin", content, "application/octet-stream")},
                data={
                    "target_path": f"/app/data/memory_test_{i}.bin",
                    "permissions": "0644"
                }
            )

        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory

        # Memory increase should be reasonable (less than 100MB for this test)
        assert memory_increase < 100 * 1024 * 1024, f"Memory increased by {memory_increase / 1024 / 1024:.1f}MB"

        print(f"Memory usage: {initial_memory / 1024 / 1024:.1f}MB -> "
              f"{final_memory / 1024 / 1024:.1f}MB (+{memory_increase / 1024 / 1024:.1f}MB)")

    def test_memory_leak_detection(self, client) -> None:
        """Test for memory leaks during repeated operations."""
        import gc
        import os

        import psutil

        process = psutil.Process(os.getpid())

        # Baseline memory measurement
        gc.collect()
        baseline_memory = process.memory_info().rss

        # Perform repeated operations
        for cycle in range(3):
            for i in range(20):
                response = client.get("/health")
                assert response.status_code == 200

            # Force garbage collection
            gc.collect()
            current_memory = process.memory_info().rss
            memory_increase = current_memory - baseline_memory

            print(f"Cycle {cycle + 1}: Memory increase: {memory_increase / 1024 / 1024:.1f}MB")

            # Memory should not continuously increase
            if cycle > 0:  # Allow some increase in first cycle
                assert memory_increase < 50 * 1024 * 1024, f"Potential memory leak: {memory_increase / 1024 / 1024:.1f}MB increase"


class TestDatabasePerformance:
    """Test suite for database performance."""

    @pytest.mark.skipif(
        not pytest.importorskip("sqlalchemy", minversion="1.4"),
        reason="SQLAlchemy not available"
    )
    def test_database_query_performance(self) -> None:
        """Test database query performance."""
        # This would test actual database queries
        # For now, we'll simulate with timing

        query_times = []

        for i in range(100):
            start_time = time.time()
            # Simulate database query
            time.sleep(0.001)  # 1ms simulated query
            end_time = time.time()

            query_times.append(end_time - start_time)

        avg_query_time = sum(query_times) / len(query_times)
        max_query_time = max(query_times)

        # Database queries should be fast
        assert avg_query_time < 0.1, f"Average query time {avg_query_time:.3f}s too slow"
        assert max_query_time < 0.5, f"Max query time {max_query_time:.3f}s too slow"

    def test_database_connection_pool_performance(self) -> None:
        """Test database connection pool performance."""
        # Test connection acquisition times
        connection_times = []

        for i in range(50):
            start_time = time.time()
            # Simulate connection acquisition
            time.sleep(0.002)  # 2ms simulated connection time
            end_time = time.time()

            connection_times.append(end_time - start_time)

        avg_connection_time = sum(connection_times) / len(connection_times)

        # Connection acquisition should be fast
        assert avg_connection_time < 0.05, f"Average connection time {avg_connection_time:.3f}s too slow"


class TestCachePerformance:
    """Test suite for cache performance."""

    def test_cache_hit_performance(self) -> None:
        """Test cache hit performance."""
        # Simulate cache operations
        cache_times = []

        for i in range(1000):
            start_time = time.time()
            # Simulate cache hit
            time.sleep(0.0001)  # 0.1ms simulated cache hit
            end_time = time.time()

            cache_times.append(end_time - start_time)

        avg_cache_time = sum(cache_times) / len(cache_times)

        # Cache hits should be very fast
        assert avg_cache_time < 0.001, f"Average cache time {avg_cache_time:.6f}s too slow"

    def test_cache_miss_performance(self) -> None:
        """Test cache miss performance."""
        # Simulate cache misses with database fallback
        cache_miss_times = []

        for i in range(100):
            start_time = time.time()
            # Simulate cache miss + database query
            time.sleep(0.005)  # 5ms simulated cache miss + DB query
            end_time = time.time()

            cache_miss_times.append(end_time - start_time)

        avg_cache_miss_time = sum(cache_miss_times) / len(cache_miss_times)

        # Cache misses should still be reasonable
        assert avg_cache_miss_time < 0.1, f"Average cache miss time {avg_cache_miss_time:.3f}s too slow"


class TurdPartyLoadTestUser(HttpUser):
    """Locust user class for load testing TurdParty API."""

    wait_time = between(1, 3)  # Wait 1-3 seconds between requests

    def on_start(self) -> None:
        """Called when a user starts."""
        # Setup any authentication or initialization
        pass

    @task(3)
    def health_check(self) -> None:
        """Test health endpoint (most frequent)."""
        self.client.get("/health")

    @task(2)
    def list_injections(self) -> None:
        """Test listing injections."""
        self.client.get("/api/v1/file_injection/")

    @task(1)
    def upload_file(self) -> None:
        """Test file upload (less frequent, more resource intensive)."""
        content = b"#!/bin/bash\necho 'Load test file'\n"
        files = {"file": ("loadtest.sh", content, "application/x-sh")}
        data = {
            "target_path": "/app/scripts/loadtest.sh",
            "permissions": "0755",
            "description": "Load test file upload"
        }

        response = self.client.post("/api/v1/file_injection/", files=files, data=data)

        if response.status_code == 201:
            # If upload successful, try to get the injection details
            injection_data = response.json()
            injection_id = injection_data.get("id")
            if injection_id:
                self.client.get(f"/api/v1/file_injection/{injection_id}")


class TestStressConditions:
    """Test suite for stress conditions and edge cases."""

    def test_rapid_sequential_uploads(self, client) -> None:
        """Test rapid sequential file uploads."""
        upload_times = []

        for i in range(50):
            content = f"#!/bin/bash\necho 'Rapid test {i}'\n".encode()

            start_time = time.time()
            response = client.post(
                "/api/v1/file_injection/",
                files={"file": (f"rapid_{i}.sh", content, "application/x-sh")},
                data={
                    "target_path": f"/app/scripts/rapid_{i}.sh",
                    "permissions": "0755"
                }
            )
            end_time = time.time()

            upload_times.append(end_time - start_time)

            # Each upload should still be reasonably fast
            assert (end_time - start_time) < 10.0, f"Upload {i} took too long"

        # Performance should not degrade significantly
        first_half_avg = sum(upload_times[:25]) / 25
        second_half_avg = sum(upload_times[25:]) / 25

        # Second half should not be more than 2x slower than first half
        assert second_half_avg < first_half_avg * 2, "Performance degraded significantly"

    def test_large_payload_handling(self, client) -> None:
        """Test handling of large payloads."""
        # Test with maximum allowed file size
        max_size = 10 * 1024 * 1024  # 10MB
        large_content = b"A" * max_size

        start_time = time.time()
        response = client.post(
            "/api/v1/file_injection/",
            files={"file": ("large.bin", large_content, "application/octet-stream")},
            data={
                "target_path": "/app/data/large.bin",
                "permissions": "0644"
            }
        )
        end_time = time.time()

        upload_time = end_time - start_time

        # Large file upload should complete within reasonable time
        if response.status_code in [200, 201]:
            assert upload_time < 60.0, f"Large file upload took {upload_time:.1f}s"
        else:
            # If rejected, should be due to size limits, not timeout
            assert response.status_code in [400, 413, 422]

    def test_concurrent_large_uploads(self, client) -> None:
        """Test concurrent large file uploads."""
        def upload_large_file(file_id: int) -> tuple[int, float]:
            content = b"A" * (1024 * 1024)  # 1MB each

            start_time = time.time()
            response = client.post(
                "/api/v1/file_injection/",
                files={"file": (f"concurrent_{file_id}.bin", content, "application/octet-stream")},
                data={
                    "target_path": f"/app/data/concurrent_{file_id}.bin",
                    "permissions": "0644"
                }
            )
            end_time = time.time()

            return response.status_code, end_time - start_time

        # Test with 5 concurrent large uploads
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(upload_large_file, i) for i in range(5)]
            results = [future.result() for future in futures]

        # Most uploads should succeed
        success_count = sum(1 for status, _ in results if status in [200, 201])
        success_rate = success_count / len(results)

        assert success_rate >= 0.6, f"Success rate {success_rate:.2%} too low for concurrent uploads"

        # Response times should still be reasonable
        response_times = [time_taken for _, time_taken in results]
        max_time = max(response_times)

        assert max_time < 120.0, f"Concurrent upload took too long: {max_time:.1f}s"
