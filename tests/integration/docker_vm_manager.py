"""
Docker-based VM Manager for TurdParty Integration Tests

This module provides Docker container-based "VMs" as a fallback when
VirtualBox/Vagrant cannot be used due to KVM conflicts or other issues.
"""

import time
import uuid
import subprocess
from pathlib import Path
from typing import Dict, List
import json


class DockerVMManager:
    """Manages Docker containers as VM substitutes for testing."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.vm_timeout = int(config.get('VM_CAPTURE_TIMEOUT_MINUTES', 5)) * 60
        self.boot_timeout = int(config.get('VM_BOOT_TIMEOUT_MINUTES', 10)) * 60
        self.active_containers: List[str] = []
        
    def create_ubuntu_vm(self, vm_name: str) -> Dict:
        """Create and start Ubuntu container."""
        container_name = f"turdparty-test-{vm_name}"
        
        # Ubuntu container configuration (allow network for setup, then isolate)
        docker_cmd = [
            'docker', 'run', '-d',
            '--name', container_name,
            '--hostname', vm_name,
            '--security-opt', 'no-new-privileges:true',  # Security
            '--tmpfs', '/tmp:rw,noexec,nosuid,size=1g',  # Writable tmp
            '--tmpfs', '/var/tmp:rw,noexec,nosuid,size=500m',
            '--tmpfs', '/var/lib/apt/lists:rw,size=100m',  # Writable apt cache
            '--tmpfs', '/var/cache/apt:rw,size=200m',  # Writable apt cache
            '--memory', f"{self.config.get('VBOX_MEMORY_MB', 2048)}m",
            '--cpus', str(self.config.get('VBOX_CPUS', 2)),
            'ubuntu:20.04',
            'sleep', '3600'  # Keep container running
        ]
        
        start_time = time.time()
        
        try:
            # Start container
            result = subprocess.run(
                docker_cmd,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode != 0:
                raise Exception(f"Failed to start container: {result.stderr}")
            
            container_id = result.stdout.strip()
            
            # Install basic tools
            setup_cmd = [
                'docker', 'exec', container_name,
                'bash', '-c',
                'apt-get update && apt-get install -y curl wget unzip dpkg && mkdir -p /tmp/turdparty-test'
            ]
            
            setup_result = subprocess.run(
                setup_cmd,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            if setup_result.returncode != 0:
                # Cleanup on failure
                subprocess.run(['docker', 'rm', '-f', container_name], capture_output=True)
                raise Exception(f"Failed to setup container: {setup_result.stderr}")

            # Isolate network after setup (if configured)
            if self.config.get('DISABLE_VM_NETWORKING', True):
                self._isolate_container_network(container_name)

            boot_time = time.time() - start_time
            self.active_containers.append(container_name)
            
            return {
                'vm_name': vm_name,
                'container_name': container_name,
                'container_id': container_id,
                'boot_time_seconds': boot_time,
                'status': 'running',
                'config': {
                    'name': vm_name,
                    'platform': 'ubuntu',
                    'memory': self.config.get('VBOX_MEMORY_MB', 2048),
                    'cpus': self.config.get('VBOX_CPUS', 2)
                }
            }
            
        except Exception as e:
            # Cleanup on failure
            subprocess.run(['docker', 'rm', '-f', container_name], capture_output=True)
            raise e
    
    def create_windows_vm(self, vm_name: str) -> Dict:
        """Create and start Windows container (using Wine for Windows app simulation)."""
        container_name = f"turdparty-test-{vm_name}"
        
        # Windows simulation using Wine in Ubuntu (allow network for setup)
        docker_cmd = [
            'docker', 'run', '-d',
            '--name', container_name,
            '--hostname', vm_name,
            '--security-opt', 'no-new-privileges:true',  # Security
            '--tmpfs', '/tmp:rw,noexec,nosuid,size=1g',  # Writable tmp
            '--tmpfs', '/var/tmp:rw,noexec,nosuid,size=500m',
            '--tmpfs', '/var/lib/apt/lists:rw,size=100m',  # Writable apt cache
            '--tmpfs', '/var/cache/apt:rw,size=200m',  # Writable apt cache
            '--memory', f"{self.config.get('VBOX_MEMORY_MB', 2048)}m",
            '--cpus', str(self.config.get('VBOX_CPUS', 2)),
            'ubuntu:20.04',
            'sleep', '3600'  # Keep container running
        ]
        
        start_time = time.time()
        
        try:
            # Start container
            result = subprocess.run(
                docker_cmd,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode != 0:
                raise Exception(f"Failed to start container: {result.stderr}")
            
            container_id = result.stdout.strip()
            
            # Install basic tools for Windows simulation (skip Wine for simplicity)
            setup_cmd = [
                'docker', 'exec', container_name,
                'bash', '-c',
                'apt-get update && apt-get install -y curl wget file unzip && mkdir -p /tmp/turdparty-test'
            ]

            setup_result = subprocess.run(
                setup_cmd,
                capture_output=True,
                text=True,
                timeout=300
            )

            if setup_result.returncode != 0:
                subprocess.run(['docker', 'rm', '-f', container_name], capture_output=True)
                raise Exception(f"Failed to setup container: {setup_result.stderr}")

            # Isolate network after setup (if configured)
            if self.config.get('DISABLE_VM_NETWORKING', True):
                self._isolate_container_network(container_name)

            boot_time = time.time() - start_time
            self.active_containers.append(container_name)
            
            return {
                'vm_name': vm_name,
                'container_name': container_name,
                'container_id': container_id,
                'boot_time_seconds': boot_time,
                'status': 'running',
                'config': {
                    'name': vm_name,
                    'platform': 'windows',
                    'memory': self.config.get('VBOX_MEMORY_MB', 2048),
                    'cpus': self.config.get('VBOX_CPUS', 2)
                }
            }
            
        except Exception as e:
            # Cleanup on failure
            subprocess.run(['docker', 'rm', '-f', container_name], capture_output=True)
            raise e
    
    def inject_file_to_vm(self, vm_name: str, file_path: Path, _target_path: str) -> Dict:
        """Inject file into container."""
        container_name = f"turdparty-test-{vm_name}"

        start_time = time.time()

        # Always copy to /tmp in container regardless of target_path
        container_target = f"/tmp/{file_path.name}"

        # Copy file to container
        copy_cmd = [
            'docker', 'cp', str(file_path), f"{container_name}:{container_target}"
        ]
        
        result = subprocess.run(
            copy_cmd,
            capture_output=True,
            text=True,
            timeout=300
        )
        
        if result.returncode != 0:
            raise Exception(f"Failed to copy file to container {vm_name}: {result.stderr}")
        
        injection_time = time.time() - start_time
        
        return {
            'vm_name': vm_name,
            'container_name': container_name,
            'file_injected': str(file_path),
            'target_path': container_target,  # Use actual target path
            'injection_time_seconds': injection_time,
            'injection_timestamp': time.time()
        }
    
    def install_application_in_vm(self, vm_name: str, app_config: Dict, _file_path: Path) -> Dict:
        """Install application in container and capture footprint."""
        container_name = f"turdparty-test-{vm_name}"
        
        # Prepare installation command
        install_cmd = app_config.get('install_command', '')
        if not install_cmd:
            raise ValueError(f"No install command for {app_config['name']}")
        
        # Adapt command for container environment and fix file paths
        if 'windows' in vm_name.lower():
            # For Windows apps, just analyze the file instead of installing
            filename = app_config.get('filename', 'unknown')
            # Files are copied to /tmp, not C:\temp
            install_cmd = f"ls -la /tmp/ && file /tmp/'{filename}' && echo 'Windows app analysis completed'"
        else:
            # For Ubuntu apps, ensure we're looking in the right place
            filename = app_config.get('filename', 'unknown')
            # Fix the path to where the file actually is
            install_cmd = install_cmd.replace(filename, f"'/tmp/{filename}'")
        
        # Execute installation
        start_time = time.time()
        
        # Run installation command in container
        docker_cmd = [
            'docker', 'exec', container_name,
            'bash', '-c', f"cd /tmp && {install_cmd}"
        ]
        
        result = subprocess.run(
            docker_cmd,
            capture_output=True,
            text=True,
            timeout=self.vm_timeout
        )
        
        install_time = time.time() - start_time
        
        # Capture installation footprint
        footprint = self._capture_installation_footprint(vm_name, app_config)
        
        return {
            'vm_name': vm_name,
            'container_name': container_name,
            'app_name': app_config['name'],
            'install_time_seconds': install_time,
            'install_success': result.returncode == 0,
            'install_output': result.stdout,
            'install_errors': result.stderr,
            'footprint': footprint,
            'timestamp': time.time()
        }
    
    def _capture_installation_footprint(self, vm_name: str, _app_config: Dict) -> Dict:
        """Capture installation footprint in container."""
        container_name = f"turdparty-test-{vm_name}"
        
        # Capture file system changes
        fs_cmd = [
            'docker', 'exec', container_name,
            'bash', '-c',
            "find /opt /usr/local /usr/bin /usr/share /tmp -type f 2>/dev/null | head -100"
        ]
        
        fs_result = subprocess.run(
            fs_cmd,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        # Capture process information
        proc_cmd = [
            'docker', 'exec', container_name,
            'bash', '-c',
            "ps aux | grep -v grep | grep -E '(python|node|firefox|chrome|wine)' || echo 'No matching processes'"
        ]
        
        proc_result = subprocess.run(
            proc_cmd,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        return {
            'filesystem_changes': fs_result.stdout.strip().split('\n') if fs_result.stdout.strip() else [],
            'running_processes': proc_result.stdout.strip().split('\n') if proc_result.stdout.strip() else [],
            'capture_timestamp': time.time()
        }
    
    def destroy_vm(self, vm_name: str) -> bool:
        """Destroy container and cleanup."""
        container_name = f"turdparty-test-{vm_name}"
        
        try:
            # Stop and remove container
            subprocess.run(['docker', 'stop', container_name], capture_output=True, timeout=30)
            result = subprocess.run(['docker', 'rm', '-f', container_name], capture_output=True, timeout=30)
            
            if container_name in self.active_containers:
                self.active_containers.remove(container_name)
            
            return result.returncode == 0
            
        except Exception as e:
            print(f"Warning: Failed to destroy container {container_name}: {e}")
            return False
    
    def cleanup_all_vms(self):
        """Cleanup all active containers."""
        for container_name in self.active_containers.copy():
            vm_name = container_name.replace('turdparty-test-', '')
            self.destroy_vm(vm_name)
    
    def _isolate_container_network(self, container_name: str):
        """Isolate container network after setup."""
        try:
            # Disconnect from default bridge network
            subprocess.run(
                ['docker', 'network', 'disconnect', 'bridge', container_name],
                capture_output=True,
                timeout=30
            )
        except Exception:
            # Network isolation is best-effort
            pass

    def check_docker_availability(self) -> bool:
        """Check if Docker is available and working."""
        try:
            result = subprocess.run(['docker', 'version'], capture_output=True, text=True, timeout=10)
            return result.returncode == 0
        except Exception:
            return False
