"""
TurdParty Integration Test Setup Verification

This module verifies that all components required for integration testing
are properly configured and available.
"""

import json
import os
import subprocess
from pathlib import Path
from typing import Dict

import pytest

from tests.conftest import load_test_config


class TestSetupVerification:
    """Verify integration test setup."""
    
    def test_load_test_config(self):
        """Test that test configuration loads correctly."""
        config = load_test_config()
        
        # Verify required configuration keys exist
        required_keys = [
            'TEST_MODE',
            'MINIO_ENDPOINT',
            'MINIO_ACCESS_KEY',
            'MINIO_SECRET_KEY',
            'ELASTICSEARCH_URL',
            'VAGRANT_GRPC_PORT',
            'VM_CAPTURE_TIMEOUT_MINUTES',
            'TEST_DOWNLOAD_DIR',
            'TEST_APPS_JSON',
            'TEST_BENCHMARK_FILE'
        ]
        
        for key in required_keys:
            assert key in config, f"Missing required config key: {key}"
        
        # Verify data types
        assert isinstance(config['TEST_MODE'], bool)
        assert isinstance(config['VAGRANT_GRPC_PORT'], int)
        assert isinstance(config['VM_CAPTURE_TIMEOUT_MINUTES'], int)
        assert isinstance(config['VBOX_MEMORY_MB'], int)
        assert isinstance(config['VBOX_CPUS'], int)
    
    def test_test_applications_json_exists(self):
        """Test that test applications JSON file exists and is valid."""
        apps_file = Path(__file__).parent.parent / 'data' / 'test_applications.json'
        
        assert apps_file.exists(), f"Test applications file not found: {apps_file}"
        
        with open(apps_file, 'r') as f:
            apps_data = json.load(f)
        
        # Verify structure
        assert 'linux_applications' in apps_data
        assert 'windows_applications' in apps_data
        assert 'metadata' in apps_data
        
        # Verify Linux applications
        linux_apps = apps_data['linux_applications']
        assert 'deb_packages' in linux_apps
        assert 'appimage_packages' in linux_apps
        assert 'tar_gz_packages' in linux_apps
        assert 'custom_choice' in linux_apps
        
        # Verify Windows applications
        windows_apps = apps_data['windows_applications']
        assert 'exe_packages' in windows_apps
        assert 'msi_packages' in windows_apps
        
        # Verify we have the expected number of applications
        assert len(linux_apps['deb_packages']) == 3
        assert len(linux_apps['appimage_packages']) == 3
        assert len(linux_apps['tar_gz_packages']) == 3
        assert len(windows_apps['exe_packages']) == 5
        assert len(windows_apps['msi_packages']) == 5
        
        # Verify each application has required fields
        for category in ['deb_packages', 'appimage_packages', 'tar_gz_packages']:
            for app in linux_apps[category]:
                assert 'name' in app
                assert 'description' in app
                assert 'url' in app
                assert 'filename' in app
                assert 'type' in app
                assert 'install_command' in app
        
        for category in ['exe_packages', 'msi_packages']:
            for app in windows_apps[category]:
                assert 'name' in app
                assert 'description' in app
                assert 'url' in app
                assert 'filename' in app
                assert 'type' in app
                assert 'install_command' in app
    
    def test_test_environment_file_exists(self):
        """Test that test environment file exists."""
        env_file = Path(__file__).parent / '.env.test'
        
        assert env_file.exists(), f"Test environment file not found: {env_file}"
        
        # Verify it contains required variables
        with open(env_file, 'r') as f:
            content = f.read()
        
        required_vars = [
            'TEST_MODE=true',
            'MINIO_ENDPOINT=',
            'ELASTICSEARCH_URL=',
            'VAGRANT_GRPC_PORT=',
            'VM_CAPTURE_TIMEOUT_MINUTES='
        ]
        
        for var in required_vars:
            assert var in content, f"Missing environment variable: {var}"
    
    def test_test_data_directory_structure(self):
        """Test that test data directory structure is correct."""
        test_data_dir = Path(__file__).parent.parent / 'data'
        
        assert test_data_dir.exists(), "Test data directory not found"
        assert test_data_dir.is_dir(), "Test data path is not a directory"
        
        # Check for required files
        required_files = [
            'test_applications.json'
        ]
        
        for filename in required_files:
            file_path = test_data_dir / filename
            assert file_path.exists(), f"Required test data file not found: {filename}"
    
    def test_integration_test_directory_structure(self):
        """Test that integration test directory structure is correct."""
        integration_dir = Path(__file__).parent
        
        assert integration_dir.exists(), "Integration test directory not found"
        assert integration_dir.is_dir(), "Integration test path is not a directory"
        
        # Check for required files
        required_files = [
            '.env.test',
            'test_app_downloader.py',
            'test_vm_injection.py',
            'test_benchmark_persistence.py',
            'run_integration_tests.py'
        ]
        
        for filename in required_files:
            file_path = integration_dir / filename
            assert file_path.exists(), f"Required integration test file not found: {filename}"
    
    def test_vagrant_availability(self):
        """Test that Vagrant is available for VM testing."""
        try:
            result = subprocess.run(
                ['vagrant', '--version'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            assert result.returncode == 0, f"Vagrant command failed: {result.stderr}"
            assert 'Vagrant' in result.stdout, f"Unexpected Vagrant output: {result.stdout}"
            
        except FileNotFoundError:
            pytest.skip("Vagrant not installed - VM tests will be skipped")
        except subprocess.TimeoutExpired:
            pytest.fail("Vagrant command timed out")
    
    def test_virtualbox_availability(self):
        """Test that VirtualBox is available for VM testing."""
        try:
            result = subprocess.run(
                ['VBoxManage', '--version'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            assert result.returncode == 0, f"VBoxManage command failed: {result.stderr}"
            
        except FileNotFoundError:
            pytest.skip("VirtualBox not installed - VM tests will be skipped")
        except subprocess.TimeoutExpired:
            pytest.fail("VBoxManage command timed out")
    
    def test_python_dependencies_available(self):
        """Test that required Python dependencies are available."""
        required_packages = [
            'httpx',
            'pytest',
            'blake3'
        ]
        
        optional_packages = [
            'minio',
            'elasticsearch'
        ]
        
        # Test required packages
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                pytest.fail(f"Required package not available: {package}")
        
        # Test optional packages (warn if missing)
        missing_optional = []
        for package in optional_packages:
            try:
                __import__(package)
            except ImportError:
                missing_optional.append(package)
        
        if missing_optional:
            print(f"Warning: Optional packages not available: {missing_optional}")
            print("Some integration tests may be skipped")
    
    def test_download_directory_creation(self):
        """Test that download directory can be created."""
        config = load_test_config()
        download_dir = Path(config['TEST_DOWNLOAD_DIR'])
        
        # Create directory if it doesn't exist
        download_dir.mkdir(parents=True, exist_ok=True)
        
        assert download_dir.exists(), "Could not create download directory"
        assert download_dir.is_dir(), "Download path is not a directory"
        
        # Test write permissions
        test_file = download_dir / 'test_write_permissions.txt'
        try:
            test_file.write_text('test')
            test_file.unlink()
        except Exception as e:
            pytest.fail(f"Cannot write to download directory: {e}")
    
    def test_benchmark_file_initialization(self):
        """Test that benchmark file can be initialized."""
        config = load_test_config()
        benchmark_file = Path(config['TEST_BENCHMARK_FILE'])
        
        # Create parent directory if needed
        benchmark_file.parent.mkdir(parents=True, exist_ok=True)
        
        # Test that we can write to the benchmark file location
        try:
            test_data = {'test': 'data'}
            with open(benchmark_file, 'w') as f:
                json.dump(test_data, f)
            
            # Verify we can read it back
            with open(benchmark_file, 'r') as f:
                loaded_data = json.load(f)
            
            assert loaded_data == test_data
            
            # Clean up
            benchmark_file.unlink()
            
        except Exception as e:
            pytest.fail(f"Cannot initialize benchmark file: {e}")
    
    def test_environment_variables_loaded(self):
        """Test that environment variables are properly loaded."""
        config = load_test_config()
        
        # Test that config values are properly typed
        assert isinstance(config['TEST_MODE'], bool)
        assert config['TEST_MODE'] == True  # Should be True in test environment
        
        # Test that MinIO configuration is present
        assert config['MINIO_ENDPOINT']
        assert config['MINIO_ACCESS_KEY']
        assert config['MINIO_SECRET_KEY']
        
        # Test that VM configuration is present
        assert config['VAGRANT_GRPC_PORT'] > 0
        assert config['VM_CAPTURE_TIMEOUT_MINUTES'] > 0
        assert config['VBOX_MEMORY_MB'] > 0
        assert config['VBOX_CPUS'] > 0
    
    def test_security_configuration(self):
        """Test that security configuration is properly set."""
        config = load_test_config()
        
        # Verify security settings are enabled
        assert config['DISABLE_VM_NETWORKING'] == True
        assert config['DISABLE_VM_SHARED_FOLDERS'] == True
        assert config['ENABLE_VM_ISOLATION'] == True
        assert config['SANDBOX_MODE'] == True
        
        # Verify VM network and mounts are disabled
        assert config['VM_NETWORK_DISABLED'] == True
        assert config['VM_MOUNTS_DISABLED'] == True
