#!/usr/bin/env python3
"""
TurdParty Integration Test Runner

This script orchestrates the complete integration test suite including:
- Application downloads
- MinIO bucket operations
- VM injection and installation
- ELK data capture
- Benchmark persistence
"""

import asyncio
import json
import os
import sys
import time
from pathlib import Path
from typing import Dict, List

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from tests.conftest import load_test_config
from tests.integration.test_app_downloader import ApplicationDownloader, MinIOTestManager
from tests.integration.test_vm_injection import VagrantVMManager, ELKDataManager
from tests.integration.test_benchmark_persistence import BenchmarkManager


class IntegrationTestOrchestrator:
    """Orchestrates complete integration test workflow."""
    
    def __init__(self):
        self.config = load_test_config()
        self.downloader = ApplicationDownloader(self.config)
        self.minio_manager = MinIOTestManager(self.config)
        self.vm_manager = VagrantVMManager(self.config)
        self.elk_manager = ELKDataManager(self.config)
        self.benchmark_manager = BenchmarkManager(self.config)
        
        # Load test applications
        apps_file = Path(__file__).parent.parent / 'data' / 'test_applications.json'
        with open(apps_file, 'r') as f:
            self.test_apps = json.load(f)
    
    async def run_complete_test_suite(self):
        """Run the complete integration test suite."""
        print("🚀 Starting TurdParty Integration Test Suite")
        print("=" * 50)
        
        try:
            # Pre-flight checks
            await self._preflight_checks()
            
            # Run Ubuntu tests (2 applications)
            ubuntu_results = await self._run_ubuntu_tests()
            
            # Run Windows tests (2 applications)
            windows_results = await self._run_windows_tests()
            
            # Generate final report
            await self._generate_final_report(ubuntu_results, windows_results)
            
        except Exception as e:
            print(f"❌ Test suite failed: {e}")
            raise
        finally:
            # Cleanup
            await self._cleanup()
    
    async def _preflight_checks(self):
        """Perform pre-flight checks."""
        print("🔍 Performing pre-flight checks...")
        
        # Check MinIO connectivity
        try:
            bucket_name = self.minio_manager.create_test_bucket()
            self.minio_manager.cleanup_test_buckets()
            print("✅ MinIO connectivity verified")
        except Exception as e:
            raise Exception(f"MinIO connectivity failed: {e}")
        
        # Check ELK connectivity
        try:
            test_data = {'test': 'preflight', 'timestamp': time.time()}
            doc_id = self.elk_manager.store_test_data('preflight', test_data)
            print("✅ ELK connectivity verified")
        except Exception as e:
            print(f"⚠️ ELK connectivity warning: {e}")
        
        # Check Vagrant availability
        import subprocess
        try:
            result = subprocess.run(['vagrant', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Vagrant availability verified")
            else:
                raise Exception("Vagrant not available")
        except Exception as e:
            raise Exception(f"Vagrant check failed: {e}")
        
        print("✅ All pre-flight checks passed")
    
    async def _run_ubuntu_tests(self) -> List[Dict]:
        """Run Ubuntu VM tests."""
        print("\n🐧 Running Ubuntu VM Tests")
        print("-" * 30)
        
        results = []
        
        # Select 2 Ubuntu applications
        ubuntu_apps = [
            self.test_apps['linux_applications']['deb_packages'][0],  # Firefox
            self.test_apps['linux_applications']['tar_gz_packages'][0]  # Discord
        ]
        
        for i, app in enumerate(ubuntu_apps, 1):
            print(f"\n📦 Testing Ubuntu Application {i}/2: {app['name']}")
            
            try:
                result = await self._run_single_vm_test('ubuntu', app)
                results.append(result)
                print(f"✅ Ubuntu {app['name']} test completed successfully")
                
            except Exception as e:
                print(f"❌ Ubuntu {app['name']} test failed: {e}")
                results.append({
                    'app': app,
                    'platform': 'ubuntu',
                    'success': False,
                    'error': str(e)
                })
        
        return results
    
    async def _run_windows_tests(self) -> List[Dict]:
        """Run Windows VM tests."""
        print("\n🪟 Running Windows VM Tests")
        print("-" * 30)
        
        results = []
        
        # Select 2 Windows applications
        windows_apps = [
            self.test_apps['windows_applications']['exe_packages'][0],  # Firefox
            self.test_apps['windows_applications']['msi_packages'][0]   # Python
        ]
        
        for i, app in enumerate(windows_apps, 1):
            print(f"\n📦 Testing Windows Application {i}/2: {app['name']}")
            
            try:
                result = await self._run_single_vm_test('windows', app)
                results.append(result)
                print(f"✅ Windows {app['name']} test completed successfully")
                
            except Exception as e:
                print(f"❌ Windows {app['name']} test failed: {e}")
                results.append({
                    'app': app,
                    'platform': 'windows',
                    'success': False,
                    'error': str(e)
                })
        
        return results
    
    async def _run_single_vm_test(self, platform: str, app: Dict) -> Dict:
        """Run a single VM test."""
        test_start_time = time.time()
        
        # Step 1: Download application
        print(f"  📥 Downloading {app['name']}...")
        download_start = time.time()
        file_path, metadata = await self.downloader.download_file(
            app['url'], app['filename']
        )
        download_time = time.time() - download_start
        print(f"  ✅ Downloaded in {download_time:.2f}s ({metadata['file_size']} bytes)")
        
        # Record download benchmark
        self.benchmark_manager.record_download_benchmark(
            app['name'], metadata['file_size'], download_time
        )
        
        # Step 2: Upload to MinIO
        print(f"  📤 Uploading to MinIO...")
        upload_start = time.time()
        bucket_name = self.minio_manager.create_test_bucket()
        upload_result = self.minio_manager.upload_file_with_metadata(
            bucket_name, file_path, metadata
        )
        upload_time = time.time() - upload_start
        print(f"  ✅ Uploaded in {upload_time:.2f}s")
        
        # Record upload benchmark
        self.benchmark_manager.record_upload_benchmark(
            app['name'], metadata['file_size'], upload_time
        )
        
        # Step 3: Create and boot VM
        print(f"  🖥️ Creating {platform} VM...")
        vm_name = f"{platform}-{app['name']}-{int(time.time())}"
        
        if platform == 'ubuntu':
            vm_info = self.vm_manager.create_ubuntu_vm(vm_name)
        else:
            vm_info = self.vm_manager.create_windows_vm(vm_name)
        
        print(f"  ✅ VM booted in {vm_info['boot_time_seconds']:.2f}s")
        
        # Step 4: Inject file to VM
        print(f"  💉 Injecting file to VM...")
        target_path = f"/tmp/{file_path.name}" if platform == 'ubuntu' else f"C:\\temp\\{file_path.name}"
        injection_result = self.vm_manager.inject_file_to_vm(vm_name, file_path, target_path)
        print(f"  ✅ File injected in {injection_result['injection_time_seconds']:.2f}s")
        
        # Step 5: Install application with 5-minute timeout
        print(f"  ⚙️ Installing application (5-minute timeout)...")
        install_start = time.time()
        
        try:
            install_result = self.vm_manager.install_application_in_vm(vm_name, app, file_path)
            install_success = install_result['install_success']
            footprint_size = len(install_result['footprint']['filesystem_changes'])
            
            print(f"  ✅ Installation {'succeeded' if install_success else 'failed'} in {install_result['install_time_seconds']:.2f}s")
            print(f"  📊 Footprint: {footprint_size} filesystem changes")
            
        except Exception as e:
            install_result = {
                'install_success': False,
                'install_time_seconds': time.time() - install_start,
                'install_errors': str(e),
                'footprint': {'filesystem_changes': [], 'running_processes': []}
            }
            install_success = False
            footprint_size = 0
            print(f"  ❌ Installation failed: {e}")
        
        # Record VM benchmarks
        self.benchmark_manager.record_vm_benchmark(
            platform=platform,
            boot_time=vm_info['boot_time_seconds'],
            injection_time=injection_result['injection_time_seconds'],
            install_time=install_result['install_time_seconds'],
            install_success=install_success,
            footprint_size=footprint_size
        )
        
        # Record application-specific benchmark
        self.benchmark_manager.record_application_benchmark(
            app_name=app['name'],
            platform=platform,
            install_time=install_result['install_time_seconds'],
            install_success=install_success,
            footprint_data=install_result['footprint']
        )
        
        # Step 6: Store data in ELK (optional)
        print(f"  📊 Storing data in ELK...")
        test_data = {
            'platform': platform,
            'application': app,
            'download_metadata': metadata,
            'minio_upload': upload_result,
            'vm_info': vm_info,
            'injection_result': injection_result,
            'install_result': install_result,
            'total_test_time': time.time() - test_start_time
        }

        # Convert HTTPHeaderDict to regular dict for JSON serialization
        if 'minio_upload' in test_data and 'metadata' in test_data['minio_upload']:
            test_data['minio_upload']['metadata'] = dict(test_data['minio_upload']['metadata'])

        try:
            elk_doc_id = self.elk_manager.store_test_data('vm-injection', test_data)
            print(f"  ✅ Data stored in ELK (doc: {elk_doc_id[:8]}...)")
        except Exception as e:
            elk_doc_id = f"elk-unavailable-{int(time.time())}"
            print(f"  ⚠️ ELK unavailable, data stored locally: {e}")
            # Store data locally as backup
            backup_file = Path(f"/tmp/turdparty-test-data-{elk_doc_id}.json")
            with open(backup_file, 'w') as f:
                json.dump(test_data, f, indent=2, default=str)
            print(f"  📁 Test data backed up to: {backup_file}")
        
        # Step 7: Cleanup VM
        print(f"  🧹 Cleaning up VM...")
        self.vm_manager.destroy_vm(vm_name)
        print(f"  ✅ VM destroyed")
        
        return {
            'app': app,
            'platform': platform,
            'success': True,
            'download_time': download_time,
            'upload_time': upload_time,
            'vm_boot_time': vm_info['boot_time_seconds'],
            'injection_time': injection_result['injection_time_seconds'],
            'install_time': install_result['install_time_seconds'],
            'install_success': install_success,
            'footprint_size': footprint_size,
            'total_time': time.time() - test_start_time,
            'elk_doc_id': elk_doc_id
        }
    
    async def _generate_final_report(self, ubuntu_results: List[Dict], windows_results: List[Dict]):
        """Generate final test report."""
        print("\n📊 Final Test Report")
        print("=" * 50)
        
        all_results = ubuntu_results + windows_results
        successful_tests = [r for r in all_results if r.get('success', False)]
        failed_tests = [r for r in all_results if not r.get('success', False)]
        
        print(f"Total Tests: {len(all_results)}")
        print(f"Successful: {len(successful_tests)}")
        print(f"Failed: {len(failed_tests)}")
        print(f"Success Rate: {len(successful_tests)/len(all_results)*100:.1f}%")
        
        if successful_tests:
            print("\n⏱️ Performance Summary:")
            avg_download = sum(r['download_time'] for r in successful_tests) / len(successful_tests)
            avg_upload = sum(r['upload_time'] for r in successful_tests) / len(successful_tests)
            avg_boot = sum(r['vm_boot_time'] for r in successful_tests) / len(successful_tests)
            avg_install = sum(r['install_time'] for r in successful_tests) / len(successful_tests)
            
            print(f"  Average Download Time: {avg_download:.2f}s")
            print(f"  Average Upload Time: {avg_upload:.2f}s")
            print(f"  Average VM Boot Time: {avg_boot:.2f}s")
            print(f"  Average Install Time: {avg_install:.2f}s")
        
        # Get benchmark statistics
        stats = self.benchmark_manager.get_benchmark_statistics()
        if stats:
            print("\n📈 Benchmark Statistics:")
            for category, data in stats.items():
                if isinstance(data, dict) and 'mean_seconds' in data:
                    print(f"  {category.title()}: {data['mean_seconds']:.2f}s avg ({data['count']} samples)")
        
        # Application performance ranking
        rankings = self.benchmark_manager.get_application_performance_ranking()
        if rankings:
            print("\n🏆 Application Performance Ranking:")
            for i, app in enumerate(rankings[:5], 1):  # Top 5
                print(f"  {i}. {app['app_name']}")
                if app['ubuntu_mean_time']:
                    print(f"     Ubuntu: {app['ubuntu_mean_time']:.1f}s ({app['ubuntu_success_rate']*100:.0f}% success)")
                if app['windows_mean_time']:
                    print(f"     Windows: {app['windows_mean_time']:.1f}s ({app['windows_success_rate']*100:.0f}% success)")
        
        print("\n✅ Integration test suite completed!")
    
    async def _cleanup(self):
        """Cleanup test resources."""
        print("\n🧹 Cleaning up test resources...")
        
        # Cleanup MinIO buckets
        self.minio_manager.cleanup_test_buckets()
        
        # Cleanup VMs
        self.vm_manager.cleanup_all_vms()
        
        # Cleanup old benchmarks (keep last 30 days)
        self.benchmark_manager.cleanup_old_benchmarks(days_to_keep=30)
        
        print("✅ Cleanup completed")


async def main():
    """Main entry point."""
    orchestrator = IntegrationTestOrchestrator()
    await orchestrator.run_complete_test_suite()


if __name__ == "__main__":
    asyncio.run(main())
