# TurdParty Integration Test Environment Configuration

# Test Environment
TEST_MODE=true
DEBUG=true
LOG_LEVEL=DEBUG

# MinIO Configuration
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin
MINIO_SECURE=false
MINIO_TEST_BUCKET_PREFIX=turdparty-test
MINIO_REGION=us-east-1

# API Configuration
API_BASE_URL=http://localhost:8000
API_VERSION=v1
API_TIMEOUT=300

# VM Configuration
VAGRANT_GRPC_PORT=40000
VM_CAPTURE_TIMEOUT_MINUTES=5
VM_BOOT_TIMEOUT_MINUTES=10
VM_TEARDOWN_TIMEOUT_MINUTES=5
VM_NETWORK_DISABLED=true
VM_MOUNTS_DISABLED=true

# VirtualBox Configuration
VBOX_HEADLESS=true
VBOX_MEMORY_MB=2048
VBOX_CPUS=2
VBOX_DISK_SIZE_GB=20

# ELK Configuration
ELASTICSEARCH_URL=http://localhost:9200
ELASTICSEARCH_INDEX_PREFIX=turdparty-test
ELASTICSEARCH_TIMEOUT=30
KIBANA_URL=http://localhost:5601
LOGSTASH_HOST=localhost
LOGSTASH_PORT=5044

# Test Data Configuration
TEST_DOWNLOAD_DIR=/tmp/turdparty-test-downloads
TEST_APPS_JSON=tests/data/test_applications.json
TEST_BENCHMARK_FILE=tests/data/benchmarks.json
TEST_CLEANUP_ENABLED=true

# File Processing Configuration
BLAKE3_HASH_ENABLED=true
FILE_CHUNK_SIZE=8192
MAX_FILE_SIZE_MB=2048
DOWNLOAD_TIMEOUT_SECONDS=300
UPLOAD_TIMEOUT_SECONDS=300

# VM Test Configuration
UBUNTU_VM_COUNT=2
WINDOWS_VM_COUNT=2
UBUNTU_VM_IMAGE=ubuntu/focal64
WINDOWS_VM_IMAGE=gusztavvargadr/windows-10

# Frida Configuration
FRIDA_CAPTURE_ENABLED=true
FRIDA_SCRIPT_TIMEOUT=300
FRIDA_LOG_LEVEL=info

# Test Execution Configuration
PARALLEL_TESTS=false
TEST_RETRY_COUNT=3
TEST_RETRY_DELAY=5
BENCHMARK_PERSISTENCE=true

# Security Configuration
DISABLE_VM_NETWORKING=true
DISABLE_VM_SHARED_FOLDERS=true
ENABLE_VM_ISOLATION=true
SANDBOX_MODE=true

# Performance Configuration
CONCURRENT_DOWNLOADS=3
CONCURRENT_UPLOADS=3
CONCURRENT_VMS=1
MEMORY_LIMIT_MB=4096

# Cleanup Configuration
AUTO_CLEANUP_DOWNLOADS=true
AUTO_CLEANUP_VMS=true
AUTO_CLEANUP_MINIO=true
KEEP_FAILED_TEST_DATA=true
