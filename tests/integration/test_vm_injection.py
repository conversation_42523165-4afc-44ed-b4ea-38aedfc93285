"""
TurdParty VM Injection and ELK Integration Tests

This module tests the complete API->MinIO->VM injection->ELK data flow
with real VMs and captures installation footprints.
"""

import json
import os
import time
import uuid
from pathlib import Path
from typing import Dict, List, Optional
import asyncio
import subprocess

import pytest
import httpx
from elasticsearch import Elasticsearch
import grpc

from tests.conftest import load_test_config
from tests.integration.test_app_downloader import ApplicationDownloader, MinIOTestManager


class VagrantVMManager:
    """Manages Vagrant VMs for testing."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.grpc_port = int(config.get('VAGRANT_GRPC_PORT', 40000))
        self.vm_timeout = int(config.get('VM_CAPTURE_TIMEOUT_MINUTES', 5)) * 60
        self.boot_timeout = int(config.get('VM_BOOT_TIMEOUT_MINUTES', 10)) * 60
        self.active_vms: List[str] = []
        
    def create_ubuntu_vm(self, vm_name: str) -> Dict:
        """Create and start Ubuntu VM."""
        vm_config = {
            'name': vm_name,
            'box': self.config.get('UBUNTU_VM_IMAGE', 'ubuntu/focal64'),
            'memory': int(self.config.get('VBOX_MEMORY_MB', 2048)),
            'cpus': int(self.config.get('VBOX_CPUS', 2)),
            'network_disabled': self.config.get('DISABLE_VM_NETWORKING', 'true').lower() == 'true',
            'shared_folders_disabled': self.config.get('DISABLE_VM_SHARED_FOLDERS', 'true').lower() == 'true'
        }
        
        # Create Vagrantfile
        vagrantfile_content = self._generate_ubuntu_vagrantfile(vm_config)
        vm_dir = Path(f"/tmp/turdparty-vm-{vm_name}")
        vm_dir.mkdir(parents=True, exist_ok=True)
        
        vagrantfile_path = vm_dir / "Vagrantfile"
        with open(vagrantfile_path, 'w') as f:
            f.write(vagrantfile_content)
        
        # Start VM
        start_time = time.time()
        result = subprocess.run(
            ['vagrant', 'up'],
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=self.boot_timeout
        )
        
        if result.returncode != 0:
            raise Exception(f"Failed to start VM {vm_name}: {result.stderr}")
        
        boot_time = time.time() - start_time
        self.active_vms.append(vm_name)
        
        return {
            'vm_name': vm_name,
            'vm_dir': str(vm_dir),
            'boot_time_seconds': boot_time,
            'status': 'running',
            'config': vm_config
        }
    
    def create_windows_vm(self, vm_name: str) -> Dict:
        """Create and start Windows VM."""
        vm_config = {
            'name': vm_name,
            'box': self.config.get('WINDOWS_VM_IMAGE', 'gusztavvargadr/windows-10'),
            'memory': int(self.config.get('VBOX_MEMORY_MB', 2048)),
            'cpus': int(self.config.get('VBOX_CPUS', 2)),
            'network_disabled': self.config.get('DISABLE_VM_NETWORKING', 'true').lower() == 'true',
            'shared_folders_disabled': self.config.get('DISABLE_VM_SHARED_FOLDERS', 'true').lower() == 'true'
        }
        
        # Create Vagrantfile
        vagrantfile_content = self._generate_windows_vagrantfile(vm_config)
        vm_dir = Path(f"/tmp/turdparty-vm-{vm_name}")
        vm_dir.mkdir(parents=True, exist_ok=True)
        
        vagrantfile_path = vm_dir / "Vagrantfile"
        with open(vagrantfile_path, 'w') as f:
            f.write(vagrantfile_content)
        
        # Start VM
        start_time = time.time()
        result = subprocess.run(
            ['vagrant', 'up'],
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=self.boot_timeout
        )
        
        if result.returncode != 0:
            raise Exception(f"Failed to start VM {vm_name}: {result.stderr}")
        
        boot_time = time.time() - start_time
        self.active_vms.append(vm_name)
        
        return {
            'vm_name': vm_name,
            'vm_dir': str(vm_dir),
            'boot_time_seconds': boot_time,
            'status': 'running',
            'config': vm_config
        }
    
    def inject_file_to_vm(self, vm_name: str, file_path: Path, target_path: str) -> Dict:
        """Inject file into VM and capture installation footprint."""
        vm_dir = Path(f"/tmp/turdparty-vm-{vm_name}")
        
        # Copy file to VM
        start_time = time.time()
        
        # For Ubuntu VMs
        if 'ubuntu' in vm_name.lower():
            copy_cmd = [
                'vagrant', 'upload', str(file_path), target_path
            ]
        else:  # Windows VMs
            copy_cmd = [
                'vagrant', 'upload', str(file_path), target_path
            ]
        
        result = subprocess.run(
            copy_cmd,
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=300
        )
        
        if result.returncode != 0:
            raise Exception(f"Failed to copy file to VM {vm_name}: {result.stderr}")
        
        injection_time = time.time() - start_time
        
        return {
            'vm_name': vm_name,
            'file_injected': str(file_path),
            'target_path': target_path,
            'injection_time_seconds': injection_time,
            'injection_timestamp': time.time()
        }
    
    def install_application_in_vm(self, vm_name: str, app_config: Dict, file_path: Path) -> Dict:
        """Install application in VM and capture footprint."""
        vm_dir = Path(f"/tmp/turdparty-vm-{vm_name}")
        
        # Prepare installation command
        install_cmd = app_config.get('install_command', '')
        if not install_cmd:
            raise ValueError(f"No install command for {app_config['name']}")
        
        # Execute installation
        start_time = time.time()
        
        # Run installation command in VM
        vagrant_cmd = [
            'vagrant', 'ssh', '-c', f"cd /tmp && {install_cmd}"
        ]
        
        result = subprocess.run(
            vagrant_cmd,
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=self.vm_timeout
        )
        
        install_time = time.time() - start_time
        
        # Capture installation footprint
        footprint = self._capture_installation_footprint(vm_name, app_config)
        
        return {
            'vm_name': vm_name,
            'app_name': app_config['name'],
            'install_time_seconds': install_time,
            'install_success': result.returncode == 0,
            'install_output': result.stdout,
            'install_errors': result.stderr,
            'footprint': footprint,
            'timestamp': time.time()
        }
    
    def _capture_installation_footprint(self, vm_name: str, app_config: Dict) -> Dict:
        """Capture installation footprint using Frida-like techniques."""
        vm_dir = Path(f"/tmp/turdparty-vm-{vm_name}")
        
        # Capture file system changes
        fs_cmd = [
            'vagrant', 'ssh', '-c', 
            "find /opt /usr/local /usr/bin /usr/share -newer /tmp -type f 2>/dev/null | head -100"
        ]
        
        fs_result = subprocess.run(
            fs_cmd,
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=60
        )
        
        # Capture process information
        proc_cmd = [
            'vagrant', 'ssh', '-c', 
            "ps aux | grep -v grep | grep -E '(python|node|firefox|chrome)' || true"
        ]
        
        proc_result = subprocess.run(
            proc_cmd,
            cwd=vm_dir,
            capture_output=True,
            text=True,
            timeout=30
        )
        
        return {
            'filesystem_changes': fs_result.stdout.strip().split('\n') if fs_result.stdout.strip() else [],
            'running_processes': proc_result.stdout.strip().split('\n') if proc_result.stdout.strip() else [],
            'capture_timestamp': time.time()
        }
    
    def destroy_vm(self, vm_name: str) -> bool:
        """Destroy VM and cleanup."""
        vm_dir = Path(f"/tmp/turdparty-vm-{vm_name}")
        
        if not vm_dir.exists():
            return True
        
        try:
            result = subprocess.run(
                ['vagrant', 'destroy', '-f'],
                cwd=vm_dir,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            # Remove VM directory
            import shutil
            shutil.rmtree(vm_dir, ignore_errors=True)
            
            if vm_name in self.active_vms:
                self.active_vms.remove(vm_name)
            
            return result.returncode == 0
            
        except Exception as e:
            print(f"Warning: Failed to destroy VM {vm_name}: {e}")
            return False
    
    def cleanup_all_vms(self):
        """Cleanup all active VMs."""
        for vm_name in self.active_vms.copy():
            self.destroy_vm(vm_name)
    
    def _generate_ubuntu_vagrantfile(self, config: Dict) -> str:
        """Generate Vagrantfile for Ubuntu VM."""
        return f'''
Vagrant.configure("2") do |config|
  config.vm.box = "{config['box']}"
  config.vm.hostname = "{config['name']}"
  
  # Disable networking if required
  {"config.vm.network :private_network, type: :dhcp" if not config['network_disabled'] else "# Networking disabled"}
  
  # Disable shared folders if required
  {"config.vm.synced_folder '.', '/vagrant', disabled: true" if config['shared_folders_disabled'] else ""}
  
  config.vm.provider "virtualbox" do |vb|
    vb.memory = {config['memory']}
    vb.cpus = {config['cpus']}
    vb.gui = false
    vb.name = "{config['name']}"
  end
  
  # Provision for testing
  config.vm.provision "shell", inline: <<-SHELL
    apt-get update
    apt-get install -y curl wget unzip
    mkdir -p /tmp/turdparty-test
  SHELL
end
'''
    
    def _generate_windows_vagrantfile(self, config: Dict) -> str:
        """Generate Vagrantfile for Windows VM."""
        return f'''
Vagrant.configure("2") do |config|
  config.vm.box = "{config['box']}"
  config.vm.hostname = "{config['name']}"
  
  # Disable networking if required
  {"config.vm.network :private_network, type: :dhcp" if not config['network_disabled'] else "# Networking disabled"}
  
  # Disable shared folders if required
  {"config.vm.synced_folder '.', '/vagrant', disabled: true" if config['shared_folders_disabled'] else ""}
  
  config.vm.provider "virtualbox" do |vb|
    vb.memory = {config['memory']}
    vb.cpus = {config['cpus']}
    vb.gui = false
    vb.name = "{config['name']}"
  end
  
  # Windows-specific configuration
  config.vm.communicator = "winrm"
  config.winrm.username = "vagrant"
  config.winrm.password = "vagrant"
  
  # Provision for testing
  config.vm.provision "shell", inline: <<-SHELL
    New-Item -ItemType Directory -Force -Path C:\\temp\\turdparty-test
  SHELL
end
'''


class ELKDataManager:
    """Manages ELK data operations."""
    
    def __init__(self, config: Dict):
        self.config = config
        self.es_client = Elasticsearch([config['ELASTICSEARCH_URL']])
        self.index_prefix = config.get('ELASTICSEARCH_INDEX_PREFIX', 'turdparty-test')
        
    def store_test_data(self, test_type: str, data: Dict) -> str:
        """Store test data in Elasticsearch."""
        index_name = f"{self.index_prefix}-{test_type}-{time.strftime('%Y-%m-%d')}"
        doc_id = str(uuid.uuid4())
        
        # Add metadata
        document = {
            'timestamp': time.time(),
            'test_type': test_type,
            'test_id': doc_id,
            **data
        }
        
        self.es_client.index(
            index=index_name,
            id=doc_id,
            body=document
        )
        
        return doc_id
    
    def query_test_data(self, test_type: str, hours_back: int = 24) -> List[Dict]:
        """Query test data from Elasticsearch."""
        index_pattern = f"{self.index_prefix}-{test_type}-*"
        
        query = {
            "query": {
                "range": {
                    "timestamp": {
                        "gte": time.time() - (hours_back * 3600)
                    }
                }
            },
            "sort": [{"timestamp": {"order": "desc"}}]
        }
        
        try:
            response = self.es_client.search(
                index=index_pattern,
                body=query,
                size=100
            )
            
            return [hit['_source'] for hit in response['hits']['hits']]
            
        except Exception as e:
            print(f"Warning: Failed to query ELK data: {e}")
            return []


@pytest.fixture
def vm_manager(test_config):
    """Create VM manager."""
    manager = VagrantVMManager(test_config)
    yield manager
    # Cleanup after test
    if test_config.get('AUTO_CLEANUP_VMS', 'true').lower() == 'true':
        manager.cleanup_all_vms()


@pytest.fixture
def elk_manager(test_config):
    """Create ELK data manager."""
    return ELKDataManager(test_config)


class TestVMInjection:
    """Test VM injection and ELK integration."""
    
    @pytest.mark.asyncio
    @pytest.mark.slow
    async def test_ubuntu_vm_injection_workflow(
        self, 
        app_downloader, 
        minio_manager, 
        vm_manager, 
        elk_manager,
        test_applications
    ):
        """Test complete Ubuntu VM injection workflow."""
        # Select Ubuntu application
        app = test_applications['linux_applications']['deb_packages'][0]  # Firefox
        
        # Download application
        file_path, metadata = await app_downloader.download_file(
            app['url'], app['filename']
        )
        
        # Upload to MinIO
        bucket_name = minio_manager.create_test_bucket()
        upload_result = minio_manager.upload_file_with_metadata(
            bucket_name, file_path, metadata
        )
        
        # Create Ubuntu VM
        vm_name = f"ubuntu-test-{uuid.uuid4().hex[:8]}"
        vm_info = vm_manager.create_ubuntu_vm(vm_name)
        
        # Inject file to VM
        injection_result = vm_manager.inject_file_to_vm(
            vm_name, file_path, f"/tmp/{file_path.name}"
        )
        
        # Install application in VM
        install_result = vm_manager.install_application_in_vm(
            vm_name, app, file_path
        )
        
        # Store results in ELK
        test_data = {
            'platform': 'ubuntu',
            'application': app,
            'download_metadata': metadata,
            'minio_upload': upload_result,
            'vm_info': vm_info,
            'injection_result': injection_result,
            'install_result': install_result
        }
        
        elk_doc_id = elk_manager.store_test_data('vm-injection', test_data)
        
        # Verify data was stored
        assert elk_doc_id is not None
        
        # Verify VM installation
        assert install_result['install_time_seconds'] > 0
        assert len(install_result['footprint']['filesystem_changes']) >= 0
        
        # Cleanup VM
        vm_manager.destroy_vm(vm_name)
    
    @pytest.mark.asyncio
    @pytest.mark.slow
    async def test_windows_vm_injection_workflow(
        self, 
        app_downloader, 
        minio_manager, 
        vm_manager, 
        elk_manager,
        test_applications
    ):
        """Test complete Windows VM injection workflow."""
        # Select Windows application
        app = test_applications['windows_applications']['exe_packages'][0]  # Firefox
        
        # Download application
        file_path, metadata = await app_downloader.download_file(
            app['url'], app['filename']
        )
        
        # Upload to MinIO
        bucket_name = minio_manager.create_test_bucket()
        upload_result = minio_manager.upload_file_with_metadata(
            bucket_name, file_path, metadata
        )
        
        # Create Windows VM
        vm_name = f"windows-test-{uuid.uuid4().hex[:8]}"
        vm_info = vm_manager.create_windows_vm(vm_name)
        
        # Inject file to VM
        injection_result = vm_manager.inject_file_to_vm(
            vm_name, file_path, f"C:\\temp\\{file_path.name}"
        )
        
        # Install application in VM
        install_result = vm_manager.install_application_in_vm(
            vm_name, app, file_path
        )
        
        # Store results in ELK
        test_data = {
            'platform': 'windows',
            'application': app,
            'download_metadata': metadata,
            'minio_upload': upload_result,
            'vm_info': vm_info,
            'injection_result': injection_result,
            'install_result': install_result
        }
        
        elk_doc_id = elk_manager.store_test_data('vm-injection', test_data)
        
        # Verify data was stored
        assert elk_doc_id is not None
        
        # Verify VM installation
        assert install_result['install_time_seconds'] > 0
        
        # Cleanup VM
        vm_manager.destroy_vm(vm_name)
