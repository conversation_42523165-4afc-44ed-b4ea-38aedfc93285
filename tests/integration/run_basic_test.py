#!/usr/bin/env python3
"""
Basic Integration Test Runner

This script runs a simplified version of the integration tests to verify
the setup is working correctly before running the full test suite.
"""

import asyncio
import json
import sys
import time
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from tests.conftest import load_test_config
from tests.integration.test_app_downloader import ApplicationDownloader, MinIOTestManager
from tests.integration.test_benchmark_persistence import BenchmarkManager


async def test_basic_download():
    """Test basic file download functionality."""
    print("🔍 Testing basic download functionality...")
    
    config = load_test_config()
    downloader = ApplicationDownloader(config)
    
    # Test with a small, reliable file
    test_url = "https://httpbin.org/json"
    test_filename = "test_download.json"
    
    try:
        file_path, metadata = await downloader.download_file(test_url, test_filename)
        
        print(f"✅ Downloaded {test_filename}")
        print(f"   Size: {metadata['file_size']} bytes")
        print(f"   Blake3: {metadata['blake3_hash'][:16]}...")
        print(f"   Time: {metadata['download_time_seconds']:.2f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Download test failed: {e}")
        return False


def test_minio_connection():
    """Test MinIO connection and bucket operations."""
    print("🔍 Testing MinIO connection...")
    
    try:
        config = load_test_config()
        minio_manager = MinIOTestManager(config)
        
        # Test bucket creation
        bucket_name = minio_manager.create_test_bucket()
        print(f"✅ Created test bucket: {bucket_name}")
        
        # Test bucket cleanup
        minio_manager.cleanup_test_buckets()
        print("✅ Cleaned up test buckets")
        
        return True
        
    except Exception as e:
        print(f"❌ MinIO test failed: {e}")
        return False


def test_benchmark_persistence():
    """Test benchmark persistence functionality."""
    print("🔍 Testing benchmark persistence...")
    
    try:
        config = load_test_config()
        benchmark_manager = BenchmarkManager(config)
        
        # Test recording a benchmark
        benchmark_manager.record_download_benchmark(
            app_name='test_app',
            file_size=1024,
            download_time=1.5
        )
        
        # Test loading benchmarks
        data = benchmark_manager.load_benchmarks()
        assert 'benchmarks' in data
        assert 'download_times' in data['benchmarks']
        
        print("✅ Benchmark persistence working")
        return True
        
    except Exception as e:
        print(f"❌ Benchmark test failed: {e}")
        return False


def test_configuration():
    """Test configuration loading."""
    print("🔍 Testing configuration loading...")
    
    try:
        config = load_test_config()
        
        # Check required keys
        required_keys = [
            'TEST_MODE',
            'MINIO_ENDPOINT',
            'ELASTICSEARCH_URL',
            'VAGRANT_GRPC_PORT'
        ]
        
        for key in required_keys:
            if key not in config:
                raise ValueError(f"Missing config key: {key}")
        
        print("✅ Configuration loaded successfully")
        print(f"   Test Mode: {config['TEST_MODE']}")
        print(f"   MinIO: {config['MINIO_ENDPOINT']}")
        print(f"   ELK: {config['ELASTICSEARCH_URL']}")
        print(f"   Vagrant gRPC: {config['VAGRANT_GRPC_PORT']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def test_applications_data():
    """Test applications data loading."""
    print("🔍 Testing applications data...")
    
    try:
        apps_file = Path(__file__).parent.parent / 'data' / 'test_applications.json'
        
        if not apps_file.exists():
            raise FileNotFoundError(f"Applications file not found: {apps_file}")
        
        with open(apps_file, 'r') as f:
            apps_data = json.load(f)
        
        # Verify structure
        linux_apps = apps_data['linux_applications']
        windows_apps = apps_data['windows_applications']
        
        linux_count = (
            len(linux_apps['deb_packages']) +
            len(linux_apps['appimage_packages']) +
            len(linux_apps['tar_gz_packages']) +
            1  # custom_choice
        )
        
        windows_count = (
            len(windows_apps['exe_packages']) +
            len(windows_apps['msi_packages'])
        )
        
        print("✅ Applications data loaded successfully")
        print(f"   Linux apps: {linux_count}")
        print(f"   Windows apps: {windows_count}")
        print(f"   Total: {linux_count + windows_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Applications data test failed: {e}")
        return False


async def main():
    """Run basic integration tests."""
    print("🚀 TurdParty Basic Integration Test")
    print("=" * 40)
    
    tests = [
        ("Configuration", test_configuration),
        ("Applications Data", test_applications_data),
        ("Benchmark Persistence", test_benchmark_persistence),
        ("MinIO Connection", test_minio_connection),
        ("Basic Download", test_basic_download),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} test...")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            results.append((test_name, result))
            
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 40)
    print("📊 Test Results Summary")
    print("=" * 40)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All basic tests passed! Ready for full integration testing.")
        return 0
    else:
        print("⚠️ Some tests failed. Please check the configuration and dependencies.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
