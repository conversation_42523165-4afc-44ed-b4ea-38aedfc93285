"""
Integration tests for MinIO storage integration.

Tests the complete MinIO file storage pipeline including upload, download,
and file management operations. Ensures PEP8, PEP257, and PEP484 compliance.
"""

import hashlib
import os
import tempfile
import uuid
from pathlib import Path
from typing import Dict, Any, List
from unittest.mock import patch

import pytest
from minio import Minio
from minio.error import S3Error

from api.services.storage_service import StorageService


@pytest.mark.skipif(
    os.environ.get("IN_DOCKER") != "true",
    reason="Integration tests should only run in Docker"
)
class TestMinIOIntegration:
    """Integration test suite for MinIO storage."""

    @pytest.fixture
    def minio_client(self) -> Minio:
        """Create a MinIO client for integration testing."""
        return Minio(
            endpoint=os.getenv("MINIO_ENDPOINT", "minio:9000"),
            access_key=os.getenv("MINIO_ACCESS_KEY", "minioadmin"),
            secret_key=os.getenv("MINIO_SECRET_KEY", "minioadmin"),
            secure=False
        )

    @pytest.fixture
    def storage_service(self, minio_client: Minio) -> StorageService:
        """Create a storage service instance for testing."""
        with patch("api.services.storage_service.Minio", return_value=minio_client):
            return StorageService()

    @pytest.fixture
    def test_bucket(self, minio_client: Minio) -> str:
        """Create a test bucket for integration testing."""
        bucket_name = "test-turdparty-files"
        
        # Create bucket if it doesn't exist
        if not minio_client.bucket_exists(bucket_name):
            minio_client.make_bucket(bucket_name)
        
        yield bucket_name
        
        # Cleanup: Remove all objects and bucket
        try:
            objects = minio_client.list_objects(bucket_name, recursive=True)
            for obj in objects:
                minio_client.remove_object(bucket_name, obj.object_name)
            minio_client.remove_bucket(bucket_name)
        except S3Error:
            pass  # Bucket might already be cleaned up

    @pytest.fixture
    def sample_file_data(self) -> Dict[str, Any]:
        """Provide sample file data for testing."""
        content = b"#!/bin/bash\necho 'Integration test file'\nexit 0\n"
        return {
            "filename": "integration_test.sh",
            "content": content,
            "content_type": "application/x-sh",
            "size": len(content),
            "hash": hashlib.sha256(content).hexdigest()
        }

    def test_upload_file_success(
        self,
        storage_service: StorageService,
        test_bucket: str,
        sample_file_data: Dict[str, Any],
    ) -> None:
        """Test successful file upload to MinIO."""
        # Arrange
        file_id = str(uuid.uuid4())
        
        # Act
        result = storage_service.upload_file(
            file_id=file_id,
            filename=sample_file_data["filename"],
            file_content=sample_file_data["content"],
            content_type=sample_file_data["content_type"],
            bucket_name=test_bucket
        )

        # Assert
        assert result["success"] is True
        assert result["file_id"] == file_id
        assert result["object_key"] is not None
        assert result["bucket"] == test_bucket
        assert result["file_size"] == sample_file_data["size"]

    def test_download_file_success(
        self,
        storage_service: StorageService,
        test_bucket: str,
        sample_file_data: Dict[str, Any],
    ) -> None:
        """Test successful file download from MinIO."""
        # Arrange - Upload file first
        file_id = str(uuid.uuid4())
        upload_result = storage_service.upload_file(
            file_id=file_id,
            filename=sample_file_data["filename"],
            file_content=sample_file_data["content"],
            content_type=sample_file_data["content_type"],
            bucket_name=test_bucket
        )
        
        # Act
        download_result = storage_service.download_file(
            bucket_name=test_bucket,
            object_key=upload_result["object_key"]
        )

        # Assert
        assert download_result["success"] is True
        assert download_result["content"] == sample_file_data["content"]
        assert download_result["file_size"] == sample_file_data["size"]

    def test_file_exists_check(
        self,
        storage_service: StorageService,
        test_bucket: str,
        sample_file_data: Dict[str, Any],
    ) -> None:
        """Test file existence checking in MinIO."""
        # Arrange - Upload file first
        file_id = str(uuid.uuid4())
        upload_result = storage_service.upload_file(
            file_id=file_id,
            filename=sample_file_data["filename"],
            file_content=sample_file_data["content"],
            content_type=sample_file_data["content_type"],
            bucket_name=test_bucket
        )
        
        # Act & Assert - File should exist
        assert storage_service.file_exists(test_bucket, upload_result["object_key"]) is True
        
        # Act & Assert - Non-existent file should not exist
        assert storage_service.file_exists(test_bucket, "non-existent-file.txt") is False

    def test_delete_file_success(
        self,
        storage_service: StorageService,
        test_bucket: str,
        sample_file_data: Dict[str, Any],
    ) -> None:
        """Test successful file deletion from MinIO."""
        # Arrange - Upload file first
        file_id = str(uuid.uuid4())
        upload_result = storage_service.upload_file(
            file_id=file_id,
            filename=sample_file_data["filename"],
            file_content=sample_file_data["content"],
            content_type=sample_file_data["content_type"],
            bucket_name=test_bucket
        )
        
        # Verify file exists
        assert storage_service.file_exists(test_bucket, upload_result["object_key"]) is True
        
        # Act
        delete_result = storage_service.delete_file(
            bucket_name=test_bucket,
            object_key=upload_result["object_key"]
        )

        # Assert
        assert delete_result["success"] is True
        assert storage_service.file_exists(test_bucket, upload_result["object_key"]) is False

    def test_list_files_in_bucket(
        self,
        storage_service: StorageService,
        test_bucket: str,
        sample_file_data: Dict[str, Any],
    ) -> None:
        """Test listing files in MinIO bucket."""
        # Arrange - Upload multiple files
        file_ids = [str(uuid.uuid4()) for _ in range(3)]
        uploaded_objects = []
        
        for i, file_id in enumerate(file_ids):
            content = f"#!/bin/bash\necho 'Test file {i}'\n".encode()
            upload_result = storage_service.upload_file(
                file_id=file_id,
                filename=f"test_file_{i}.sh",
                file_content=content,
                content_type="application/x-sh",
                bucket_name=test_bucket
            )
            uploaded_objects.append(upload_result["object_key"])
        
        # Act
        file_list = storage_service.list_files(bucket_name=test_bucket)

        # Assert
        assert len(file_list) >= 3
        object_names = [obj["object_name"] for obj in file_list]
        for obj_key in uploaded_objects:
            assert obj_key in object_names

    def test_get_file_metadata(
        self,
        storage_service: StorageService,
        test_bucket: str,
        sample_file_data: Dict[str, Any],
    ) -> None:
        """Test retrieving file metadata from MinIO."""
        # Arrange - Upload file first
        file_id = str(uuid.uuid4())
        upload_result = storage_service.upload_file(
            file_id=file_id,
            filename=sample_file_data["filename"],
            file_content=sample_file_data["content"],
            content_type=sample_file_data["content_type"],
            bucket_name=test_bucket
        )
        
        # Act
        metadata = storage_service.get_file_metadata(
            bucket_name=test_bucket,
            object_key=upload_result["object_key"]
        )

        # Assert
        assert metadata["success"] is True
        assert metadata["size"] == sample_file_data["size"]
        assert metadata["content_type"] == sample_file_data["content_type"]
        assert metadata["last_modified"] is not None

    def test_large_file_upload_download(
        self,
        storage_service: StorageService,
        test_bucket: str,
    ) -> None:
        """Test upload and download of large files."""
        # Arrange - Create a large file (5MB)
        large_content = b"x" * (5 * 1024 * 1024)
        file_id = str(uuid.uuid4())
        
        # Act - Upload large file
        upload_result = storage_service.upload_file(
            file_id=file_id,
            filename="large_test_file.bin",
            file_content=large_content,
            content_type="application/octet-stream",
            bucket_name=test_bucket
        )

        # Assert upload
        assert upload_result["success"] is True
        assert upload_result["file_size"] == len(large_content)

        # Act - Download large file
        download_result = storage_service.download_file(
            bucket_name=test_bucket,
            object_key=upload_result["object_key"]
        )

        # Assert download
        assert download_result["success"] is True
        assert len(download_result["content"]) == len(large_content)
        assert download_result["content"] == large_content

    def test_concurrent_file_operations(
        self,
        storage_service: StorageService,
        test_bucket: str,
    ) -> None:
        """Test concurrent file upload and download operations."""
        import concurrent.futures
        import threading
        
        # Arrange
        num_files = 10
        file_data = []
        
        for i in range(num_files):
            content = f"#!/bin/bash\necho 'Concurrent test {i}'\n".encode()
            file_data.append({
                "file_id": str(uuid.uuid4()),
                "filename": f"concurrent_test_{i}.sh",
                "content": content,
                "content_type": "application/x-sh"
            })

        # Act - Upload files concurrently
        upload_results = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            upload_futures = [
                executor.submit(
                    storage_service.upload_file,
                    file_id=data["file_id"],
                    filename=data["filename"],
                    file_content=data["content"],
                    content_type=data["content_type"],
                    bucket_name=test_bucket
                )
                for data in file_data
            ]
            
            upload_results = [future.result() for future in upload_futures]

        # Assert all uploads succeeded
        assert all(result["success"] for result in upload_results)
        assert len(upload_results) == num_files

        # Act - Download files concurrently
        download_results = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            download_futures = [
                executor.submit(
                    storage_service.download_file,
                    bucket_name=test_bucket,
                    object_key=result["object_key"]
                )
                for result in upload_results
            ]
            
            download_results = [future.result() for future in download_futures]

        # Assert all downloads succeeded
        assert all(result["success"] for result in download_results)
        assert len(download_results) == num_files

    def test_error_handling_invalid_bucket(
        self,
        storage_service: StorageService,
        sample_file_data: Dict[str, Any],
    ) -> None:
        """Test error handling with invalid bucket operations."""
        # Act - Try to upload to non-existent bucket
        upload_result = storage_service.upload_file(
            file_id=str(uuid.uuid4()),
            filename=sample_file_data["filename"],
            file_content=sample_file_data["content"],
            content_type=sample_file_data["content_type"],
            bucket_name="non-existent-bucket"
        )

        # Assert
        assert upload_result["success"] is False
        assert "error" in upload_result

    def test_error_handling_invalid_object(
        self,
        storage_service: StorageService,
        test_bucket: str,
    ) -> None:
        """Test error handling with invalid object operations."""
        # Act - Try to download non-existent object
        download_result = storage_service.download_file(
            bucket_name=test_bucket,
            object_key="non-existent-object.txt"
        )

        # Assert
        assert download_result["success"] is False
        assert "error" in download_result
