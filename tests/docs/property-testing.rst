Property-Based Testing
======================

Property-based testing with Hypothesis represents a paradigm shift from traditional example-based testing. Instead of writing tests with specific inputs and expected outputs, you define properties that should hold true for a wide range of inputs, and Hypothesis automatically generates test cases to verify these properties.

Overview
--------

Our property-based test suite consists of 9 comprehensive tests that execute in 1.02 seconds, testing thousands of input combinations to discover edge cases that traditional testing might miss.

**Key Benefits:**

* **Automated edge case discovery** - Hypothesis finds inputs that break your code
* **Comprehensive coverage** - Tests thousands of input combinations automatically
* **Regression prevention** - Saves failing examples for future test runs
* **Documentation** - Properties serve as executable specifications

What is Property-Based Testing?
------------------------------

Traditional Testing vs Property-Based Testing
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Traditional Example-Based Testing:**

.. code-block:: python

   def test_filename_validation():
       """Test specific examples."""
       assert validate_filename("test.txt") == True
       assert validate_filename("") == False
       assert validate_filename("file with spaces.txt") == True

**Property-Based Testing:**

.. code-block:: python

   from hypothesis import given, strategies as st

   @given(filename=st.text())
   def test_filename_validation_properties(filename):
       """Test properties that should always hold."""
       result = validate_filename(filename)
       
       # Property: result should always be boolean
       assert isinstance(result, bool)
       
       # Property: empty strings should be invalid
       if len(filename.strip()) == 0:
           assert result == False
       
       # Property: valid filenames should roundtrip
       if result == True:
           assert len(filename) > 0

Core Concepts
~~~~~~~~~~~~

**Properties** are statements that should be true for all valid inputs:

* **Invariants** - Things that never change
* **Postconditions** - What should be true after an operation
* **Relationships** - How inputs relate to outputs
* **Roundtrip properties** - Serialize then deserialize should equal original

Our Property-Based Tests
------------------------

Test Structure
~~~~~~~~~~~~~

Our property-based tests are located in ``tests/property/test_property_based.py``:

.. code-block:: text

   tests/property/
   └── test_property_based.py
       ├── TestFileInjectionProperties      # Model validation properties
       ├── TestDataIntegrityProperties      # Data consistency properties  
       ├── TestValidationProperties         # Input validation properties
       ├── TestConcurrencyProperties        # Concurrent operation properties
       └── TestBoundaryProperties           # Boundary condition properties

File Injection Model Properties
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   class TestFileInjectionProperties:
       """Property-based tests for file injection models."""

       @given(
           filename=st.text(
               min_size=1, 
               max_size=50,
               alphabet=st.characters(
                   whitelist_categories=("Lu", "Ll", "Nd"),
                   whitelist_characters=".-_"
               )
           ),
           target_path=st.text(min_size=5, max_size=100, alphabet="abcdefghijklmnopqrstuvwxyz/").map(
               lambda x: "/" + x.replace("//", "/").strip("/")
           ),
           permissions=st.sampled_from(["0644", "0755", "0777", "0600", "0700"]),
           description=st.one_of(st.none(), st.text(max_size=100))
       )
       @settings(suppress_health_check=[HealthCheck.filter_too_much])
       def test_file_injection_create_roundtrip(
           self,
           filename: str,
           target_path: str, 
           permissions: str,
           description: str | None
       ) -> None:
           """Test that FileInjectionCreate can be created and serialized consistently."""
           # Assume valid inputs
           assume(len(filename.strip()) > 0)
           assume(target_path.startswith("/"))
           assume(not any(char in filename for char in ['/', '\\', '\0', '\n', '\r']))
           
           # Create model
           model = FileInjectionCreate(
               filename=filename,
               target_path=target_path,
               permissions=permissions,
               description=description
           )
           
           # Properties that should always hold
           assert model.filename == filename
           assert model.target_path == target_path
           assert model.permissions == permissions
           assert model.description == description
           
           # Serialization roundtrip property
           json_data = model.model_dump_json()
           recreated = FileInjectionCreate.model_validate_json(json_data)
           
           assert recreated.filename == model.filename
           assert recreated.target_path == model.target_path
           assert recreated.permissions == model.permissions
           assert recreated.description == model.description

Data Integrity Properties
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   class TestDataIntegrityProperties:
       """Property-based tests for data integrity."""

       @given(data=st.binary(min_size=1, max_size=10000))
       @settings(suppress_health_check=[HealthCheck.function_scoped_fixture])
       def test_hash_consistency_property(self, data: bytes) -> None:
           """Test that hash calculation is always consistent."""
           # Calculate hash multiple times
           hash1 = hashlib.sha256(data).hexdigest()
           hash2 = hashlib.sha256(data).hexdigest()
           hash3 = hashlib.sha256(data).hexdigest()
           
           # Hashes should always be identical
           assert hash1 == hash2 == hash3
           
           # Hash should always be 64 characters (SHA256)
           assert len(hash1) == 64
           
           # Hash should only contain hex characters
           assert all(c in '0123456789abcdef' for c in hash1)

Validation Properties
~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   class TestValidationProperties:
       """Property-based tests for validation logic."""

       @given(filename=st.text(max_size=1000))
       def test_filename_validation_properties(self, filename: str) -> None:
           """Test filename validation properties."""
           # Test various filename patterns
           has_path_separator = "/" in filename or "\\" in filename
           has_null_byte = "\0" in filename
           has_newline = "\n" in filename or "\r" in filename
           is_empty = len(filename.strip()) == 0
           
           # If filename has dangerous characters, model creation might fail
           # This is expected behavior
           if has_path_separator or has_null_byte or has_newline or is_empty:
               # These should be handled by validation in service layer
               pass
           else:
               # Safe filenames should always work
               try:
                   model = FileInjectionCreate(
                       filename=filename,
                       target_path="/app/test.sh",
                       permissions="0755"
                   )
                   assert model.filename == filename
               except Exception:
                   # Some edge cases might still fail, which is acceptable
                   pass

Hypothesis Strategies
--------------------

Strategies define how Hypothesis generates test data. Our tests use various strategies to create realistic and edge-case inputs.

Basic Strategies
~~~~~~~~~~~~~~~

.. code-block:: python

   import hypothesis.strategies as st

   # Text strategies
   st.text()                           # Any unicode text
   st.text(min_size=1, max_size=100)  # Text with size constraints
   st.text(alphabet="abcdef")          # Text from specific alphabet

   # Numeric strategies  
   st.integers()                       # Any integer
   st.integers(min_value=0, max_value=100)  # Bounded integers
   st.floats(min_value=0.0, max_value=1.0)  # Bounded floats

   # Collection strategies
   st.lists(st.integers())             # Lists of integers
   st.dictionaries(st.text(), st.integers())  # String to int mappings
   st.tuples(st.text(), st.integers()) # Fixed-size tuples

Custom Strategies
~~~~~~~~~~~~~~~~

.. code-block:: python

   # Custom filename strategy
   filename_strategy = st.text(
       min_size=1,
       max_size=255,
       alphabet=st.characters(
           whitelist_categories=("Lu", "Ll", "Nd"),  # Letters and digits
           whitelist_characters=".-_"                 # Safe punctuation
       )
   ).filter(lambda x: not x.startswith('.'))  # No hidden files

   # Custom path strategy
   path_strategy = st.text(
       min_size=5, 
       max_size=100, 
       alphabet="abcdefghijklmnopqrstuvwxyz/"
   ).map(lambda x: "/" + x.replace("//", "/").strip("/"))

   # Custom permissions strategy
   permissions_strategy = st.sampled_from([
       "0644", "0755", "0777", "0600", "0700", "0750", "0640"
   ])

Composite Strategies
~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   from hypothesis import strategies as st
   from hypothesis.strategies import composite

   @composite
   def file_injection_data(draw):
       """Generate complete file injection data."""
       filename = draw(st.text(min_size=1, max_size=50))
       target_path = draw(st.text(min_size=5, max_size=100))
       permissions = draw(st.sampled_from(["0644", "0755", "0777"]))
       description = draw(st.one_of(st.none(), st.text(max_size=200)))
       
       return {
           "filename": filename,
           "target_path": f"/{target_path.strip('/')}",
           "permissions": permissions,
           "description": description
       }

   @given(data=file_injection_data())
   def test_with_composite_strategy(data):
       """Test using composite strategy."""
       model = FileInjectionCreate(**data)
       assert model.filename == data["filename"]

Advanced Hypothesis Features
----------------------------

Assumptions
~~~~~~~~~~

Use ``assume()`` to filter out invalid inputs:

.. code-block:: python

   from hypothesis import given, assume
   import hypothesis.strategies as st

   @given(x=st.integers(), y=st.integers())
   def test_division_properties(x, y):
       """Test division properties."""
       assume(y != 0)  # Skip cases where y is zero
       
       result = x / y
       assert isinstance(result, float)
       assert result * y == x  # Property: (x/y) * y == x

Examples and Targeting
~~~~~~~~~~~~~~~~~~~~~

Provide specific examples and guide Hypothesis towards interesting cases:

.. code-block:: python

   @given(filename=st.text())
   @example(filename="")           # Always test empty string
   @example(filename="test.txt")   # Always test normal case
   @example(filename="a" * 1000)   # Always test very long string
   def test_filename_with_examples(filename):
       """Test with specific examples."""
       result = validate_filename(filename)
       assert isinstance(result, bool)

Settings and Profiles
~~~~~~~~~~~~~~~~~~~~

Configure Hypothesis behavior:

.. code-block:: python

   from hypothesis import settings, Verbosity

   # Test-specific settings
   @given(data=st.binary(min_size=1, max_size=1000000))
   @settings(
       max_examples=50,           # Run 50 examples instead of default 100
       deadline=5000,             # Allow 5 seconds per example
       verbosity=Verbosity.verbose  # Show detailed output
   )
   def test_large_data_processing(data):
       """Test with custom settings."""
       result = process_large_data(data)
       assert len(result) > 0

   # Global profiles (in conftest.py)
   settings.register_profile("dev", max_examples=10, verbosity=Verbosity.verbose)
   settings.register_profile("ci", max_examples=100, verbosity=Verbosity.normal)
   settings.load_profile("dev")

Stateful Testing
~~~~~~~~~~~~~~~

Test sequences of operations:

.. code-block:: python

   from hypothesis.stateful import RuleBasedStateMachine, rule, invariant
   import hypothesis.strategies as st

   class FileInjectionStateMachine(RuleBasedStateMachine):
       """Stateful testing for file injection service."""
       
       def __init__(self):
           super().__init__()
           self.injections = {}
           self.service = FileInjectionService()
       
       @rule(filename=st.text(min_size=1, max_size=50))
       def create_injection(self, filename):
           """Rule: create a new injection."""
           injection_id = self.service.create_injection({
               "filename": filename,
               "target_path": f"/app/{filename}",
               "permissions": "0755"
           })
           self.injections[injection_id] = filename
       
       @rule()
       def list_injections(self):
           """Rule: list all injections."""
           result = self.service.list_injections()
           assert len(result) == len(self.injections)
       
       @invariant()
       def injection_count_consistent(self):
           """Invariant: injection count should be consistent."""
           stored_count = len(self.service.list_injections())
           tracked_count = len(self.injections)
           assert stored_count == tracked_count

   # Run the state machine
   TestFileInjectionStateMachine = FileInjectionStateMachine.TestCase

Running Property-Based Tests
----------------------------

Basic Execution
~~~~~~~~~~~~~~

.. code-block:: bash

   # Run all property-based tests
   python -m pytest tests/property/ -v

   # Run with Hypothesis statistics
   python -m pytest tests/property/ --hypothesis-show-statistics

   # Run specific property test
   python -m pytest tests/property/test_property_based.py::TestFileInjectionProperties::test_file_injection_create_roundtrip -v

Debugging Failures
~~~~~~~~~~~~~~~~~

When Hypothesis finds a failing case, it provides detailed information:

.. code-block:: bash

   # Run with verbose Hypothesis output
   python -m pytest tests/property/ -v -s --hypothesis-verbosity=verbose

   # Reproduce a specific failure
   python -m pytest tests/property/ --hypothesis-seed=12345

Example failure output:

.. code-block:: text

   FAILED tests/property/test_property_based.py::test_filename_validation - hypothesis.errors.Flaky
   
   Falsifying example: test_filename_validation(filename='/')
   
   This test appears to be flaky. It passed when Hypothesis tried to shrink the
   failing example, but failed on the original input.

Hypothesis Database
~~~~~~~~~~~~~~~~~

Hypothesis saves failing examples in a database for regression testing:

.. code-block:: bash

   # View the Hypothesis database
   ls .hypothesis/examples/

   # Clear the database (start fresh)
   rm -rf .hypothesis/

   # Run without using saved examples
   python -m pytest tests/property/ --hypothesis-database=none

Configuration
~~~~~~~~~~~~

Configure Hypothesis in ``tests/conftest.py``:

.. code-block:: python

   from hypothesis import settings, Verbosity, HealthCheck

   # Development profile - fast feedback
   settings.register_profile(
       "dev",
       max_examples=10,
       verbosity=Verbosity.verbose,
       deadline=1000,  # 1 second per example
       suppress_health_check=[HealthCheck.too_slow]
   )

   # CI profile - thorough testing
   settings.register_profile(
       "ci", 
       max_examples=100,
       verbosity=Verbosity.normal,
       deadline=5000,  # 5 seconds per example
   )

   # Load appropriate profile
   import os
   profile = "ci" if os.getenv("CI") else "dev"
   settings.load_profile(profile)

Best Practices
-------------

Writing Good Properties
~~~~~~~~~~~~~~~~~~~~~~

1. **Think in terms of invariants** - What should always be true?
2. **Test relationships** - How do inputs relate to outputs?
3. **Use roundtrip properties** - Serialize/deserialize should preserve data
4. **Test error conditions** - What should cause failures?

.. code-block:: python

   @given(data=st.dictionaries(st.text(), st.integers()))
   def test_json_roundtrip_property(data):
       """Property: JSON roundtrip should preserve data."""
       # Serialize to JSON
       json_str = json.dumps(data)
       
       # Deserialize from JSON  
       recreated = json.loads(json_str)
       
       # Property: original data should equal recreated data
       assert recreated == data

Strategy Design
~~~~~~~~~~~~~~

1. **Start simple** - Begin with basic strategies
2. **Add constraints gradually** - Refine strategies based on failures
3. **Use realistic data** - Generate data similar to production
4. **Handle edge cases** - Include boundary conditions

.. code-block:: python

   # Evolution of a filename strategy

   # Too simple - generates invalid filenames
   st.text()

   # Better - excludes some problematic characters
   st.text().filter(lambda x: '/' not in x)

   # Good - realistic filenames with proper constraints
   st.text(
       min_size=1,
       max_size=255,
       alphabet=st.characters(
           whitelist_categories=("Lu", "Ll", "Nd"),
           whitelist_characters=".-_"
       )
   ).filter(lambda x: not x.startswith('.') and x.strip() == x)

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~

1. **Use appropriate example counts** - Balance thoroughness with speed
2. **Set reasonable deadlines** - Prevent hanging tests
3. **Filter efficiently** - Use ``assume()`` sparingly
4. **Cache expensive operations** - Reuse computed values

.. code-block:: python

   # Efficient filtering
   @given(x=st.integers(min_value=1, max_value=100))  # Better
   def test_positive_numbers(x):
       assert x > 0

   # Instead of:
   @given(x=st.integers())
   def test_positive_numbers_slow(x):
       assume(x > 0)  # Slower - rejects many examples
       assert x > 0

Integration with CI/CD
---------------------

Property-based tests in continuous integration:

.. code-block:: yaml

   # GitHub Actions example
   - name: Run Property-Based Tests
     run: |
       python -m pytest tests/property/ \
         --hypothesis-profile=ci \
         --hypothesis-show-statistics \
         --junitxml=property-test-results.xml

   - name: Upload Test Results
     uses: actions/upload-artifact@v3
     with:
       name: property-test-results
       path: property-test-results.xml

Monitoring and Metrics
~~~~~~~~~~~~~~~~~~~~~

Track property-based test effectiveness:

* **Coverage of input space** - How much of the possible input space is tested
* **Shrinking efficiency** - How well Hypothesis minimizes failing examples
* **Test execution time** - Balance between thoroughness and speed
* **Failure discovery rate** - How often new edge cases are found

Next Steps
----------

After mastering property-based testing:

1. **Performance Testing** - :doc:`performance-testing`
2. **Security Testing** - :doc:`security-testing`
3. **Advanced Debugging** - :doc:`debugging`
4. **Test Patterns** - :doc:`test-patterns`

Resources
--------

* `Hypothesis Documentation <https://hypothesis.readthedocs.io/>`_
* `Property-Based Testing Patterns <https://hypothesis.works/articles/what-is-property-based-testing/>`_
* `Hypothesis Strategies Reference <https://hypothesis.readthedocs.io/en/latest/data.html>`_
