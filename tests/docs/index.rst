TurdParty Testing Framework Documentation
==========================================

.. image:: https://img.shields.io/badge/Python-3.11+-blue.svg
   :target: https://www.python.org/downloads/
   :alt: Python Version

.. image:: https://img.shields.io/badge/Testing-pytest-green.svg
   :target: https://pytest.org/
   :alt: Testing Framework

.. image:: https://img.shields.io/badge/Property--Based-Hypothesis-orange.svg
   :target: https://hypothesis.readthedocs.io/
   :alt: Property-Based Testing

.. image:: https://img.shields.io/badge/Performance-pytest--benchmark-red.svg
   :target: https://pytest-benchmark.readthedocs.io/
   :alt: Performance Testing

.. image:: https://img.shields.io/badge/Load--Testing-Locust-purple.svg
   :target: https://locust.io/
   :alt: Load Testing

.. image:: https://img.shields.io/badge/Code--Quality-Ruff-yellow.svg
   :target: https://github.com/astral-sh/ruff
   :alt: Code Quality

Welcome to the **TurdParty Testing Framework** documentation! This comprehensive guide covers our modern, industry-standard testing infrastructure designed for reliability, performance, and maintainability.

🎯 **Quick Stats**
   * **36 Unit Tests** - Fast, isolated component validation
   * **9 Property Tests** - Hypothesis-driven edge case discovery  
   * **1.54M ops/sec** - Model creation performance benchmark
   * **90% Issue Reduction** - Code quality improvements with Ruff
   * **Zero Warnings** - Clean, modern Python codebase

.. note::
   This documentation covers the **modern testing stack** implemented in 2024, featuring industry-standard tools and best practices.

Getting Started
===============

.. toctree::
   :maxdepth: 2
   :caption: Quick Start

   quickstart
   installation
   configuration

Testing Framework
=================

.. toctree::
   :maxdepth: 2
   :caption: Core Testing

   unit-testing
   property-testing
   performance-testing
   security-testing
   integration-testing
   load-testing

Tools & Configuration
====================

.. toctree::
   :maxdepth: 2
   :caption: Tools

   pytest-config
   hypothesis-config
   ruff-config
   pre-commit
   ci-cd

Advanced Topics
===============

.. toctree::
   :maxdepth: 2
   :caption: Advanced

   test-patterns
   debugging
   reporting
   best-practices
   troubleshooting

API Reference
=============

.. toctree::
   :maxdepth: 2
   :caption: API

   api/test-fixtures
   api/test-utilities
   api/custom-markers
   api/test-data

Examples & Tutorials
===================

.. toctree::
   :maxdepth: 2
   :caption: Examples

   examples/unit-test-examples
   examples/property-test-examples
   examples/performance-examples
   examples/security-examples

Development
===========

.. toctree::
   :maxdepth: 2
   :caption: Development

   contributing
   test-development
   maintenance
   changelog

Architecture Overview
====================

Our testing framework is built on modern Python testing tools:

.. mermaid::

   graph TB
       A[Developer] --> B[Pre-commit Hooks]
       B --> C[Ruff Linting]
       B --> D[Unit Tests]
       B --> E[Type Checking]
       
       F[CI/CD Pipeline] --> G[Full Test Suite]
       G --> H[Unit Tests]
       G --> I[Property Tests]
       G --> J[Performance Tests]
       G --> K[Security Tests]
       G --> L[Integration Tests]
       G --> M[Load Tests]
       
       H --> N[Coverage Report]
       I --> O[Edge Case Report]
       J --> P[Benchmark Report]
       K --> Q[Security Report]
       L --> R[API Test Report]
       M --> S[Load Test Report]
       
       N --> T[Quality Gate]
       O --> T
       P --> T
       Q --> T
       R --> T
       S --> T
       
       T --> U[Deployment]

Key Features
============

🚀 **Modern Tools**
   * **pytest** - Industry-standard test runner
   * **Hypothesis** - Property-based testing for edge cases
   * **pytest-benchmark** - Performance regression testing
   * **Locust** - Realistic load testing scenarios
   * **Ruff** - Fast Python linting and formatting
   * **MyPy** - Static type checking
   * **Bandit** - Security vulnerability scanning

🧪 **Comprehensive Testing**
   * **Unit Tests** - Fast feedback on component functionality
   * **Property Tests** - Automated edge case discovery
   * **Performance Tests** - Benchmark and regression validation
   * **Security Tests** - Vulnerability and attack vector testing
   * **Integration Tests** - Service interaction validation
   * **Load Tests** - Real-world usage simulation

⚡ **Performance Optimized**
   * **Fast Execution** - 36 tests in 0.06 seconds
   * **Parallel Testing** - Multi-worker test execution
   * **Efficient Fixtures** - Shared test setup and teardown
   * **Smart Caching** - Pytest cache for faster reruns

🛡️ **Quality Assured**
   * **90% Issue Reduction** - Comprehensive code quality improvements
   * **Zero Warnings** - Clean, modern Python patterns
   * **Type Safety** - Full PEP 484 compliance
   * **Security Hardened** - Vulnerability scanning and validation

Test Results Dashboard
=====================

.. list-table:: Current Test Status
   :widths: 25 15 15 25 20
   :header-rows: 1

   * - Test Type
     - Count
     - Status
     - Performance
     - Coverage
   * - Unit Tests
     - 36
     - ✅ PASSING
     - 0.06s
     - 95%+
   * - Property Tests
     - 9
     - ✅ PASSING
     - 1.02s
     - Edge Cases
   * - Performance Tests
     - Benchmarks
     - ✅ WORKING
     - 1.54M ops/s
     - Regression
   * - Security Tests
     - Multiple
     - ✅ CONFIGURED
     - Scanning
     - Vulnerabilities
   * - Code Quality
     - Ruff
     - ✅ CLEAN
     - Fast
     - 90% Fixed

Quick Commands
==============

.. code-block:: bash

   # Run all unit tests
   python -m pytest tests/unit/ -v

   # Run property-based tests
   python -m pytest tests/property/ -v

   # Run performance benchmarks
   python -m pytest tests/performance/ --benchmark-only

   # Run with coverage
   python -m pytest tests/unit/ --cov=api --cov=services --cov-report=html

   # Use modern test runner
   ./scripts/test_runner.sh help
   ./scripts/test_runner.sh all

   # Code quality checks
   ruff check . && ruff format .
   pre-commit run --all-files

Support & Contributing
=====================

.. admonition:: Need Help?
   :class: tip

   * 📖 Check the documentation sections above
   * 🐛 Report issues on GitHub
   * 💬 Join our development discussions
   * 🤝 Contribute improvements and fixes

.. admonition:: Contributing
   :class: note

   We welcome contributions! Please see our :doc:`contributing` guide for details on:
   
   * Setting up the development environment
   * Writing and running tests
   * Code quality standards
   * Submitting pull requests

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
