"""
Pytest configuration and shared fixtures for TurdParty tests.

This module provides common test fixtures and configuration for all test types.
Ensures PEP8, PEP257, and PEP484 compliance.
"""

from collections.abc import Generator
from datetime import UTC, datetime
import os
from pathlib import Path
import tempfile
from typing import Any, Dict
from unittest.mock import AsyncMock, MagicMock
import uuid

import pytest

# Try to import optional dependencies
try:
    from fastapi.testclient import TestClient
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    TestClient = None

try:
    from minio import Minio
    MINIO_AVAILABLE = True
except ImportError:
    MINIO_AVAILABLE = False
    Minio = None

try:
    from elasticsearch import AsyncElasticsearch
    ELASTICSEARCH_AVAILABLE = True
except ImportError:
    ELASTICSEARCH_AVAILABLE = False
    AsyncElasticsearch = None

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    redis = None

try:
    from api.v1.application import get_application
    APP_AVAILABLE = True
except ImportError:
    APP_AVAILABLE = False
    get_application = None


# Note: Removed deprecated event_loop fixture - pytest-asyncio handles this automatically


@pytest.fixture
def app():
    """Create a FastAPI application instance for testing."""
    if not APP_AVAILABLE:
        pytest.skip("FastAPI application not available")
    return get_application()


@pytest.fixture
def client(app):
    """Create a test client for the FastAPI application."""
    if not FASTAPI_AVAILABLE:
        pytest.skip("FastAPI TestClient not available")
    return TestClient(app)


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def sample_file_content() -> bytes:
    """Provide sample file content for testing."""
    return b"#!/bin/bash\necho 'Hello from test file'\n"


@pytest.fixture
def sample_script_file(temp_dir: Path, sample_file_content: bytes) -> Path:
    """Create a sample script file for testing."""
    script_file = temp_dir / "test_script.sh"
    script_file.write_bytes(sample_file_content)
    script_file.chmod(0o755)
    return script_file


@pytest.fixture
def mock_elk_logger() -> MagicMock:
    """Create a mock ELK logger for testing."""
    mock_logger = MagicMock()
    mock_logger.log_file_injection_event = AsyncMock()
    mock_logger.log_installation_base = AsyncMock()
    mock_logger.log_system_event = AsyncMock()
    return mock_logger


@pytest.fixture
def mock_file_injection_service() -> MagicMock:
    """Create a mock file injection service for testing."""
    mock_service = MagicMock()
    mock_service.create_injection = AsyncMock()
    mock_service.get_by_id = AsyncMock()
    mock_service.get_all = AsyncMock()
    mock_service.get_status = AsyncMock()
    mock_service.process_injection = AsyncMock()
    mock_service.delete = AsyncMock()
    return mock_service


@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch, temp_dir: Path) -> None:
    """Set up test environment variables."""
    monkeypatch.setenv("TEST_MODE", "true")
    monkeypatch.setenv("DEBUG", "true")
    monkeypatch.setenv("FILE_UPLOAD_DIR", str(temp_dir))
    monkeypatch.setenv("ELASTICSEARCH_HOST", "localhost")
    monkeypatch.setenv("ELASTICSEARCH_PORT", "9200")
    monkeypatch.setenv("LOGSTASH_HOST", "localhost")
    monkeypatch.setenv("LOGSTASH_PORT", "5000")


@pytest.fixture
def injection_data() -> dict:
    """Provide sample injection data for testing."""
    return {
        "filename": "test_script.sh",
        "target_path": "/app/scripts/test_script.sh",
        "permissions": "0755",
        "description": "Test script for unit testing",
    }


@pytest.fixture
def injection_response_data() -> dict:
    """Provide sample injection response data for testing."""
    return {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "filename": "test_script.sh",
        "target_path": "/app/scripts/test_script.sh",
        "permissions": "0755",
        "status": "pending",
        "description": "Test script for unit testing",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z",
        "file_size": 1024,
        "file_hash": "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3",
    }


@pytest.fixture
def injection_status_data() -> dict:
    """Provide sample injection status data for testing."""
    return {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "status": "completed",
        "progress": 100,
        "message": "File injection completed successfully",
        "details": {
            "file_path": "/tmp/test_script.sh",
            "target_path": "/app/scripts/test_script.sh",
            "permissions": "0755",
        },
        "updated_at": "2024-01-15T10:35:00Z",
    }


class AsyncContextManager:
    """Helper class for testing async context managers."""

    def __init__(self, return_value=None):
        """Initialize with optional return value."""
        self.return_value = return_value

    async def __aenter__(self):
        """Async enter method."""
        return self.return_value

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async exit method."""
        # Suppress unused parameter warnings
        _ = exc_type, exc_val, exc_tb


@pytest.fixture
def async_context_manager():
    """Provide an async context manager for testing."""
    return AsyncContextManager


@pytest.fixture
def mock_minio_client() -> MagicMock:
    """Create a mock MinIO client for testing."""
    mock_client = MagicMock(spec=Minio) if MINIO_AVAILABLE else MagicMock()
    mock_client.bucket_exists.return_value = True
    mock_client.put_object.return_value = MagicMock()
    mock_client.get_object.return_value = MagicMock()
    mock_client.remove_object.return_value = None
    mock_client.list_objects.return_value = []
    return mock_client


@pytest.fixture
def mock_elasticsearch_client() -> AsyncMock:
    """Create a mock Elasticsearch client for testing."""
    if ELASTICSEARCH_AVAILABLE:
        mock_client = AsyncMock(spec=AsyncElasticsearch)
    else:
        mock_client = AsyncMock()
    mock_client.index.return_value = {"_id": "test-doc-id", "result": "created"}
    mock_client.search.return_value = {
        "hits": {"total": {"value": 0}, "hits": []}
    }
    mock_client.indices.exists.return_value = True
    return mock_client


@pytest.fixture
def mock_redis_client() -> MagicMock:
    """Create a mock Redis client for testing."""
    mock_client = MagicMock(spec=redis.Redis) if REDIS_AVAILABLE else MagicMock()
    mock_client.ping.return_value = True
    mock_client.set.return_value = True
    mock_client.get.return_value = None
    mock_client.delete.return_value = 1
    return mock_client


@pytest.fixture
def mock_celery_app() -> MagicMock:
    """Create a mock Celery application for testing."""
    mock_app = MagicMock()
    mock_task = MagicMock()
    mock_task.delay.return_value = MagicMock(id="test-task-id")
    mock_app.send_task.return_value = mock_task.delay.return_value
    return mock_app


@pytest.fixture
def sample_file_data() -> dict[str, Any]:
    """Provide sample file data for testing."""
    return {
        "filename": "test_script.sh",
        "content": b"#!/bin/bash\necho 'Hello World'\n",
        "size": 28,
        "content_type": "application/x-sh",
        "hash": "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3"
    }


@pytest.fixture
def sample_vm_data() -> dict[str, Any]:
    """Provide sample VM data for testing."""
    return {
        "vm_id": "test-vm-123",
        "name": "test-vm",
        "status": "running",
        "ip_address": "*************",
        "memory": 1024,
        "cpus": 2,
        "created_at": datetime.now(UTC).isoformat()
    }


@pytest.fixture
def sample_workflow_data() -> dict[str, Any]:
    """Provide sample workflow data for testing."""
    return {
        "workflow_id": str(uuid.uuid4()),
        "file_id": str(uuid.uuid4()),
        "vm_id": "test-vm-123",
        "status": "pending",
        "steps": ["upload", "inject", "execute", "monitor"],
        "current_step": "upload"
    }


def load_test_config() -> Dict[str, Any]:
    """Load test configuration from environment and .env files."""
    config = {}

    # Load from integration test .env file if it exists
    integration_env = Path(__file__).parent / 'integration' / '.env.test'
    if integration_env.exists():
        try:
            from dotenv import load_dotenv
            load_dotenv(integration_env)
        except ImportError:
            # Parse .env file manually if python-dotenv not available
            with open(integration_env, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()

    # Load from main .env file if it exists
    env_file = Path(__file__).parent / '.env'
    if env_file.exists():
        try:
            from dotenv import load_dotenv
            load_dotenv(env_file)
        except ImportError:
            pass

    # Load configuration from environment
    config.update({
        'TEST_MODE': os.getenv('TEST_MODE', 'true').lower() == 'true',
        'DEBUG': os.getenv('DEBUG', 'false').lower() == 'true',
        'LOG_LEVEL': os.getenv('LOG_LEVEL', 'INFO'),
        'DATABASE_URL': os.getenv('DATABASE_URL', 'sqlite:///test.db'),
        'REDIS_URL': os.getenv('REDIS_URL', 'redis://localhost:6379/1'),
        'API_BASE_URL': os.getenv('API_BASE_URL', 'http://localhost:8000'),
        'API_VERSION': os.getenv('API_VERSION', 'v1'),
        'API_TIMEOUT': int(os.getenv('API_TIMEOUT', '300')),

        # MinIO Configuration
        'MINIO_ENDPOINT': os.getenv('MINIO_ENDPOINT', 'localhost:9000'),
        'MINIO_ACCESS_KEY': os.getenv('MINIO_ACCESS_KEY', 'minioadmin'),
        'MINIO_SECRET_KEY': os.getenv('MINIO_SECRET_KEY', 'minioadmin'),
        'MINIO_SECURE': os.getenv('MINIO_SECURE', 'false').lower() == 'true',
        'MINIO_TEST_BUCKET_PREFIX': os.getenv('MINIO_TEST_BUCKET_PREFIX', 'turdparty-test'),
        'MINIO_REGION': os.getenv('MINIO_REGION', 'us-east-1'),

        # ELK Configuration
        'ELASTICSEARCH_URL': os.getenv('ELASTICSEARCH_URL', 'http://localhost:9200'),
        'ELASTICSEARCH_INDEX_PREFIX': os.getenv('ELASTICSEARCH_INDEX_PREFIX', 'turdparty-test'),
        'ELASTICSEARCH_TIMEOUT': int(os.getenv('ELASTICSEARCH_TIMEOUT', '30')),
        'KIBANA_URL': os.getenv('KIBANA_URL', 'http://localhost:5601'),
        'LOGSTASH_HOST': os.getenv('LOGSTASH_HOST', 'localhost'),
        'LOGSTASH_PORT': int(os.getenv('LOGSTASH_PORT', '5044')),

        # VM Configuration
        'VAGRANT_GRPC_PORT': int(os.getenv('VAGRANT_GRPC_PORT', '40000')),
        'VM_CAPTURE_TIMEOUT_MINUTES': int(os.getenv('VM_CAPTURE_TIMEOUT_MINUTES', '5')),
        'VM_BOOT_TIMEOUT_MINUTES': int(os.getenv('VM_BOOT_TIMEOUT_MINUTES', '10')),
        'VM_TEARDOWN_TIMEOUT_MINUTES': int(os.getenv('VM_TEARDOWN_TIMEOUT_MINUTES', '5')),
        'VM_NETWORK_DISABLED': os.getenv('VM_NETWORK_DISABLED', 'true').lower() == 'true',
        'VM_MOUNTS_DISABLED': os.getenv('VM_MOUNTS_DISABLED', 'true').lower() == 'true',
        'USE_DOCKER_FALLBACK': os.getenv('USE_DOCKER_FALLBACK', 'true').lower() == 'true',

        # VirtualBox Configuration
        'VBOX_HEADLESS': os.getenv('VBOX_HEADLESS', 'true').lower() == 'true',
        'VBOX_MEMORY_MB': int(os.getenv('VBOX_MEMORY_MB', '2048')),
        'VBOX_CPUS': int(os.getenv('VBOX_CPUS', '2')),
        'VBOX_DISK_SIZE_GB': int(os.getenv('VBOX_DISK_SIZE_GB', '20')),

        # Test Data Configuration
        'TEST_DOWNLOAD_DIR': os.getenv('TEST_DOWNLOAD_DIR', '/tmp/turdparty-test-downloads'),
        'TEST_APPS_JSON': os.getenv('TEST_APPS_JSON', 'tests/data/test_applications.json'),
        'TEST_BENCHMARK_FILE': os.getenv('TEST_BENCHMARK_FILE', 'tests/data/benchmarks.json'),
        'TEST_CLEANUP_ENABLED': os.getenv('TEST_CLEANUP_ENABLED', 'true').lower() == 'true',

        # File Processing Configuration
        'BLAKE3_HASH_ENABLED': os.getenv('BLAKE3_HASH_ENABLED', 'true').lower() == 'true',
        'FILE_CHUNK_SIZE': int(os.getenv('FILE_CHUNK_SIZE', '8192')),
        'MAX_FILE_SIZE_MB': int(os.getenv('MAX_FILE_SIZE_MB', '2048')),
        'DOWNLOAD_TIMEOUT_SECONDS': int(os.getenv('DOWNLOAD_TIMEOUT_SECONDS', '300')),
        'UPLOAD_TIMEOUT_SECONDS': int(os.getenv('UPLOAD_TIMEOUT_SECONDS', '300')),

        # VM Test Configuration
        'UBUNTU_VM_COUNT': int(os.getenv('UBUNTU_VM_COUNT', '2')),
        'WINDOWS_VM_COUNT': int(os.getenv('WINDOWS_VM_COUNT', '2')),
        'UBUNTU_VM_IMAGE': os.getenv('UBUNTU_VM_IMAGE', 'ubuntu/focal64'),
        'WINDOWS_VM_IMAGE': os.getenv('WINDOWS_VM_IMAGE', 'gusztavvargadr/windows-10'),

        # Cleanup Configuration
        'AUTO_CLEANUP_DOWNLOADS': os.getenv('AUTO_CLEANUP_DOWNLOADS', 'true').lower() == 'true',
        'AUTO_CLEANUP_VMS': os.getenv('AUTO_CLEANUP_VMS', 'true').lower() == 'true',
        'AUTO_CLEANUP_MINIO': os.getenv('AUTO_CLEANUP_MINIO', 'true').lower() == 'true',
        'KEEP_FAILED_TEST_DATA': os.getenv('KEEP_FAILED_TEST_DATA', 'true').lower() == 'true',

        # Security Configuration
        'DISABLE_VM_NETWORKING': os.getenv('DISABLE_VM_NETWORKING', 'true').lower() == 'true',
        'DISABLE_VM_SHARED_FOLDERS': os.getenv('DISABLE_VM_SHARED_FOLDERS', 'true').lower() == 'true',
        'ENABLE_VM_ISOLATION': os.getenv('ENABLE_VM_ISOLATION', 'true').lower() == 'true',
        'SANDBOX_MODE': os.getenv('SANDBOX_MODE', 'true').lower() == 'true',
    })

    return config
