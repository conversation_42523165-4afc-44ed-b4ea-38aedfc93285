"""
Pytest configuration and shared fixtures for TurdParty tests.

This module provides common test fixtures and configuration for all test types.
Ensures PEP8, PEP257, and PEP484 compliance.
"""

import asyncio
import os
import tempfile
from pathlib import Path
from typing import AsyncGenerator, Generator
from unittest.mock import AsyncMock, MagicMock

import pytest
from fastapi.testclient import TestClient

from api.v1.application import get_application


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def app():
    """Create a FastAPI application instance for testing."""
    return get_application()


@pytest.fixture
def client(app) -> TestClient:
    """Create a test client for the FastAPI application."""
    return TestClient(app)


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def sample_file_content() -> bytes:
    """Provide sample file content for testing."""
    return b"#!/bin/bash\necho 'Hello from test file'\n"


@pytest.fixture
def sample_script_file(temp_dir: Path, sample_file_content: bytes) -> Path:
    """Create a sample script file for testing."""
    script_file = temp_dir / "test_script.sh"
    script_file.write_bytes(sample_file_content)
    script_file.chmod(0o755)
    return script_file


@pytest.fixture
def mock_elk_logger() -> MagicMock:
    """Create a mock ELK logger for testing."""
    mock_logger = MagicMock()
    mock_logger.log_file_injection_event = AsyncMock()
    mock_logger.log_installation_base = AsyncMock()
    mock_logger.log_system_event = AsyncMock()
    return mock_logger


@pytest.fixture
def mock_file_injection_service() -> MagicMock:
    """Create a mock file injection service for testing."""
    mock_service = MagicMock()
    mock_service.create_injection = AsyncMock()
    mock_service.get_by_id = AsyncMock()
    mock_service.get_all = AsyncMock()
    mock_service.get_status = AsyncMock()
    mock_service.process_injection = AsyncMock()
    mock_service.delete = AsyncMock()
    return mock_service


@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch, temp_dir: Path) -> None:
    """Set up test environment variables."""
    monkeypatch.setenv("TEST_MODE", "true")
    monkeypatch.setenv("DEBUG", "true")
    monkeypatch.setenv("FILE_UPLOAD_DIR", str(temp_dir))
    monkeypatch.setenv("ELASTICSEARCH_HOST", "localhost")
    monkeypatch.setenv("ELASTICSEARCH_PORT", "9200")
    monkeypatch.setenv("LOGSTASH_HOST", "localhost")
    monkeypatch.setenv("LOGSTASH_PORT", "5000")


@pytest.fixture
def injection_data() -> dict:
    """Provide sample injection data for testing."""
    return {
        "filename": "test_script.sh",
        "target_path": "/app/scripts/test_script.sh",
        "permissions": "0755",
        "description": "Test script for unit testing",
    }


@pytest.fixture
def injection_response_data() -> dict:
    """Provide sample injection response data for testing."""
    return {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "filename": "test_script.sh",
        "target_path": "/app/scripts/test_script.sh",
        "permissions": "0755",
        "status": "pending",
        "description": "Test script for unit testing",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z",
        "file_size": 1024,
        "file_hash": "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3",
    }


@pytest.fixture
def injection_status_data() -> dict:
    """Provide sample injection status data for testing."""
    return {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "status": "completed",
        "progress": 100,
        "message": "File injection completed successfully",
        "details": {
            "file_path": "/tmp/test_script.sh",
            "target_path": "/app/scripts/test_script.sh",
            "permissions": "0755",
        },
        "updated_at": "2024-01-15T10:35:00Z",
    }


class AsyncContextManager:
    """Helper class for testing async context managers."""

    def __init__(self, return_value=None):
        """Initialize with optional return value."""
        self.return_value = return_value

    async def __aenter__(self):
        """Async enter method."""
        return self.return_value

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async exit method."""
        return None


@pytest.fixture
def async_context_manager():
    """Provide an async context manager for testing."""
    return AsyncContextManager
