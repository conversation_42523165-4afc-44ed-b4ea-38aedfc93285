"""
Pytest configuration and shared fixtures for TurdParty tests.

This module provides common test fixtures and configuration for all test types.
Ensures PEP8, PEP257, and PEP484 compliance.
"""

from collections.abc import Generator
from datetime import UTC, datetime
from pathlib import Path
import tempfile
from typing import Any
from unittest.mock import AsyncMock, MagicMock
import uuid

import pytest

# Try to import optional dependencies
try:
    from fastapi.testclient import TestClient
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    TestClient = None

try:
    from minio import Minio
    MINIO_AVAILABLE = True
except ImportError:
    MINIO_AVAILABLE = False
    Minio = None

try:
    from elasticsearch import AsyncElasticsearch
    ELASTICSEARCH_AVAILABLE = True
except ImportError:
    ELASTICSEARCH_AVAILABLE = False
    AsyncElasticsearch = None

try:
    import redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    redis = None

try:
    from api.v1.application import get_application
    APP_AVAILABLE = True
except ImportError:
    APP_AVAILABLE = False
    get_application = None


# Note: Removed deprecated event_loop fixture - pytest-asyncio handles this automatically


@pytest.fixture
def app():
    """Create a FastAPI application instance for testing."""
    if not APP_AVAILABLE:
        pytest.skip("FastAPI application not available")
    return get_application()


@pytest.fixture
def client(app):
    """Create a test client for the FastAPI application."""
    if not FASTAPI_AVAILABLE:
        pytest.skip("FastAPI TestClient not available")
    return TestClient(app)


@pytest.fixture
def temp_dir() -> Generator[Path, None, None]:
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as temp_dir:
        yield Path(temp_dir)


@pytest.fixture
def sample_file_content() -> bytes:
    """Provide sample file content for testing."""
    return b"#!/bin/bash\necho 'Hello from test file'\n"


@pytest.fixture
def sample_script_file(temp_dir: Path, sample_file_content: bytes) -> Path:
    """Create a sample script file for testing."""
    script_file = temp_dir / "test_script.sh"
    script_file.write_bytes(sample_file_content)
    script_file.chmod(0o755)
    return script_file


@pytest.fixture
def mock_elk_logger() -> MagicMock:
    """Create a mock ELK logger for testing."""
    mock_logger = MagicMock()
    mock_logger.log_file_injection_event = AsyncMock()
    mock_logger.log_installation_base = AsyncMock()
    mock_logger.log_system_event = AsyncMock()
    return mock_logger


@pytest.fixture
def mock_file_injection_service() -> MagicMock:
    """Create a mock file injection service for testing."""
    mock_service = MagicMock()
    mock_service.create_injection = AsyncMock()
    mock_service.get_by_id = AsyncMock()
    mock_service.get_all = AsyncMock()
    mock_service.get_status = AsyncMock()
    mock_service.process_injection = AsyncMock()
    mock_service.delete = AsyncMock()
    return mock_service


@pytest.fixture(autouse=True)
def setup_test_environment(monkeypatch, temp_dir: Path) -> None:
    """Set up test environment variables."""
    monkeypatch.setenv("TEST_MODE", "true")
    monkeypatch.setenv("DEBUG", "true")
    monkeypatch.setenv("FILE_UPLOAD_DIR", str(temp_dir))
    monkeypatch.setenv("ELASTICSEARCH_HOST", "localhost")
    monkeypatch.setenv("ELASTICSEARCH_PORT", "9200")
    monkeypatch.setenv("LOGSTASH_HOST", "localhost")
    monkeypatch.setenv("LOGSTASH_PORT", "5000")


@pytest.fixture
def injection_data() -> dict:
    """Provide sample injection data for testing."""
    return {
        "filename": "test_script.sh",
        "target_path": "/app/scripts/test_script.sh",
        "permissions": "0755",
        "description": "Test script for unit testing",
    }


@pytest.fixture
def injection_response_data() -> dict:
    """Provide sample injection response data for testing."""
    return {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "filename": "test_script.sh",
        "target_path": "/app/scripts/test_script.sh",
        "permissions": "0755",
        "status": "pending",
        "description": "Test script for unit testing",
        "created_at": "2024-01-15T10:30:00Z",
        "updated_at": "2024-01-15T10:30:00Z",
        "file_size": 1024,
        "file_hash": "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3",
    }


@pytest.fixture
def injection_status_data() -> dict:
    """Provide sample injection status data for testing."""
    return {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "status": "completed",
        "progress": 100,
        "message": "File injection completed successfully",
        "details": {
            "file_path": "/tmp/test_script.sh",
            "target_path": "/app/scripts/test_script.sh",
            "permissions": "0755",
        },
        "updated_at": "2024-01-15T10:35:00Z",
    }


class AsyncContextManager:
    """Helper class for testing async context managers."""

    def __init__(self, return_value=None):
        """Initialize with optional return value."""
        self.return_value = return_value

    async def __aenter__(self):
        """Async enter method."""
        return self.return_value

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async exit method."""
        # Suppress unused parameter warnings
        _ = exc_type, exc_val, exc_tb


@pytest.fixture
def async_context_manager():
    """Provide an async context manager for testing."""
    return AsyncContextManager


@pytest.fixture
def mock_minio_client() -> MagicMock:
    """Create a mock MinIO client for testing."""
    mock_client = MagicMock(spec=Minio) if MINIO_AVAILABLE else MagicMock()
    mock_client.bucket_exists.return_value = True
    mock_client.put_object.return_value = MagicMock()
    mock_client.get_object.return_value = MagicMock()
    mock_client.remove_object.return_value = None
    mock_client.list_objects.return_value = []
    return mock_client


@pytest.fixture
def mock_elasticsearch_client() -> AsyncMock:
    """Create a mock Elasticsearch client for testing."""
    if ELASTICSEARCH_AVAILABLE:
        mock_client = AsyncMock(spec=AsyncElasticsearch)
    else:
        mock_client = AsyncMock()
    mock_client.index.return_value = {"_id": "test-doc-id", "result": "created"}
    mock_client.search.return_value = {
        "hits": {"total": {"value": 0}, "hits": []}
    }
    mock_client.indices.exists.return_value = True
    return mock_client


@pytest.fixture
def mock_redis_client() -> MagicMock:
    """Create a mock Redis client for testing."""
    mock_client = MagicMock(spec=redis.Redis) if REDIS_AVAILABLE else MagicMock()
    mock_client.ping.return_value = True
    mock_client.set.return_value = True
    mock_client.get.return_value = None
    mock_client.delete.return_value = 1
    return mock_client


@pytest.fixture
def mock_celery_app() -> MagicMock:
    """Create a mock Celery application for testing."""
    mock_app = MagicMock()
    mock_task = MagicMock()
    mock_task.delay.return_value = MagicMock(id="test-task-id")
    mock_app.send_task.return_value = mock_task.delay.return_value
    return mock_app


@pytest.fixture
def sample_file_data() -> dict[str, Any]:
    """Provide sample file data for testing."""
    return {
        "filename": "test_script.sh",
        "content": b"#!/bin/bash\necho 'Hello World'\n",
        "size": 28,
        "content_type": "application/x-sh",
        "hash": "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3"
    }


@pytest.fixture
def sample_vm_data() -> dict[str, Any]:
    """Provide sample VM data for testing."""
    return {
        "vm_id": "test-vm-123",
        "name": "test-vm",
        "status": "running",
        "ip_address": "*************",
        "memory": 1024,
        "cpus": 2,
        "created_at": datetime.now(UTC).isoformat()
    }


@pytest.fixture
def sample_workflow_data() -> dict[str, Any]:
    """Provide sample workflow data for testing."""
    return {
        "workflow_id": str(uuid.uuid4()),
        "file_id": str(uuid.uuid4()),
        "vm_id": "test-vm-123",
        "status": "pending",
        "steps": ["upload", "inject", "execute", "monitor"],
        "current_step": "upload"
    }
