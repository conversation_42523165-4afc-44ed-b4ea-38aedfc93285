"""
Load testing configuration using Locust for TurdParty application.

Simulates realistic user behavior and load patterns.
Ensures PEP8, PEP257, and PEP484 compliance.
"""

import random
import time
from typing import Dict, Any

from locust import HttpUser, task, between, events
from locust.runners import MasterRunner


class FileInjectionUser(HttpUser):
    """Simulates a user performing file injection operations."""
    
    wait_time = between(1, 3)  # Wait 1-3 seconds between tasks
    
    def on_start(self) -> None:
        """Called when a user starts."""
        self.injection_ids: list[str] = []
        self.user_id = random.randint(1000, 9999)
    
    @task(3)
    def check_health(self) -> None:
        """Check application health - most common operation."""
        with self.client.get("/health", catch_response=True) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "healthy":
                    response.success()
                else:
                    response.failure(f"Unhealthy status: {data}")
            else:
                response.failure(f"Health check failed: {response.status_code}")
    
    @task(2)
    def upload_file(self) -> None:
        """Upload a file for injection."""
        # Generate test file content
        file_content = f"#!/bin/bash\necho 'Test file from user {self.user_id}'\ndate\nexit 0\n"
        filename = f"test_user_{self.user_id}_{int(time.time())}.sh"
        
        files = {
            "file": (filename, file_content.encode(), "application/x-sh")
        }
        
        data = {
            "target_path": f"/app/scripts/{filename}",
            "permissions": "0755",
            "description": f"Load test file from user {self.user_id}"
        }
        
        with self.client.post(
            "/api/v1/file-injections/", 
            files=files, 
            data=data,
            catch_response=True
        ) as response:
            if response.status_code == 201:
                result = response.json()
                injection_id = result.get("id")
                if injection_id:
                    self.injection_ids.append(injection_id)
                    response.success()
                else:
                    response.failure("No injection ID returned")
            else:
                response.failure(f"Upload failed: {response.status_code}")
    
    @task(1)
    def list_injections(self) -> None:
        """List all file injections."""
        with self.client.get("/api/v1/file-injections/", catch_response=True) as response:
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list):
                    response.success()
                else:
                    response.failure("Invalid response format")
            else:
                response.failure(f"List failed: {response.status_code}")
    
    @task(1)
    def get_injection_details(self) -> None:
        """Get details of a specific injection."""
        if not self.injection_ids:
            return  # Skip if no injections created yet
        
        injection_id = random.choice(self.injection_ids)
        
        with self.client.get(
            f"/api/v1/file-injections/{injection_id}",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if data.get("id") == injection_id:
                    response.success()
                else:
                    response.failure("Injection ID mismatch")
            elif response.status_code == 404:
                # Remove non-existent injection from our list
                self.injection_ids.remove(injection_id)
                response.success()  # 404 is acceptable
            else:
                response.failure(f"Get details failed: {response.status_code}")
    
    @task(1)
    def check_injection_status(self) -> None:
        """Check status of a specific injection."""
        if not self.injection_ids:
            return  # Skip if no injections created yet
        
        injection_id = random.choice(self.injection_ids)
        
        with self.client.get(
            f"/api/v1/file-injections/{injection_id}/status",
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if "status" in data and "progress" in data:
                    response.success()
                else:
                    response.failure("Invalid status response")
            elif response.status_code == 404:
                # Remove non-existent injection from our list
                self.injection_ids.remove(injection_id)
                response.success()  # 404 is acceptable
            else:
                response.failure(f"Status check failed: {response.status_code}")


class AdminUser(HttpUser):
    """Simulates an admin user with different behavior patterns."""
    
    wait_time = between(2, 5)  # Admins are less frequent
    weight = 1  # Lower weight than regular users
    
    def on_start(self) -> None:
        """Called when an admin user starts."""
        self.admin_id = random.randint(100, 999)
    
    @task(5)
    def check_health_detailed(self) -> None:
        """Admin health check with more detailed analysis."""
        with self.client.get("/health", catch_response=True) as response:
            if response.status_code == 200:
                data = response.json()
                # Admins care about dependencies
                if "dependencies" in data:
                    response.success()
                else:
                    response.failure("Missing dependency information")
            else:
                response.failure(f"Health check failed: {response.status_code}")
    
    @task(3)
    def list_all_injections(self) -> None:
        """List all injections with pagination."""
        params = {
            "skip": random.randint(0, 50),
            "limit": random.randint(10, 50)
        }
        
        with self.client.get(
            "/api/v1/file-injections/",
            params=params,
            catch_response=True
        ) as response:
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list):
                    response.success()
                else:
                    response.failure("Invalid response format")
            else:
                response.failure(f"Admin list failed: {response.status_code}")
    
    @task(1)
    def bulk_status_check(self) -> None:
        """Check status of multiple injections."""
        # First get list of injections
        with self.client.get("/api/v1/file-injections/?limit=10") as list_response:
            if list_response.status_code == 200:
                injections = list_response.json()
                
                # Check status of each injection
                for injection in injections[:5]:  # Limit to 5 to avoid overload
                    injection_id = injection.get("id")
                    if injection_id:
                        self.client.get(f"/api/v1/file-injections/{injection_id}/status")


class HeavyUser(HttpUser):
    """Simulates a heavy user that uploads large files."""
    
    wait_time = between(5, 10)  # Longer wait times due to heavy operations
    weight = 1  # Lower weight due to resource intensity
    
    def on_start(self) -> None:
        """Called when a heavy user starts."""
        self.heavy_user_id = random.randint(10000, 99999)
    
    @task(1)
    def upload_large_file(self) -> None:
        """Upload a larger file for injection."""
        # Generate larger test file content
        base_content = f"#!/bin/bash\n# Heavy load test file from user {self.heavy_user_id}\n"
        
        # Add some bulk content
        for i in range(100):
            base_content += f"echo 'Line {i} from heavy user {self.heavy_user_id}'\n"
        
        base_content += "exit 0\n"
        
        filename = f"heavy_test_{self.heavy_user_id}_{int(time.time())}.sh"
        
        files = {
            "file": (filename, base_content.encode(), "application/x-sh")
        }
        
        data = {
            "target_path": f"/app/scripts/heavy/{filename}",
            "permissions": "0755",
            "description": f"Heavy load test file from user {self.heavy_user_id}"
        }
        
        with self.client.post(
            "/api/v1/file-injections/",
            files=files,
            data=data,
            catch_response=True,
            timeout=30  # Longer timeout for large files
        ) as response:
            if response.status_code == 201:
                response.success()
            else:
                response.failure(f"Heavy upload failed: {response.status_code}")


# Event handlers for custom metrics
@events.request.add_listener
def on_request(request_type: str, name: str, response_time: float, response_length: int, exception: Exception | None, **kwargs) -> None:
    """Custom request handler for metrics."""
    if exception:
        print(f"Request failed: {name} - {exception}")


@events.test_start.add_listener
def on_test_start(environment, **kwargs) -> None:
    """Called when test starts."""
    print("🚀 Load test starting...")
    print(f"Target host: {environment.host}")


@events.test_stop.add_listener
def on_test_stop(environment, **kwargs) -> None:
    """Called when test stops."""
    print("🏁 Load test completed!")
    
    # Print summary statistics
    stats = environment.stats
    print(f"Total requests: {stats.total.num_requests}")
    print(f"Total failures: {stats.total.num_failures}")
    print(f"Average response time: {stats.total.avg_response_time:.2f}ms")
    print(f"Max response time: {stats.total.max_response_time:.2f}ms")


# Custom user classes with different weights
class TurdPartyLoadTest(HttpUser):
    """Main load test configuration."""
    
    # Define user types and their weights
    tasks = {
        FileInjectionUser: 10,  # Most common users
        AdminUser: 2,           # Some admin users
        HeavyUser: 1,           # Few heavy users
    }
    
    wait_time = between(1, 5)


# Configuration for different load test scenarios
class SpikeTest(HttpUser):
    """Spike testing configuration."""
    
    wait_time = between(0.1, 0.5)  # Very short wait times
    
    @task
    def rapid_health_checks(self) -> None:
        """Rapid health checks for spike testing."""
        self.client.get("/health")


class SoakTest(HttpUser):
    """Soak testing configuration for long-duration tests."""
    
    wait_time = between(10, 30)  # Longer wait times for sustained load
    
    @task(5)
    def sustained_health_check(self) -> None:
        """Sustained health checks."""
        self.client.get("/health")
    
    @task(1)
    def sustained_file_upload(self) -> None:
        """Sustained file uploads."""
        file_content = "#!/bin/bash\necho 'Soak test'\nexit 0\n"
        filename = f"soak_test_{int(time.time())}.sh"
        
        files = {"file": (filename, file_content.encode(), "application/x-sh")}
        data = {
            "target_path": f"/app/soak/{filename}",
            "permissions": "0755"
        }
        
        self.client.post("/api/v1/file-injections/", files=files, data=data)
