"""
Enhanced unit tests for file injection service.

Comprehensive tests covering all service functionality, edge cases, and error scenarios.
Ensures PEP8, PEP257, and PEP484 compliance.
"""

import hashlib
from unittest.mock import MagicMock, mock_open, patch

import pytest

from api.models.file_injection import InjectionStatus
from api.services.file_injection_service import FileInjectionService


class TestFileInjectionServiceCore:
    """Test suite for core file injection service functionality."""

    @pytest.fixture
    def service(self) -> FileInjectionService:
        """Create a file injection service instance."""
        return FileInjectionService()

    @pytest.fixture
    def sample_file_content(self) -> bytes:
        """Provide sample file content for testing."""
        return b"#!/bin/bash\necho 'Hello World'\nexit 0\n"

    @pytest.fixture
    def sample_file_hash(self, sample_file_content: bytes) -> str:
        """Calculate hash of sample file content."""
        return hashlib.sha256(sample_file_content).hexdigest()

    async def test_create_injection_success(
        self,
        service: FileInjectionService,
        sample_file_content: bytes,
        sample_file_hash: str,
    ) -> None:
        """Test successful file injection creation."""
        # Arrange
        filename = "test_script.sh"
        target_path = "/app/scripts/test_script.sh"
        permissions = "0755"
        description = "Test script for unit testing"

        with patch("builtins.open", mock_open(read_data=sample_file_content)), \
             patch("os.path.exists", return_value=True), \
             patch("os.path.getsize", return_value=len(sample_file_content)), \
             patch("uuid.uuid4", return_value=MagicMock(hex="test-uuid-123")):

            # Act
            result = await service.create_injection(
                filename=filename,
                file_content=sample_file_content,
                target_path=target_path,
                permissions=permissions,
                description=description,
            )

        # Assert
        assert result.filename == filename
        assert result.target_path == target_path
        assert result.permissions == permissions
        assert result.status == InjectionStatus.PENDING
        assert result.file_size == len(sample_file_content)
        assert result.file_hash == sample_file_hash
        assert result.description == description

    async def test_create_injection_invalid_permissions(
        self,
        service: FileInjectionService,
        sample_file_content: bytes,
    ) -> None:
        """Test file injection creation with invalid permissions."""
        # Arrange
        filename = "test_script.sh"
        target_path = "/app/scripts/test_script.sh"
        invalid_permissions = "9999"  # Invalid octal

        # Act & Assert
        with pytest.raises(ValueError, match="Invalid permissions"):
            await service.create_injection(
                filename=filename,
                file_content=sample_file_content,
                target_path=target_path,
                permissions=invalid_permissions,
            )

    async def test_create_injection_path_traversal_protection(
        self,
        service: FileInjectionService,
        sample_file_content: bytes,
    ) -> None:
        """Test protection against path traversal attacks."""
        # Arrange
        filename = "test_script.sh"
        malicious_path = "../../../etc/passwd"

        # Act & Assert
        with pytest.raises(ValueError, match="Invalid target path"):
            await service.create_injection(
                filename=filename,
                file_content=sample_file_content,
                target_path=malicious_path,
                permissions="0755",
            )

    async def test_create_injection_empty_file(
        self,
        service: FileInjectionService,
    ) -> None:
        """Test file injection creation with empty file."""
        # Arrange
        filename = "empty_file.txt"
        target_path = "/app/empty_file.txt"
        empty_content = b""

        # Act & Assert
        with pytest.raises(ValueError, match="File content cannot be empty"):
            await service.create_injection(
                filename=filename,
                file_content=empty_content,
                target_path=target_path,
                permissions="0644",
            )

    async def test_get_by_id_success(
        self,
        service: FileInjectionService,
    ) -> None:
        """Test successful retrieval by ID."""
        # Arrange
        injection_id = "test-injection-123"
        mock_injection = MagicMock()
        mock_injection.id = injection_id
        mock_injection.filename = "test_script.sh"
        mock_injection.status = InjectionStatus.COMPLETED

        with patch.object(service, "_get_from_storage", return_value=mock_injection):
            # Act
            result = await service.get_by_id(injection_id)

        # Assert
        assert result is not None
        assert result.id == injection_id
        assert result.filename == "test_script.sh"
        assert result.status == InjectionStatus.COMPLETED

    async def test_get_by_id_not_found(
        self,
        service: FileInjectionService,
    ) -> None:
        """Test retrieval of non-existent injection."""
        # Arrange
        injection_id = "non-existent-id"

        with patch.object(service, "_get_from_storage", return_value=None):
            # Act
            result = await service.get_by_id(injection_id)

        # Assert
        assert result is None

    async def test_get_all_with_filters(
        self,
        service: FileInjectionService,
    ) -> None:
        """Test retrieval of all injections with filters."""
        # Arrange
        mock_injections = [
            MagicMock(id="1", status=InjectionStatus.PENDING),
            MagicMock(id="2", status=InjectionStatus.COMPLETED),
            MagicMock(id="3", status=InjectionStatus.FAILED),
        ]

        with patch.object(service, "_get_all_from_storage", return_value=mock_injections):
            # Act - filter by status
            result = await service.get_all(
                skip=0,
                limit=10,
                status_filter=InjectionStatus.PENDING
            )

        # Assert
        assert len(result) == 1
        assert result[0].status == InjectionStatus.PENDING

    async def test_get_all_with_pagination(
        self,
        service: FileInjectionService,
    ) -> None:
        """Test retrieval with pagination."""
        # Arrange
        mock_injections = [MagicMock(id=str(i)) for i in range(20)]

        with patch.object(service, "_get_all_from_storage", return_value=mock_injections):
            # Act
            result = await service.get_all(skip=5, limit=10)

        # Assert
        assert len(result) == 10
        # Verify pagination logic (would depend on actual implementation)

    async def test_update_status_success(
        self,
        service: FileInjectionService,
    ) -> None:
        """Test successful status update."""
        # Arrange
        injection_id = "test-injection-123"
        new_status = InjectionStatus.IN_PROGRESS
        progress = 50
        message = "Processing file"

        mock_injection = MagicMock()
        mock_injection.id = injection_id
        mock_injection.status = InjectionStatus.PENDING

        with patch.object(service, "_get_from_storage", return_value=mock_injection), \
             patch.object(service, "_save_to_storage") as mock_save:

            # Act
            result = await service.update_status(
                injection_id=injection_id,
                status=new_status,
                progress=progress,
                message=message,
            )

        # Assert
        assert result is not None
        assert result.status == new_status
        mock_save.assert_called_once()

    async def test_delete_success(
        self,
        service: FileInjectionService,
    ) -> None:
        """Test successful deletion."""
        # Arrange
        injection_id = "test-injection-123"
        mock_injection = MagicMock()
        mock_injection.id = injection_id

        with patch.object(service, "_get_from_storage", return_value=mock_injection), \
             patch.object(service, "_delete_from_storage") as mock_delete, \
             patch.object(service, "_cleanup_files") as mock_cleanup:

            # Act
            result = await service.delete(injection_id)

        # Assert
        assert result is True
        mock_delete.assert_called_once_with(injection_id)
        mock_cleanup.assert_called_once_with(mock_injection)

    async def test_delete_not_found(
        self,
        service: FileInjectionService,
    ) -> None:
        """Test deletion of non-existent injection."""
        # Arrange
        injection_id = "non-existent-id"

        with patch.object(service, "_get_from_storage", return_value=None):
            # Act
            result = await service.delete(injection_id)

        # Assert
        assert result is False


class TestFileInjectionServiceProcessing:
    """Test suite for file processing functionality."""

    @pytest.fixture
    def service(self) -> FileInjectionService:
        """Create a file injection service instance."""
        return FileInjectionService()

    async def test_process_injection_success(
        self,
        service: FileInjectionService,
    ) -> None:
        """Test successful file injection processing."""
        # Arrange
        injection_id = "test-injection-123"
        mock_injection = MagicMock()
        mock_injection.id = injection_id
        mock_injection.status = InjectionStatus.PENDING
        mock_injection.target_path = "/app/scripts/test_script.sh"
        mock_injection.permissions = "0755"

        with patch.object(service, "_get_from_storage", return_value=mock_injection), \
             patch.object(service, "_copy_file_to_target") as mock_copy, \
             patch.object(service, "_set_file_permissions") as mock_perms, \
             patch.object(service, "update_status") as mock_update:

            # Act
            result = await service.process_injection(injection_id)

        # Assert
        assert result is not None
        mock_copy.assert_called_once()
        mock_perms.assert_called_once()
        mock_update.assert_called()

    async def test_process_injection_already_processed(
        self,
        service: FileInjectionService,
    ) -> None:
        """Test processing of already completed injection."""
        # Arrange
        injection_id = "test-injection-123"
        mock_injection = MagicMock()
        mock_injection.id = injection_id
        mock_injection.status = InjectionStatus.COMPLETED

        with patch.object(service, "_get_from_storage", return_value=mock_injection):
            # Act & Assert
            with pytest.raises(ValueError, match="already processed"):
                await service.process_injection(injection_id)

    async def test_process_injection_file_copy_error(
        self,
        service: FileInjectionService,
    ) -> None:
        """Test processing with file copy error."""
        # Arrange
        injection_id = "test-injection-123"
        mock_injection = MagicMock()
        mock_injection.id = injection_id
        mock_injection.status = InjectionStatus.PENDING

        with patch.object(service, "_get_from_storage", return_value=mock_injection), \
             patch.object(service, "_copy_file_to_target", side_effect=OSError("Copy failed")), \
             patch.object(service, "update_status") as mock_update:

            # Act
            result = await service.process_injection(injection_id)

        # Assert
        assert result is not None
        # Should update status to failed
        mock_update.assert_called_with(
            injection_id,
            InjectionStatus.FAILED,
            progress=0,
            message="Copy failed"
        )


class TestFileInjectionServiceValidation:
    """Test suite for validation functionality."""

    @pytest.fixture
    def service(self) -> FileInjectionService:
        """Create a file injection service instance."""
        return FileInjectionService()

    def test_validate_permissions_valid(self, service: FileInjectionService) -> None:
        """Test validation of valid permissions."""
        valid_permissions = ["0644", "0755", "0777", "0600"]

        for perm in valid_permissions:
            # Should not raise exception
            service._validate_permissions(perm)

    def test_validate_permissions_invalid(self, service: FileInjectionService) -> None:
        """Test validation of invalid permissions."""
        invalid_permissions = ["9999", "abcd", "0888", ""]

        for perm in invalid_permissions:
            with pytest.raises(ValueError):
                service._validate_permissions(perm)

    def test_validate_target_path_valid(self, service: FileInjectionService) -> None:
        """Test validation of valid target paths."""
        valid_paths = [
            "/app/scripts/test.sh",
            "/opt/myapp/config.txt",
            "/usr/local/bin/script",
        ]

        for path in valid_paths:
            # Should not raise exception
            service._validate_target_path(path)

    def test_validate_target_path_invalid(self, service: FileInjectionService) -> None:
        """Test validation of invalid target paths."""
        invalid_paths = [
            "../../../etc/passwd",
            "/etc/passwd",
            "../../sensitive/file",
            "/root/.ssh/id_rsa",
        ]

        for path in invalid_paths:
            with pytest.raises(ValueError):
                service._validate_target_path(path)

    def test_validate_filename_valid(self, service: FileInjectionService) -> None:
        """Test validation of valid filenames."""
        valid_filenames = [
            "script.sh",
            "config.json",
            "app-config.yaml",
            "test_file.txt",
        ]

        for filename in valid_filenames:
            # Should not raise exception
            service._validate_filename(filename)

    def test_validate_filename_invalid(self, service: FileInjectionService) -> None:
        """Test validation of invalid filenames."""
        invalid_filenames = [
            "../script.sh",
            "file\x00name",
            "",
            "file with spaces",  # Depending on requirements
        ]

        for filename in invalid_filenames:
            with pytest.raises(ValueError):
                service._validate_filename(filename)


class TestFileInjectionServiceStorage:
    """Test suite for storage operations."""

    @pytest.fixture
    def service(self) -> FileInjectionService:
        """Create a file injection service instance."""
        return FileInjectionService()

    async def test_save_to_storage_success(
        self,
        service: FileInjectionService,
    ) -> None:
        """Test successful storage save operation."""
        # Arrange
        mock_injection = MagicMock()
        mock_injection.id = "test-injection-123"

        with patch("json.dump") as mock_dump, \
             patch("builtins.open", mock_open()) as mock_file:

            # Act
            await service._save_to_storage(mock_injection)

        # Assert
        mock_file.assert_called_once()
        mock_dump.assert_called_once()

    async def test_get_from_storage_success(
        self,
        service: FileInjectionService,
    ) -> None:
        """Test successful storage retrieval."""
        # Arrange
        injection_id = "test-injection-123"
        mock_data = {
            "id": injection_id,
            "filename": "test.sh",
            "status": "pending"
        }

        with patch("json.load", return_value=mock_data), \
             patch("builtins.open", mock_open()), \
             patch("os.path.exists", return_value=True):

            # Act
            result = await service._get_from_storage(injection_id)

        # Assert
        assert result is not None
        assert result.id == injection_id

    async def test_get_from_storage_not_found(
        self,
        service: FileInjectionService,
    ) -> None:
        """Test storage retrieval of non-existent item."""
        # Arrange
        injection_id = "non-existent-id"

        with patch("os.path.exists", return_value=False):
            # Act
            result = await service._get_from_storage(injection_id)

        # Assert
        assert result is None
