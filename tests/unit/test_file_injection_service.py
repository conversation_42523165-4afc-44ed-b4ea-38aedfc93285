"""
Unit tests for file injection service.

Tests the core file injection functionality ensuring PEP8, PEP257, and PEP484 compliance.
Adapted from the reference TurdParty repository.
"""

import asyncio
import hashlib
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Dict, Any
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from api.models.file_injection import (
    FileInjectionCreate,
    FileInjectionResponse,
    InjectionStatus,
)
from api.services.file_injection_service import FileInjectionService


class TestFileInjectionService:
    """Test suite for FileInjectionService."""

    @pytest.fixture
    def service(self, temp_dir: Path) -> FileInjectionService:
        """Create a FileInjectionService instance for testing."""
        with patch.dict("os.environ", {"FILE_UPLOAD_DIR": str(temp_dir)}):
            return FileInjectionService()

    @pytest.fixture
    def injection_create_data(self) -> FileInjectionCreate:
        """Create sample injection data."""
        return FileInjectionCreate(
            filename="test_script.sh",
            target_path="/app/scripts/test_script.sh",
            permissions="0755",
            description="Test script for unit testing",
        )

    @pytest.fixture
    def sample_file_content(self) -> bytes:
        """Provide sample file content."""
        return b"#!/bin/bash\necho 'Hello from test script'\nexit 0\n"

    async def test_create_injection_success(
        self,
        service: FileInjectionService,
        injection_create_data: FileInjectionCreate,
        sample_file_content: bytes,
    ) -> None:
        """Test successful file injection creation."""
        # Act
        result = await service.create_injection(injection_create_data, sample_file_content)

        # Assert
        assert isinstance(result, FileInjectionResponse)
        assert result.filename == injection_create_data.filename
        assert result.target_path == injection_create_data.target_path
        assert result.permissions == injection_create_data.permissions
        assert result.status == InjectionStatus.PENDING
        assert result.file_size == len(sample_file_content)
        assert result.file_hash == hashlib.sha256(sample_file_content).hexdigest()
        assert result.created_at is not None
        assert result.updated_at is not None

    async def test_create_injection_file_saved(
        self,
        service: FileInjectionService,
        injection_create_data: FileInjectionCreate,
        sample_file_content: bytes,
    ) -> None:
        """Test that file is properly saved during injection creation."""
        # Act
        result = await service.create_injection(injection_create_data, sample_file_content)

        # Assert
        file_path = service.upload_dir / f"{result.id}_{injection_create_data.filename}"
        assert file_path.exists()
        assert file_path.read_bytes() == sample_file_content

    async def test_get_by_id_existing(
        self,
        service: FileInjectionService,
        injection_create_data: FileInjectionCreate,
        sample_file_content: bytes,
    ) -> None:
        """Test retrieving an existing injection by ID."""
        # Arrange
        created = await service.create_injection(injection_create_data, sample_file_content)

        # Act
        result = await service.get_by_id(created.id)

        # Assert
        assert result is not None
        assert result.id == created.id
        assert result.filename == created.filename

    async def test_get_by_id_nonexistent(self, service: FileInjectionService) -> None:
        """Test retrieving a non-existent injection by ID."""
        # Act
        result = await service.get_by_id("nonexistent-id")

        # Assert
        assert result is None

    async def test_get_all_empty(self, service: FileInjectionService) -> None:
        """Test getting all injections when none exist."""
        # Act
        result = await service.get_all()

        # Assert
        assert result == []

    async def test_get_all_with_data(
        self,
        service: FileInjectionService,
        injection_create_data: FileInjectionCreate,
        sample_file_content: bytes,
    ) -> None:
        """Test getting all injections with existing data."""
        # Arrange
        injection1 = await service.create_injection(injection_create_data, sample_file_content)
        
        injection_data2 = FileInjectionCreate(
            filename="test2.sh",
            target_path="/app/test2.sh",
            permissions="0644",
            description="Second test",
        )
        injection2 = await service.create_injection(injection_data2, sample_file_content)

        # Act
        result = await service.get_all()

        # Assert
        assert len(result) == 2
        injection_ids = {inj.id for inj in result}
        assert injection1.id in injection_ids
        assert injection2.id in injection_ids

    async def test_get_all_with_pagination(
        self,
        service: FileInjectionService,
        injection_create_data: FileInjectionCreate,
        sample_file_content: bytes,
    ) -> None:
        """Test pagination in get_all method."""
        # Arrange
        for i in range(5):
            data = FileInjectionCreate(
                filename=f"test{i}.sh",
                target_path=f"/app/test{i}.sh",
                permissions="0755",
                description=f"Test {i}",
            )
            await service.create_injection(data, sample_file_content)

        # Act
        result = await service.get_all(skip=2, limit=2)

        # Assert
        assert len(result) == 2

    async def test_get_all_with_status_filter(
        self,
        service: FileInjectionService,
        injection_create_data: FileInjectionCreate,
        sample_file_content: bytes,
    ) -> None:
        """Test status filtering in get_all method."""
        # Arrange
        injection = await service.create_injection(injection_create_data, sample_file_content)
        
        # Manually update status for testing
        service._injections[injection.id]["status"] = InjectionStatus.COMPLETED

        # Act
        pending_result = await service.get_all(status_filter="pending")
        completed_result = await service.get_all(status_filter="completed")

        # Assert
        assert len(pending_result) == 0
        assert len(completed_result) == 1
        assert completed_result[0].id == injection.id

    async def test_get_status_existing(
        self,
        service: FileInjectionService,
        injection_create_data: FileInjectionCreate,
        sample_file_content: bytes,
    ) -> None:
        """Test getting status for an existing injection."""
        # Arrange
        injection = await service.create_injection(injection_create_data, sample_file_content)

        # Act
        result = await service.get_status(injection.id)

        # Assert
        assert result is not None
        assert result.id == injection.id
        assert result.status == InjectionStatus.PENDING
        assert result.progress == 0

    async def test_get_status_nonexistent(self, service: FileInjectionService) -> None:
        """Test getting status for a non-existent injection."""
        # Act
        result = await service.get_status("nonexistent-id")

        # Assert
        assert result is None

    async def test_process_injection_success(
        self,
        service: FileInjectionService,
        injection_create_data: FileInjectionCreate,
        sample_file_content: bytes,
    ) -> None:
        """Test successful injection processing."""
        # Arrange
        injection = await service.create_injection(injection_create_data, sample_file_content)

        # Act
        result = await service.process_injection(injection.id)

        # Assert
        assert result.status == InjectionStatus.COMPLETED
        assert result.progress == 100
        assert "completed successfully" in result.message
        assert result.details is not None

    async def test_process_injection_nonexistent(self, service: FileInjectionService) -> None:
        """Test processing a non-existent injection."""
        # Act & Assert
        with pytest.raises(ValueError, match="Injection .* not found"):
            await service.process_injection("nonexistent-id")

    async def test_process_injection_not_pending(
        self,
        service: FileInjectionService,
        injection_create_data: FileInjectionCreate,
        sample_file_content: bytes,
    ) -> None:
        """Test processing an injection that is not in pending status."""
        # Arrange
        injection = await service.create_injection(injection_create_data, sample_file_content)
        service._injections[injection.id]["status"] = InjectionStatus.COMPLETED

        # Act & Assert
        with pytest.raises(ValueError, match="not in pending status"):
            await service.process_injection(injection.id)

    async def test_delete_existing(
        self,
        service: FileInjectionService,
        injection_create_data: FileInjectionCreate,
        sample_file_content: bytes,
    ) -> None:
        """Test deleting an existing injection."""
        # Arrange
        injection = await service.create_injection(injection_create_data, sample_file_content)
        file_path = service.upload_dir / f"{injection.id}_{injection_create_data.filename}"
        assert file_path.exists()

        # Act
        await service.delete(injection.id)

        # Assert
        assert not file_path.exists()
        assert injection.id not in service._injections

    async def test_delete_nonexistent(self, service: FileInjectionService) -> None:
        """Test deleting a non-existent injection."""
        # Act & Assert
        with pytest.raises(ValueError, match="Injection .* not found"):
            await service.delete("nonexistent-id")

    async def test_simulate_processing_steps(
        self,
        service: FileInjectionService,
        injection_create_data: FileInjectionCreate,
        sample_file_content: bytes,
    ) -> None:
        """Test the processing simulation steps."""
        # Arrange
        injection = await service.create_injection(injection_create_data, sample_file_content)

        # Act
        await service._simulate_processing(injection.id)

        # Assert
        injection_data = service._injections[injection.id]
        assert "details" in injection_data
        assert injection_data["details"]["total_steps"] == 4
