import os

import requests

# Create a test file
with open("test.txt", "w") as f:
    f.write("Test file content")

# Upload the file
url = "http://localhost:8000/api/v1/file_upload/"
files = {"file": open("test.txt", "rb")}
data = {"description": "Test file"}

response = requests.post(url, files=files, data=data)
print(f"Status code: {response.status_code}")
print(f"Response: {response.text}")

# Clean up
os.remove("test.txt")
