"""
End-to-end tests for complete TurdParty workflow.

Tests the complete user journey from file upload through VM processing
to ELK analysis using Playwright. Ensures PEP8, PEP257, and PEP484 compliance.
"""

import asyncio
import os
import tempfile
from pathlib import Path
from typing import Dict, Any

import pytest
from playwright.async_api import async_playwright, <PERSON>, <PERSON><PERSON><PERSON>, BrowserContext


@pytest.mark.skipif(
    os.environ.get("IN_DOCKER") != "true",
    reason="E2E tests should only run in Docker"
)
class TestCompleteWorkflow:
    """End-to-end test suite for complete TurdParty workflow."""

    @pytest.fixture
    async def browser_context(self) -> BrowserContext:
        """Create a browser context for testing."""
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context()
            yield context
            await context.close()
            await browser.close()

    @pytest.fixture
    async def page(self, browser_context: BrowserContext) -> Page:
        """Create a page for testing."""
        page = await browser_context.new_page()
        yield page
        await page.close()

    @pytest.fixture
    def test_file(self) -> Path:
        """Create a test file for upload."""
        content = """#!/bin/bash
echo "E2E test script"
echo "Timestamp: $(date)"
echo "Hostname: $(hostname)"
echo "Current user: $(whoami)"
echo "Working directory: $(pwd)"
ls -la /app
exit 0
"""
        temp_file = Path(tempfile.gettempdir()) / "e2e_test_script.sh"
        temp_file.write_text(content)
        yield temp_file
        temp_file.unlink(missing_ok=True)

    @pytest.fixture
    def app_url(self) -> str:
        """Get the application URL for testing."""
        return os.getenv("APP_URL", "http://frontend:3000")

    @pytest.fixture
    def api_url(self) -> str:
        """Get the API URL for testing."""
        return os.getenv("API_URL", "http://api:8000")

    async def test_file_upload_workflow(
        self,
        page: Page,
        app_url: str,
        test_file: Path,
    ) -> None:
        """Test the complete file upload workflow through the UI."""
        # Navigate to the application
        await page.goto(app_url)
        
        # Wait for the page to load
        await page.wait_for_selector('[data-testid="file-upload-form"]', timeout=10000)
        
        # Upload a file
        file_input = page.locator('input[type="file"]')
        await file_input.set_input_files(str(test_file))
        
        # Set target path
        target_path_input = page.locator('[data-testid="target-path-input"]')
        await target_path_input.fill("/app/scripts/e2e_test_script.sh")
        
        # Set permissions
        permissions_input = page.locator('[data-testid="permissions-input"]')
        await permissions_input.fill("0755")
        
        # Add description
        description_input = page.locator('[data-testid="description-input"]')
        await description_input.fill("E2E test script upload")
        
        # Submit the form
        submit_button = page.locator('[data-testid="upload-submit-button"]')
        await submit_button.click()
        
        # Wait for success message
        success_message = page.locator('[data-testid="upload-success-message"]')
        await success_message.wait_for(timeout=30000)
        
        # Verify success message content
        success_text = await success_message.text_content()
        assert "successfully uploaded" in success_text.lower()
        
        # Extract injection ID from the success message
        injection_id_element = page.locator('[data-testid="injection-id"]')
        injection_id = await injection_id_element.text_content()
        assert injection_id is not None
        assert len(injection_id.strip()) > 0

    async def test_injection_status_monitoring(
        self,
        page: Page,
        app_url: str,
        test_file: Path,
    ) -> None:
        """Test monitoring injection status through the UI."""
        # First upload a file (reuse upload workflow)
        await self.test_file_upload_workflow(page, app_url, test_file)
        
        # Navigate to injections list
        injections_link = page.locator('[data-testid="injections-nav-link"]')
        await injections_link.click()
        
        # Wait for injections list to load
        await page.wait_for_selector('[data-testid="injections-list"]', timeout=10000)
        
        # Find the most recent injection
        first_injection = page.locator('[data-testid="injection-item"]').first
        await first_injection.wait_for(timeout=5000)
        
        # Click on the injection to view details
        await first_injection.click()
        
        # Wait for injection details page
        await page.wait_for_selector('[data-testid="injection-details"]', timeout=10000)
        
        # Verify injection details are displayed
        status_element = page.locator('[data-testid="injection-status"]')
        status_text = await status_element.text_content()
        assert status_text in ["pending", "in_progress", "completed", "failed"]
        
        # Check for progress indicator
        progress_element = page.locator('[data-testid="injection-progress"]')
        await progress_element.wait_for(timeout=5000)
        
        # Verify file details are shown
        filename_element = page.locator('[data-testid="injection-filename"]')
        filename = await filename_element.text_content()
        assert "e2e_test_script.sh" in filename

    async def test_process_injection_workflow(
        self,
        page: Page,
        app_url: str,
        test_file: Path,
    ) -> None:
        """Test processing an injection through the UI."""
        # Upload a file first
        await self.test_file_upload_workflow(page, app_url, test_file)
        
        # Navigate to injection details
        await self.test_injection_status_monitoring(page, app_url, test_file)
        
        # Find and click the process button
        process_button = page.locator('[data-testid="process-injection-button"]')
        
        # Check if button is enabled (injection should be in pending status)
        is_enabled = await process_button.is_enabled()
        if is_enabled:
            await process_button.click()
            
            # Wait for processing confirmation
            confirmation_dialog = page.locator('[data-testid="process-confirmation-dialog"]')
            await confirmation_dialog.wait_for(timeout=5000)
            
            # Confirm processing
            confirm_button = page.locator('[data-testid="confirm-process-button"]')
            await confirm_button.click()
            
            # Wait for status update
            await page.wait_for_timeout(2000)
            
            # Verify status changed
            status_element = page.locator('[data-testid="injection-status"]')
            updated_status = await status_element.text_content()
            assert updated_status in ["in_progress", "completed"]

    async def test_elk_logs_viewing(
        self,
        page: Page,
        app_url: str,
        test_file: Path,
    ) -> None:
        """Test viewing ELK logs through the UI."""
        # Upload and process a file first
        await self.test_process_injection_workflow(page, app_url, test_file)
        
        # Navigate to logs section
        logs_link = page.locator('[data-testid="logs-nav-link"]')
        await logs_link.click()
        
        # Wait for logs page to load
        await page.wait_for_selector('[data-testid="logs-container"]', timeout=10000)
        
        # Search for logs related to our injection
        search_input = page.locator('[data-testid="logs-search-input"]')
        await search_input.fill("e2e_test_script.sh")
        
        search_button = page.locator('[data-testid="logs-search-button"]')
        await search_button.click()
        
        # Wait for search results
        await page.wait_for_selector('[data-testid="log-entries"]', timeout=15000)
        
        # Verify log entries are displayed
        log_entries = page.locator('[data-testid="log-entry"]')
        entry_count = await log_entries.count()
        assert entry_count > 0
        
        # Check first log entry contains expected information
        first_entry = log_entries.first
        entry_text = await first_entry.text_content()
        assert "injection_created" in entry_text or "e2e_test_script.sh" in entry_text

    async def test_error_handling_invalid_file(
        self,
        page: Page,
        app_url: str,
    ) -> None:
        """Test error handling for invalid file uploads."""
        # Navigate to the application
        await page.goto(app_url)
        
        # Wait for the page to load
        await page.wait_for_selector('[data-testid="file-upload-form"]', timeout=10000)
        
        # Create an invalid file (too large or wrong type)
        invalid_file = Path(tempfile.gettempdir()) / "invalid_file.exe"
        invalid_file.write_bytes(b"MZ" + b"x" * 10000000)  # Large fake executable
        
        try:
            # Try to upload the invalid file
            file_input = page.locator('input[type="file"]')
            await file_input.set_input_files(str(invalid_file))
            
            # Set other required fields
            target_path_input = page.locator('[data-testid="target-path-input"]')
            await target_path_input.fill("/app/invalid_file.exe")
            
            # Submit the form
            submit_button = page.locator('[data-testid="upload-submit-button"]')
            await submit_button.click()
            
            # Wait for error message
            error_message = page.locator('[data-testid="upload-error-message"]')
            await error_message.wait_for(timeout=10000)
            
            # Verify error message is displayed
            error_text = await error_message.text_content()
            assert "error" in error_text.lower() or "invalid" in error_text.lower()
            
        finally:
            invalid_file.unlink(missing_ok=True)

    async def test_responsive_design(
        self,
        browser_context: BrowserContext,
        app_url: str,
    ) -> None:
        """Test responsive design on different screen sizes."""
        # Test mobile viewport
        mobile_page = await browser_context.new_page()
        await mobile_page.set_viewport_size({"width": 375, "height": 667})
        await mobile_page.goto(app_url)
        
        # Verify mobile navigation
        mobile_menu = mobile_page.locator('[data-testid="mobile-menu-button"]')
        if await mobile_menu.is_visible():
            await mobile_menu.click()
            
            # Check if navigation items are visible
            nav_items = mobile_page.locator('[data-testid="mobile-nav-item"]')
            nav_count = await nav_items.count()
            assert nav_count > 0
        
        await mobile_page.close()
        
        # Test tablet viewport
        tablet_page = await browser_context.new_page()
        await tablet_page.set_viewport_size({"width": 768, "height": 1024})
        await tablet_page.goto(app_url)
        
        # Verify tablet layout
        main_content = tablet_page.locator('[data-testid="main-content"]')
        await main_content.wait_for(timeout=5000)
        
        # Check if content is properly displayed
        is_visible = await main_content.is_visible()
        assert is_visible
        
        await tablet_page.close()

    async def test_accessibility_compliance(
        self,
        page: Page,
        app_url: str,
    ) -> None:
        """Test basic accessibility compliance."""
        await page.goto(app_url)
        
        # Wait for page to load
        await page.wait_for_load_state("networkidle")
        
        # Check for proper heading structure
        h1_elements = page.locator("h1")
        h1_count = await h1_elements.count()
        assert h1_count >= 1  # Should have at least one main heading
        
        # Check for alt text on images
        images = page.locator("img")
        image_count = await images.count()
        
        for i in range(image_count):
            img = images.nth(i)
            alt_text = await img.get_attribute("alt")
            # Images should have alt text or be decorative
            assert alt_text is not None or await img.get_attribute("role") == "presentation"
        
        # Check for form labels
        form_inputs = page.locator("input[type='text'], input[type='file'], textarea, select")
        input_count = await form_inputs.count()
        
        for i in range(input_count):
            input_element = form_inputs.nth(i)
            
            # Check if input has associated label
            input_id = await input_element.get_attribute("id")
            if input_id:
                label = page.locator(f"label[for='{input_id}']")
                label_exists = await label.count() > 0
                
                # Also check for aria-label or aria-labelledby
                aria_label = await input_element.get_attribute("aria-label")
                aria_labelledby = await input_element.get_attribute("aria-labelledby")
                
                assert label_exists or aria_label or aria_labelledby

    async def test_performance_metrics(
        self,
        page: Page,
        app_url: str,
    ) -> None:
        """Test basic performance metrics."""
        # Navigate to the application and measure load time
        start_time = asyncio.get_event_loop().time()
        await page.goto(app_url)
        await page.wait_for_load_state("networkidle")
        end_time = asyncio.get_event_loop().time()
        
        load_time = end_time - start_time
        
        # Page should load within reasonable time (adjust threshold as needed)
        assert load_time < 10.0, f"Page load time {load_time:.2f}s exceeds threshold"
        
        # Check for console errors
        console_errors = []
        
        def handle_console(msg):
            if msg.type == "error":
                console_errors.append(msg.text)
        
        page.on("console", handle_console)
        
        # Interact with the page to trigger any console errors
        await page.reload()
        await page.wait_for_load_state("networkidle")
        
        # Should have minimal console errors
        assert len(console_errors) == 0, f"Console errors found: {console_errors}"
