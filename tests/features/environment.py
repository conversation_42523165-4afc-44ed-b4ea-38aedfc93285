"""
Behave environment configuration for TurdParty BDD tests.

Sets up test environment and provides common fixtures ensuring PEP8, PEP257, and PEP484 compliance.
"""

import os
from pathlib import Path
import tempfile
from typing import Any

from behave.runner import Context


def before_all(context: Context) -> None:
    """Set up the test environment before all tests."""
    # Configure API base URL
    context.api_base_url = os.getenv("API_BASE_URL", "http://localhost:8000")

    # Configure ELK stack URLs
    context.elasticsearch_url = os.getenv("ELASTICSEARCH_URL", "http://localhost:9200")
    context.kibana_url = os.getenv("KIBANA_URL", "http://localhost:5601")
    context.logstash_url = os.getenv("LOGSTASH_URL", "http://localhost:5000")

    # Set up temporary directory for test files
    context.temp_dir = Path(tempfile.mkdtemp(prefix="turdparty_bdd_"))

    # Initialize test data containers
    context.test_files = {}
    context.injections = {}
    context.responses = []

    print("BDD Test Environment Setup:")
    print(f"  API Base URL: {context.api_base_url}")
    print(f"  Elasticsearch URL: {context.elasticsearch_url}")
    print(f"  Temp Directory: {context.temp_dir}")


def after_all(context: Context) -> None:
    """Clean up the test environment after all tests."""
    # Clean up temporary files
    if hasattr(context, 'temp_dir') and context.temp_dir.exists():
        import shutil
        shutil.rmtree(context.temp_dir)
        print(f"Cleaned up temporary directory: {context.temp_dir}")


def before_scenario(context: Context, scenario: Any) -> None:
    """Set up before each scenario."""
    # Reset scenario-specific data
    context.test_files = {}
    context.injections = {}
    context.response = None
    context.responses = []
    context.concurrent_responses = []
    context.elk_response = None

    # Set scenario-specific flags
    context.api_available = False
    context.elk_available = False
    context.invalid_file = False

    print(f"\nStarting scenario: {scenario.name}")


def after_scenario(context: Context, scenario: Any) -> None:
    """Clean up after each scenario."""
    # Clean up any test files created during the scenario
    for filename, file_path in context.test_files.items():
        if file_path.exists():
            file_path.unlink()

    # Clean up any injections created during the scenario
    if hasattr(context, 'injections') and context.api_available:
        import requests
        for injection_name, injection_id in context.injections.items():
            try:
                requests.delete(f"{context.api_base_url}/api/v1/file_injection/{injection_id}")
            except Exception:
                pass  # Ignore cleanup errors

    print(f"Completed scenario: {scenario.name} - {'PASSED' if scenario.status == 'passed' else 'FAILED'}")


def before_step(context: Context, step: Any) -> None:
    """Set up before each step."""
    # Log step execution for debugging
    if os.getenv("BDD_DEBUG", "false").lower() == "true":
        print(f"  Executing step: {step.step_type} {step.name}")


def after_step(context: Context, step: Any) -> None:
    """Clean up after each step."""
    # Log step completion for debugging
    if os.getenv("BDD_DEBUG", "false").lower() == "true":
        status = "PASSED" if step.status == "passed" else "FAILED"
        print(f"  Step completed: {status}")

        # Log response details if available
        if hasattr(context, 'response') and context.response:
            print(f"    Response status: {context.response.status_code}")


def before_tag(context: Context, tag: str) -> None:
    """Handle specific tags before scenarios."""
    if tag == "slow":
        # Increase timeouts for slow tests
        context.timeout = 60
    elif tag == "integration":
        # Ensure services are available for integration tests
        context.require_services = True
    elif tag == "elk":
        # Ensure ELK stack is available
        context.require_elk = True


def after_tag(context: Context, tag: str) -> None:
    """Handle specific tags after scenarios."""
    if tag == "cleanup":
        # Perform additional cleanup for tagged scenarios
        pass
