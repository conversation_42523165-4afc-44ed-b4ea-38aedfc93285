"""
Behave step definitions for file injection feature.

Implements BDD steps ensuring PEP8, PEP257, and PEP484 compliance.
"""

from pathlib import Path
import tempfile

from behave import given, then, when
from behave.runner import Context
import requests


@given('the TurdParty API is running')
def step_api_running(context: Context) -> None:
    """Verify that the TurdParty API is accessible."""
    try:
        response = requests.get(f"{context.api_base_url}/health", timeout=10)
        assert response.status_code == 200
        context.api_available = True
    except Exception as e:
        context.api_available = False
        raise AssertionError(f"API is not running: {e}")


@given('the ELK stack is available')
def step_elk_available(context: Context) -> None:
    """Verify that the ELK stack is accessible."""
    try:
        # Check Elasticsearch
        response = requests.get(f"{context.elasticsearch_url}/_cluster/health", timeout=10)
        assert response.status_code == 200
        context.elk_available = True
    except Exception as e:
        context.elk_available = False
        raise AssertionError(f"ELK stack is not available: {e}")


@given('I have a shell script file "{filename}"')
def step_create_shell_script(context: Context, filename: str) -> None:
    """Create a shell script file for testing."""
    script_content = """#!/bin/bash
echo "Hello from TurdParty deployment script"
echo "Current time: $(date)"
echo "Current user: $(whoami)"
exit 0
"""
    temp_file = Path(tempfile.gettempdir()) / filename
    temp_file.write_text(script_content)
    context.test_files = getattr(context, 'test_files', {})
    context.test_files[filename] = temp_file


@given('I have a file "{filename}"')
def step_create_file(context: Context, filename: str) -> None:
    """Create a generic test file."""
    content = f"Test file content for {filename}\nCreated for TurdParty testing\n"
    temp_file = Path(tempfile.gettempdir()) / filename
    temp_file.write_text(content)
    context.test_files = getattr(context, 'test_files', {})
    context.test_files[filename] = temp_file


@given('I have an invalid file')
def step_create_invalid_file(context: Context) -> None:
    """Create an invalid file for testing error handling."""
    # Create a file that's too large or has invalid content
    context.invalid_file = True


@given('I have created a file injection with ID "{injection_id}"')
def step_create_injection_with_id(context: Context, injection_id: str) -> None:
    """Create a file injection with a specific ID for testing."""
    # Create a test file first
    test_file = Path(tempfile.gettempdir()) / "test_injection.txt"
    test_file.write_text("Test content for injection")

    # Upload the file
    with open(test_file, 'rb') as f:
        files = {'file': ('test_injection.txt', f, 'text/plain')}
        data = {
            'target_path': '/app/test_injection.txt',
            'permissions': '0644',
            'description': f'Test injection {injection_id}'
        }
        response = requests.post(
            f"{context.api_base_url}/api/v1/file_injection/",
            files=files,
            data=data
        )

    assert response.status_code == 201
    result = response.json()

    # Store the actual injection ID
    context.injections = getattr(context, 'injections', {})
    context.injections[injection_id] = result['id']


@given('the injection status is "{status}"')
def step_verify_injection_status(context: Context, status: str) -> None:
    """Verify that an injection has a specific status."""
    # This step assumes the injection was created in a previous step
    # In a real scenario, you might need to wait or poll for the status
    context.expected_status = status


@given('I have multiple files to inject')
def step_have_multiple_files(context: Context) -> None:
    """Create multiple files for concurrent injection testing."""
    context.test_files = {}
    for i in range(5):
        filename = f"concurrent_test_{i}.sh"
        content = f"""#!/bin/bash
echo "Concurrent test script {i}"
echo "Process ID: $$"
exit 0
"""
        temp_file = Path(tempfile.gettempdir()) / filename
        temp_file.write_text(content)
        context.test_files[filename] = temp_file


@given('I have uploaded a file for injection')
def step_have_uploaded_file(context: Context) -> None:
    """Upload a file for injection as a prerequisite."""
    # Create a test file
    filename = "prerequisite_test.sh"
    content = "#!/bin/bash\necho 'Prerequisite test'\nexit 0\n"
    temp_file = Path(tempfile.gettempdir()) / filename
    temp_file.write_text(content)

    with open(temp_file, 'rb') as f:
        files = {'file': (filename, f, 'application/x-sh')}
        data = {
            'target_path': '/app/scripts/prerequisite_test.sh',
            'permissions': '0755',
            'description': 'Prerequisite file for testing'
        }

        response = requests.post(
            f"{context.api_base_url}/api/v1/file_injection/",
            files=files,
            data=data,
            timeout=30
        )

    assert response.status_code == 201
    context.injection_data = response.json()
    context.injection_id = context.injection_data['id']


@given('the file is stored in MinIO')
def step_file_stored_in_minio(context: Context) -> None:
    """Verify that the file is stored in MinIO."""
    # This would typically check MinIO directly, but for now we'll assume
    # the file upload process includes MinIO storage
    assert hasattr(context, 'injection_id')
    context.file_in_minio = True


@given('I have a file injection request')
def step_have_injection_request(context: Context) -> None:
    """Create a file injection request for VM lifecycle testing."""
    filename = "vm_lifecycle_test.sh"
    content = "#!/bin/bash\necho 'VM lifecycle test'\nsleep 10\nexit 0\n"
    temp_file = Path(tempfile.gettempdir()) / filename
    temp_file.write_text(content)

    context.test_files = {filename: temp_file}
    context.injection_request = {
        'filename': filename,
        'target_path': '/app/scripts/vm_lifecycle_test.sh',
        'permissions': '0755'
    }


@given('I have a test script "{filename}"')
def step_have_test_script(context: Context, filename: str) -> None:
    """Create a test script for end-to-end testing."""
    content = f"""#!/bin/bash
echo "End-to-end test script: {filename}"
echo "Timestamp: $(date)"
echo "Hostname: $(hostname)"
echo "User: $(whoami)"
echo "Working directory: $(pwd)"
ls -la
exit 0
"""
    temp_file = Path(tempfile.gettempdir()) / filename
    temp_file.write_text(content)
    context.test_files = {filename: temp_file}


@given('I have created {count:d} file injections')
def step_create_multiple_injections(context: Context, count: int) -> None:
    """Create multiple file injections for testing."""
    context.injections = getattr(context, 'injections', {})

    for i in range(count):
        # Create test file
        test_file = Path(tempfile.gettempdir()) / f"test_{i}.txt"
        test_file.write_text(f"Test content {i}")

        # Upload the file
        with open(test_file, 'rb') as f:
            files = {'file': (f'test_{i}.txt', f, 'text/plain')}
            data = {
                'target_path': f'/app/test_{i}.txt',
                'permissions': '0644',
                'description': f'Test injection {i}'
            }
            response = requests.post(
                f"{context.api_base_url}/api/v1/file_injection/",
                files=files,
                data=data
            )

        assert response.status_code == 201
        result = response.json()
        context.injections[f"injection_{i}"] = result['id']


@given('I have created injections with different statuses')
def step_create_injections_different_statuses(context: Context) -> None:
    """Create injections and set them to different statuses."""
    context.injections = getattr(context, 'injections', {})

    # Create pending injection
    step_create_injection_with_id(context, "pending_injection")

    # Create and process completed injection
    step_create_injection_with_id(context, "completed_injection")
    completed_id = context.injections["completed_injection"]
    requests.post(f"{context.api_base_url}/api/v1/file_injection/{completed_id}/process")


@given('there is no injection with ID "{injection_id}"')
def step_verify_no_injection(context: Context, injection_id: str) -> None:
    """Verify that an injection with the given ID does not exist."""
    response = requests.get(f"{context.api_base_url}/api/v1/file_injection/{injection_id}")
    assert response.status_code == 404


@given('I have created and processed a file injection')
def step_create_and_process_injection(context: Context) -> None:
    """Create and process a file injection for ELK testing."""
    step_create_injection_with_id(context, "elk_test_injection")
    injection_id = context.injections["elk_test_injection"]

    # Process the injection
    response = requests.post(f"{context.api_base_url}/api/v1/file_injection/{injection_id}/process")
    assert response.status_code == 200


@given('I have multiple files to inject')
def step_create_multiple_files(context: Context) -> None:
    """Create multiple files for concurrent injection testing."""
    context.test_files = getattr(context, 'test_files', {})

    for i in range(5):
        filename = f"concurrent_test_{i}.txt"
        content = f"Concurrent test file {i}\nContent for testing\n"
        temp_file = Path(tempfile.gettempdir()) / filename
        temp_file.write_text(content)
        context.test_files[filename] = temp_file


@when('I upload the file for injection to "{target_path}" with permissions "{permissions}"')
def step_upload_file_injection(context: Context, target_path: str, permissions: str) -> None:
    """Upload a file for injection."""
    # Get the first test file
    filename = list(context.test_files.keys())[0]
    file_path = context.test_files[filename]

    with open(file_path, 'rb') as f:
        files = {'file': (filename, f, 'text/plain')}
        data = {
            'target_path': target_path,
            'permissions': permissions,
            'description': 'BDD test injection'
        }
        context.response = requests.post(
            f"{context.api_base_url}/api/v1/file_injection/",
            files=files,
            data=data
        )


@when('I upload the file with permissions "{permissions}"')
def step_upload_file_with_permissions(context: Context, permissions: str) -> None:
    """Upload a file with specific permissions."""
    filename = "test_file.txt"
    file_path = context.test_files[filename]

    with open(file_path, 'rb') as f:
        files = {'file': (filename, f, 'text/plain')}
        data = {
            'target_path': f'/app/{filename}',
            'permissions': permissions,
            'description': f'Test with permissions {permissions}'
        }
        context.response = requests.post(
            f"{context.api_base_url}/api/v1/file_injection/",
            files=files,
            data=data
        )


@when('I process the injection')
def step_process_injection(context: Context) -> None:
    """Process a file injection."""
    # Get the first injection ID
    injection_id = list(context.injections.values())[0]
    context.response = requests.post(
        f"{context.api_base_url}/api/v1/file_injection/{injection_id}/process"
    )


@when('I request the injection status')
def step_request_injection_status(context: Context) -> None:
    """Request the status of an injection."""
    injection_id = list(context.injections.values())[0]
    context.response = requests.get(
        f"{context.api_base_url}/api/v1/file_injection/{injection_id}/status"
    )


@when('I request injections with skip={skip:d} and limit={limit:d}')
def step_request_injections_paginated(context: Context, skip: int, limit: int) -> None:
    """Request injections with pagination parameters."""
    context.response = requests.get(
        f"{context.api_base_url}/api/v1/file_injection/?skip={skip}&limit={limit}"
    )


@when('I request injections with status filter "{status}"')
def step_request_injections_filtered(context: Context, status: str) -> None:
    """Request injections filtered by status."""
    context.response = requests.get(
        f"{context.api_base_url}/api/v1/file_injection/?status_filter={status}"
    )


@when('I delete the injection')
def step_delete_injection(context: Context) -> None:
    """Delete a file injection."""
    injection_id = list(context.injections.values())[0]
    context.response = requests.delete(
        f"{context.api_base_url}/api/v1/file_injection/{injection_id}"
    )


@when('I attempt to upload the file for injection')
def step_attempt_upload_invalid_file(context: Context) -> None:
    """Attempt to upload an invalid file."""
    # Simulate invalid file upload
    data = {
        'target_path': '/app/invalid.txt',
        'permissions': '0644'
    }
    context.response = requests.post(
        f"{context.api_base_url}/api/v1/file_injection/",
        data=data  # No file provided
    )


@when('I attempt to process the injection')
def step_attempt_process_nonexistent(context: Context) -> None:
    """Attempt to process a non-existent injection."""
    context.response = requests.post(
        f"{context.api_base_url}/api/v1/file_injection/non-existent-id/process"
    )


@when('I query the ELK stack for injection events')
def step_query_elk_for_events(context: Context) -> None:
    """Query the ELK stack for injection events."""
    # Query Elasticsearch for injection events
    query = {
        "query": {
            "match": {
                "event_type": "injection_created"
            }
        }
    }
    context.elk_response = requests.post(
        f"{context.elasticsearch_url}/turdparty-*/_search",
        json=query
    )


@when('I create multiple injections simultaneously')
def step_create_concurrent_injections(context: Context) -> None:
    """Create multiple injections simultaneously."""
    import concurrent.futures

    context.concurrent_responses = []

    def upload_file(filename: str, file_path: Path) -> requests.Response:
        with open(file_path, 'rb') as f:
            files = {'file': (filename, f, 'text/plain')}
            data = {
                'target_path': f'/app/{filename}',
                'permissions': '0644',
                'description': f'Concurrent injection {filename}'
            }
            return requests.post(
                f"{context.api_base_url}/api/v1/file_injection/",
                files=files,
                data=data
            )

    # Upload files concurrently
    with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
        futures = []
        for filename, file_path in context.test_files.items():
            future = executor.submit(upload_file, filename, file_path)
            futures.append(future)

        for future in concurrent.futures.as_completed(futures):
            context.concurrent_responses.append(future.result())


@then('the injection should be created successfully')
def step_verify_injection_created(context: Context) -> None:
    """Verify that the injection was created successfully."""
    assert context.response.status_code == 201
    result = context.response.json()
    assert 'id' in result
    context.created_injection_id = result['id']


@then('the injection status should be "{expected_status}"')
def step_verify_injection_status(context: Context, expected_status: str) -> None:
    """Verify the injection status."""
    if hasattr(context, 'response'):
        result = context.response.json()
        assert result['status'] == expected_status


@then('the file should be logged to ELK stack')
def step_verify_elk_logging(context: Context) -> None:
    """Verify that the file injection was logged to ELK stack."""
    # In a real implementation, you would query Elasticsearch
    # For now, we'll assume logging is working if the injection was created
    assert hasattr(context, 'created_injection_id')


@then('the installation base should be logged to ELK stack')
def step_verify_installation_base_logged(context: Context) -> None:
    """Verify that installation base information was logged."""
    # This would involve querying Elasticsearch for installation base logs
    assert context.response.status_code == 200


@then('the file should be available at the target path')
def step_verify_file_at_target(context: Context) -> None:
    """Verify that the file is available at the target path."""
    # In a real implementation, this would check the actual file system
    # For testing, we'll verify the injection was processed
    result = context.response.json()
    assert 'details' in result


@then('I should receive the current status')
def step_verify_status_response(context: Context) -> None:
    """Verify that the status response is received."""
    assert context.response.status_code == 200
    result = context.response.json()
    assert 'status' in result
    assert 'id' in result


@then('the response should include progress information')
def step_verify_progress_info(context: Context) -> None:
    """Verify that progress information is included."""
    result = context.response.json()
    assert 'progress' in result
    assert isinstance(result['progress'], int)


@then('I should receive {expected_count:d} injections')
def step_verify_injection_count(context: Context, expected_count: int) -> None:
    """Verify the number of injections received."""
    assert context.response.status_code == 200
    result = context.response.json()
    assert len(result) == expected_count


@then('the injections should be properly paginated')
def step_verify_pagination(context: Context) -> None:
    """Verify that pagination is working correctly."""
    # This is verified by the count check in the previous step
    assert context.response.status_code == 200


@then('I should only receive completed injections')
def step_verify_completed_injections(context: Context) -> None:
    """Verify that only completed injections are returned."""
    assert context.response.status_code == 200
    result = context.response.json()
    for injection in result:
        assert injection['status'] == 'completed'


@then('the injection should be removed')
def step_verify_injection_removed(context: Context) -> None:
    """Verify that the injection was removed."""
    assert context.response.status_code == 204


@then('the deletion should be logged to ELK stack')
def step_verify_deletion_logged(context: Context) -> None:
    """Verify that the deletion was logged to ELK stack."""
    # This would involve checking ELK logs for deletion events
    assert context.response.status_code == 204


@then('the upload should fail with validation error')
def step_verify_upload_validation_error(context: Context) -> None:
    """Verify that the upload failed with a validation error."""
    assert context.response.status_code == 422


@then('no injection should be created')
def step_verify_no_injection_created(context: Context) -> None:
    """Verify that no injection was created."""
    # This is verified by the validation error check
    assert context.response.status_code == 422


@then('I should receive a not found error')
def step_verify_not_found_error(context: Context) -> None:
    """Verify that a not found error is received."""
    assert context.response.status_code in [404, 500]  # Depending on implementation


@then('I should find logs for injection creation')
def step_verify_creation_logs(context: Context) -> None:
    """Verify that creation logs are found in ELK."""
    assert context.elk_response.status_code == 200
    result = context.elk_response.json()
    assert result['hits']['total']['value'] > 0


@then('I should find logs for injection processing')
def step_verify_processing_logs(context: Context) -> None:
    """Verify that processing logs are found in ELK."""
    # This would involve a separate query for processing events
    assert True  # Placeholder


@then('I should find installation base information')
def step_verify_installation_base_info(context: Context) -> None:
    """Verify that installation base information is found."""
    # This would involve querying for installation base logs
    assert True  # Placeholder


@then('the injection should be created with permissions "{permissions}"')
def step_verify_injection_permissions(context: Context, permissions: str) -> None:
    """Verify that the injection was created with correct permissions."""
    assert context.response.status_code == 201
    result = context.response.json()
    assert result['permissions'] == permissions


@then('the file should be processed correctly')
def step_verify_file_processed(context: Context) -> None:
    """Verify that the file was processed correctly."""
    # This would involve checking the processing status
    assert context.response.status_code == 201


@then('all injections should be created successfully')
def step_verify_all_concurrent_created(context: Context) -> None:
    """Verify that all concurrent injections were created successfully."""
    for response in context.concurrent_responses:
        assert response.status_code == 201


@then('each injection should have a unique ID')
def step_verify_unique_ids(context: Context) -> None:
    """Verify that each injection has a unique ID."""
    ids = set()
    for response in context.concurrent_responses:
        result = response.json()
        injection_id = result['id']
        assert injection_id not in ids
        ids.add(injection_id)


@then('all injections should be logged separately')
def step_verify_separate_logging(context: Context) -> None:
    """Verify that all injections are logged separately."""
    # This would involve checking ELK logs for separate entries
    assert len(context.concurrent_responses) == 5
