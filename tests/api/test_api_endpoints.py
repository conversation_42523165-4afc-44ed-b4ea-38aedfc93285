"""
Comprehensive API endpoint tests for TurdParty
"""
import requests
import json
import time
from typing import Dict, Any
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test configuration
API_BASE_URL = "http://localhost:8000"
API_V1_BASE = f"{API_BASE_URL}/api/v1"

class TestAPIEndpoints:
    """Test suite for all API endpoints"""
    
    def setup_method(self):
        """Setup method run before each test"""
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })

        # Test data storage
        self.test_file_id = None
        self.test_injection_id = None
        
    def test_health_endpoint(self):
        """Test the health check endpoint"""
        logger.info("Testing health endpoint...")

        response = self.session.get(f"{API_BASE_URL}/health/")

        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert data["service"] == "turdparty-api"
        assert "timestamp" in data

        logger.info("✅ Health endpoint test passed")
    
    def test_files_list_endpoint(self):
        """Test the files listing endpoint"""
        logger.info("Testing files list endpoint...")
        
        response = self.session.get(f"{API_V1_BASE}/files/")
        
        assert response.status_code == 200
        data = response.json()
        assert "files" in data
        assert "total" in data
        assert "skip" in data
        assert "limit" in data
        assert isinstance(data["files"], list)
        
        logger.info(f"✅ Files list endpoint test passed - found {data['total']} files")
    
    def test_files_list_pagination(self):
        """Test files listing with pagination"""
        logger.info("Testing files list pagination...")
        
        # Test with skip and limit parameters
        response = self.session.get(f"{API_V1_BASE}/files/?skip=0&limit=5")
        
        assert response.status_code == 200
        data = response.json()
        assert data["skip"] == 0
        assert data["limit"] == 5
        assert len(data["files"]) <= 5
        
        logger.info("✅ Files pagination test passed")
    
    def test_file_upload_endpoint(self):
        """Test file upload endpoint"""
        logger.info("Testing file upload endpoint...")
        
        # Create a test file
        test_file_content = b"This is a test file for API testing"
        files = {
            'file': ('test_api_file.txt', test_file_content, 'text/plain')
        }
        data = {
            'description': 'Test file uploaded via API test'
        }
        
        # Remove Content-Type header for multipart upload
        headers = {k: v for k, v in self.session.headers.items() if k.lower() != 'content-type'}
        
        response = requests.post(
            f"{API_V1_BASE}/files/upload",
            files=files,
            data=data,
            headers=headers
        )
        
        assert response.status_code == 201
        upload_data = response.json()
        assert "file_id" in upload_data
        assert upload_data["filename"] == "test_api_file.txt"
        assert upload_data["status"] == "stored"
        assert "created_at" in upload_data
        
        # Store file ID for later tests
        self.test_file_id = upload_data["file_id"]
        
        logger.info(f"✅ File upload test passed - file ID: {self.test_file_id}")
    
    def test_file_get_by_id(self):
        """Test getting a file by ID"""
        if not self.test_file_id:
            self.test_file_upload_endpoint()
        
        logger.info(f"Testing file get by ID: {self.test_file_id}")
        
        response = self.session.get(f"{API_V1_BASE}/files/{self.test_file_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["file_id"] == self.test_file_id
        assert data["filename"] == "test_api_file.txt"
        assert data["status"] == "stored"
        
        logger.info("✅ File get by ID test passed")
    
    def test_file_get_nonexistent(self):
        """Test getting a non-existent file"""
        logger.info("Testing file get for non-existent file...")
        
        fake_id = "00000000-0000-0000-0000-000000000000"
        response = self.session.get(f"{API_V1_BASE}/files/{fake_id}")
        
        assert response.status_code == 404
        data = response.json()
        assert "not found" in data["detail"].lower()
        
        logger.info("✅ File not found test passed")
    
    def test_vm_injections_list_endpoint(self):
        """Test VM injections listing endpoint"""
        logger.info("Testing VM injections list endpoint...")
        
        response = self.session.get(f"{API_V1_BASE}/virtual-machines/injections/")
        
        assert response.status_code == 200
        data = response.json()
        assert "injections" in data
        assert "total" in data
        assert "skip" in data
        assert "limit" in data
        assert isinstance(data["injections"], list)
        
        logger.info(f"✅ VM injections list test passed - found {data['total']} injections")
    
    def test_vm_injection_create(self):
        """Test creating a VM injection"""
        if not self.test_file_id:
            self.test_file_upload_endpoint()
        
        logger.info("Testing VM injection creation...")
        
        injection_data = {
            "file_upload_id": self.test_file_id,
            "template_id": "ubuntu_2204",
            "target_path": "/tmp/test_injection.txt",
            "permissions": "0755"
        }
        
        response = self.session.post(
            f"{API_V1_BASE}/virtual-machines/injections/",
            json=injection_data
        )
        
        assert response.status_code == 201
        data = response.json()
        assert "injection_id" in data
        assert data["file_upload_id"] == self.test_file_id
        assert data["template_id"] == "ubuntu_2204"
        assert data["target_path"] == "/tmp/test_injection.txt"
        assert data["permissions"] == "0755"
        assert data["status"] == "pending"
        assert "created_at" in data
        
        # Store injection ID for later tests
        self.test_injection_id = data["injection_id"]
        
        logger.info(f"✅ VM injection creation test passed - injection ID: {self.test_injection_id}")
    
    def test_vm_injection_get_by_id(self):
        """Test getting a VM injection by ID"""
        if not self.test_injection_id:
            self.test_vm_injection_create()
        
        logger.info(f"Testing VM injection get by ID: {self.test_injection_id}")
        
        response = self.session.get(f"{API_V1_BASE}/virtual-machines/injections/{self.test_injection_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["injection_id"] == self.test_injection_id
        assert data["status"] == "pending"
        
        logger.info("✅ VM injection get by ID test passed")
    
    def test_vm_injection_process(self):
        """Test processing a VM injection"""
        if not self.test_injection_id:
            self.test_vm_injection_create()
        
        logger.info(f"Testing VM injection processing: {self.test_injection_id}")
        
        response = self.session.post(f"{API_V1_BASE}/virtual-machines/injections/{self.test_injection_id}/process")
        
        assert response.status_code == 200
        data = response.json()
        assert data["injection_id"] == self.test_injection_id
        assert data["status"] == "processing"
        
        logger.info("✅ VM injection processing test passed")
    
    def test_template_injection_endpoints(self):
        """Test template injection endpoints"""
        if not self.test_file_id:
            self.test_file_upload_endpoint()
        
        logger.info("Testing template injection endpoints...")
        
        # Test list endpoint
        response = self.session.get(f"{API_V1_BASE}/template_injection/")
        assert response.status_code == 200
        
        # Test create endpoint
        injection_data = {
            "file_upload_id": self.test_file_id,
            "template_id": "debian_11",
            "target_path": "/tmp/template_test.txt",
            "permissions": "0644"
        }
        
        response = self.session.post(
            f"{API_V1_BASE}/template_injection/",
            json=injection_data
        )
        
        assert response.status_code == 201
        data = response.json()
        assert "injection_id" in data
        assert data["template_id"] == "debian_11"
        
        template_injection_id = data["injection_id"]
        
        # Test get by ID
        response = self.session.get(f"{API_V1_BASE}/template_injection/{template_injection_id}")
        assert response.status_code == 200
        
        logger.info("✅ Template injection endpoints test passed")
    
    def test_file_injection_endpoints(self):
        """Test file injection endpoints"""
        logger.info("Testing file injection endpoints...")
        
        # Test list endpoint
        response = self.session.get(f"{API_V1_BASE}/file_injection/")
        assert response.status_code == 200
        
        logger.info("✅ File injection endpoints test passed")
    
    def test_api_error_handling(self):
        """Test API error handling"""
        logger.info("Testing API error handling...")
        
        # Test invalid endpoint
        response = self.session.get(f"{API_V1_BASE}/nonexistent/")
        assert response.status_code == 404
        
        # Test invalid JSON
        response = requests.post(
            f"{API_V1_BASE}/virtual-machines/injections/",
            data="invalid json",
            headers={'Content-Type': 'application/json'}
        )
        assert response.status_code in [400, 422]
        
        logger.info("✅ API error handling test passed")
    
    def test_api_documentation(self):
        """Test API documentation endpoints"""
        logger.info("Testing API documentation...")
        
        # Test OpenAPI docs
        response = self.session.get(f"{API_BASE_URL}/docs")
        assert response.status_code == 200
        
        # Test OpenAPI JSON
        response = self.session.get(f"{API_BASE_URL}/openapi.json")
        assert response.status_code == 200
        data = response.json()
        assert "openapi" in data
        assert "info" in data
        
        logger.info("✅ API documentation test passed")

if __name__ == "__main__":
    # Run tests directly
    test_suite = TestAPIEndpoints()
    test_suite.setup_method()
    
    try:
        test_suite.test_health_endpoint()
        test_suite.test_files_list_endpoint()
        test_suite.test_files_list_pagination()
        test_suite.test_file_upload_endpoint()
        test_suite.test_file_get_by_id()
        test_suite.test_file_get_nonexistent()
        test_suite.test_vm_injections_list_endpoint()
        test_suite.test_vm_injection_create()
        test_suite.test_vm_injection_get_by_id()
        test_suite.test_vm_injection_process()
        test_suite.test_template_injection_endpoints()
        test_suite.test_file_injection_endpoints()
        test_suite.test_api_error_handling()
        test_suite.test_api_documentation()
        
        print("\n🎉 All API endpoint tests passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise
