"""
Pydantic models for file injection functionality
"""
from datetime import datetime
from typing import Optional, Dict, Any
from enum import Enum
from pydantic import BaseModel, Field
import uuid

class InjectionStatus(str, Enum):
    """Enumeration of possible injection statuses"""
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"

class FileInjectionCreate(BaseModel):
    """Model for creating a new file injection"""
    filename: str = Field(..., description="Name of the file to inject")
    target_path: str = Field(..., description="Target path for the injected file")
    permissions: str = Field(default="0755", description="File permissions in octal format")
    description: Optional[str] = Field(None, description="Optional description of the injection")

    class Config:
        json_schema_extra = {
            "example": {
                "filename": "my_script.sh",
                "target_path": "/app/scripts/my_script.sh",
                "permissions": "0755",
                "description": "Deployment script for application setup"
            }
        }

class FileInjectionResponse(BaseModel):
    """Model for file injection response"""
    id: str = Field(..., description="Unique identifier for the injection")
    filename: str = Field(..., description="Name of the injected file")
    target_path: str = Field(..., description="Target path for the injected file")
    permissions: str = Field(..., description="File permissions in octal format")
    status: InjectionStatus = Field(..., description="Current status of the injection")
    description: Optional[str] = Field(None, description="Description of the injection")
    created_at: datetime = Field(..., description="Timestamp when injection was created")
    updated_at: datetime = Field(..., description="Timestamp when injection was last updated")
    file_size: Optional[int] = Field(None, description="Size of the injected file in bytes")
    file_hash: Optional[str] = Field(None, description="SHA256 hash of the injected file")

    class Config:
        json_schema_extra = {
            "example": {
                "id": "550e8400-e29b-41d4-a716-446655440000",
                "filename": "my_script.sh",
                "target_path": "/app/scripts/my_script.sh",
                "permissions": "0755",
                "status": "completed",
                "description": "Deployment script for application setup",
                "created_at": "2024-01-15T10:30:00Z",
                "updated_at": "2024-01-15T10:35:00Z",
                "file_size": 1024,
                "file_hash": "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3"
            }
        }

class FileInjectionStatus(BaseModel):
    """Model for file injection status information"""
    id: str = Field(..., description="Unique identifier for the injection")
    status: InjectionStatus = Field(..., description="Current status of the injection")
    progress: int = Field(default=0, description="Progress percentage (0-100)")
    message: Optional[str] = Field(None, description="Status message or error description")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional status details")
    updated_at: datetime = Field(..., description="Timestamp when status was last updated")

    class Config:
        json_schema_extra = {
            "example": {
                "id": "550e8400-e29b-41d4-a716-446655440000",
                "status": "in_progress",
                "progress": 75,
                "message": "Copying file to target location",
                "details": {
                    "current_step": "file_copy",
                    "total_steps": 4,
                    "completed_steps": 3
                },
                "updated_at": "2024-01-15T10:33:00Z"
            }
        }

class FileInjectionUpdate(BaseModel):
    """Model for updating file injection"""
    status: Optional[InjectionStatus] = Field(None, description="New status")
    progress: Optional[int] = Field(None, description="Progress percentage")
    message: Optional[str] = Field(None, description="Status message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional details")

class ELKLogEntry(BaseModel):
    """Model for ELK log entries"""
    timestamp: datetime = Field(default_factory=datetime.utcnow)
    service: str = Field(default="turdparty-api")
    level: str = Field(default="INFO")
    event_type: str = Field(..., description="Type of event being logged")
    injection_id: Optional[str] = Field(None, description="Related injection ID")
    message: str = Field(..., description="Log message")
    details: Optional[Dict[str, Any]] = Field(None, description="Additional log details")

    class Config:
        json_schema_extra = {
            "example": {
                "timestamp": "2024-01-15T10:30:00Z",
                "service": "turdparty-api",
                "level": "INFO",
                "event_type": "file_injection_created",
                "injection_id": "550e8400-e29b-41d4-a716-446655440000",
                "message": "File injection created successfully",
                "details": {
                    "filename": "my_script.sh",
                    "target_path": "/app/scripts/my_script.sh",
                    "file_size": 1024
                }
            }
        }
