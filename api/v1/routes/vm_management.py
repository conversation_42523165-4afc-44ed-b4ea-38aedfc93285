"""
Enhanced VM Management API with real-time WebSocket support
"""
from fastapi import APIRouter, HTTPException, status, Depends, Query, WebSocket, WebSocketDisconnect
from typing import Optional, List, Dict, Any
import uuid
import logging
import asyncio
import json
from datetime import datetime, timezone

from api.models.vm_management import (
    VMCreateRequest, VMResponse, VMListResponse, VMActionRequest, 
    VMActionResponse, VMTemplateResponse
)
from api.services.vm_service import VMService
from api.services.vm_metrics_service import vm_metrics_service

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/vms",
    tags=["vm_management"],
    responses={404: {"description": "VM not found"}},
)

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}
    
    async def connect(self, websocket: WebSocket, vm_id: str):
        await websocket.accept()
        if vm_id not in self.active_connections:
            self.active_connections[vm_id] = []
        self.active_connections[vm_id].append(websocket)
        logger.info(f"WebSocket connected for VM {vm_id}")
    
    def disconnect(self, websocket: WebSocket, vm_id: str):
        if vm_id in self.active_connections:
            self.active_connections[vm_id].remove(websocket)
            if not self.active_connections[vm_id]:
                del self.active_connections[vm_id]
        logger.info(f"WebSocket disconnected for VM {vm_id}")
    
    async def send_personal_message(self, message: str, websocket: WebSocket):
        await websocket.send_text(message)
    
    async def broadcast_to_vm(self, message: str, vm_id: str):
        if vm_id in self.active_connections:
            for connection in self.active_connections[vm_id]:
                try:
                    await connection.send_text(message)
                except Exception as e:
                    logger.error(f"Error broadcasting to VM {vm_id}: {e}")

manager = ConnectionManager()

# Mock VM storage (replace with actual database)
mock_vms = {}

@router.websocket("/{vm_id}/metrics/stream")
async def stream_vm_metrics(websocket: WebSocket, vm_id: str, vm_type: str = "docker"):
    """Stream real-time VM metrics via WebSocket"""
    await manager.connect(websocket, vm_id)
    
    try:
        # Initialize metrics service
        await vm_metrics_service.initialize()
        
        # Start streaming metrics
        async for metrics in vm_metrics_service.stream_vm_metrics(vm_id, vm_type, interval=1.0):
            await websocket.send_json(metrics)
            
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected for VM {vm_id} metrics stream")
    except Exception as e:
        logger.error(f"Error in metrics stream for VM {vm_id}: {e}")
        await websocket.send_json({
            "error": str(e),
            "timestamp": int(datetime.utcnow().timestamp() * 1000)
        })
    finally:
        manager.disconnect(websocket, vm_id)
        vm_metrics_service.stop_stream(vm_id, vm_type)

@router.websocket("/{vm_id}/commands/execute")
async def execute_command_stream(websocket: WebSocket, vm_id: str):
    """Execute commands with real-time output streaming"""
    await manager.connect(websocket, f"{vm_id}_commands")
    
    try:
        while True:
            # Wait for command from client
            data = await websocket.receive_text()
            command_data = json.loads(data)
            
            command = command_data.get("command", "")
            working_dir = command_data.get("working_directory", "/tmp")
            
            if not command:
                await websocket.send_json({
                    "type": "error",
                    "message": "No command provided"
                })
                continue
            
            # Execute command and stream output
            await execute_command_with_streaming(websocket, vm_id, command, working_dir)
            
    except WebSocketDisconnect:
        logger.info(f"Command execution WebSocket disconnected for VM {vm_id}")
    except Exception as e:
        logger.error(f"Error in command execution for VM {vm_id}: {e}")
        await websocket.send_json({
            "type": "error",
            "message": str(e)
        })
    finally:
        manager.disconnect(websocket, f"{vm_id}_commands")

async def execute_command_with_streaming(websocket: WebSocket, vm_id: str, command: str, working_dir: str):
    """Execute command and stream output in real-time"""
    try:
        # For Docker containers
        if vm_id.startswith("docker_"):
            await execute_docker_command_stream(websocket, vm_id, command, working_dir)
        else:
            # For Vagrant VMs (mock implementation for now)
            await execute_vagrant_command_stream(websocket, vm_id, command, working_dir)
            
    except Exception as e:
        await websocket.send_json({
            "type": "command_output",
            "stderr": f"Command execution error: {str(e)}",
            "exit_code": 1,
            "is_complete": True
        })

async def execute_docker_command_stream(websocket: WebSocket, vm_id: str, command: str, working_dir: str):
    """Execute command in Docker container with streaming"""
    try:
        import docker
        client = docker.from_env()
        container_id = vm_id.replace("docker_", "")
        container = client.containers.get(container_id)
        
        # Create exec instance
        exec_instance = container.exec_run(
            f"cd {working_dir} && {command}",
            stream=True,
            demux=True
        )
        
        # Stream output
        for chunk in exec_instance.output:
            if chunk[0]:  # stdout
                await websocket.send_json({
                    "type": "command_output",
                    "stdout": chunk[0].decode(),
                    "is_complete": False
                })
            if chunk[1]:  # stderr
                await websocket.send_json({
                    "type": "command_output",
                    "stderr": chunk[1].decode(),
                    "is_complete": False
                })
        
        # Send completion
        await websocket.send_json({
            "type": "command_output",
            "exit_code": exec_instance.exit_code,
            "is_complete": True
        })
        
    except Exception as e:
        await websocket.send_json({
            "type": "command_output",
            "stderr": f"Docker command error: {str(e)}",
            "exit_code": 1,
            "is_complete": True
        })

async def execute_vagrant_command_stream(websocket: WebSocket, vm_id: str, command: str, working_dir: str):
    """Execute command in Vagrant VM with streaming (mock implementation)"""
    try:
        # Mock streaming output for Vagrant
        await websocket.send_json({
            "type": "command_output",
            "stdout": f"$ cd {working_dir} && {command}\n",
            "is_complete": False
        })
        
        # Simulate command execution
        await asyncio.sleep(0.5)
        
        await websocket.send_json({
            "type": "command_output",
            "stdout": f"Mock output for command: {command}\n",
            "is_complete": False
        })
        
        await asyncio.sleep(0.5)
        
        await websocket.send_json({
            "type": "command_output",
            "stdout": "Command completed successfully\n",
            "exit_code": 0,
            "is_complete": True
        })
        
    except Exception as e:
        await websocket.send_json({
            "type": "command_output",
            "stderr": f"Vagrant command error: {str(e)}",
            "exit_code": 1,
            "is_complete": True
        })

# Traditional REST endpoints
@router.get("/templates", response_model=List[VMTemplateResponse])
async def get_vm_templates():
    """Get available VM templates"""
    templates = [
        {
            "template_id": "ubuntu:20.04",
            "name": "Ubuntu 20.04 LTS",
            "description": "Ubuntu 20.04 LTS with security tools",
            "vm_type": "docker",
            "compatibility": ["linux", "malware_analysis"],
            "resource_requirements": {
                "min_memory_mb": 512,
                "min_cpus": 1,
                "min_disk_gb": 10
            }
        },
        {
            "template_id": "windows:10",
            "name": "Windows 10 Enterprise",
            "description": "Windows 10 Enterprise for malware analysis",
            "vm_type": "vagrant",
            "compatibility": ["windows", "malware_analysis"],
            "resource_requirements": {
                "min_memory_mb": 2048,
                "min_cpus": 2,
                "min_disk_gb": 40
            }
        }
    ]
    return templates

@router.post("/", response_model=VMResponse, status_code=status.HTTP_201_CREATED)
async def create_vm(request: VMCreateRequest):
    """Create a new VM"""
    try:
        vm_id = str(uuid.uuid4())
        
        # Create VM record
        vm_data = {
            "vm_id": vm_id,
            "name": request.name,
            "template": request.template,
            "vm_type": request.vm_type,
            "memory_mb": request.memory_mb,
            "cpus": request.cpus,
            "disk_gb": getattr(request, 'disk_gb', 20),
            "domain": request.domain,
            "description": getattr(request, 'description', ''),
            "status": "creating",
            "created_at": datetime.now(timezone.utc).isoformat(),
            "runtime_minutes": 0.0,
            "is_expired": False
        }
        
        # Store in mock storage
        mock_vms[vm_id] = vm_data
        
        # Start VM creation (mock)
        if request.vm_type == "docker":
            vm_data["status"] = "running"
            vm_data["started_at"] = datetime.now(timezone.utc).isoformat()
        
        logger.info(f"VM created successfully: {vm_id}")
        return vm_data
        
    except Exception as e:
        logger.error(f"Error creating VM: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create VM"
        )

@router.get("/", response_model=VMListResponse)
async def list_vms(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    status_filter: Optional[str] = Query(None)
):
    """List VMs with pagination and filtering"""
    try:
        vms = list(mock_vms.values())
        
        # Apply status filter
        if status_filter:
            vms = [vm for vm in vms if vm["status"] == status_filter]
        
        # Apply pagination
        total = len(vms)
        vms = vms[skip:skip + limit]
        
        return VMListResponse(
            vms=vms,
            total=total,
            skip=skip,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"Error listing VMs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list VMs"
        )

@router.get("/{vm_id}", response_model=VMResponse)
async def get_vm(vm_id: str):
    """Get VM details by ID"""
    try:
        if vm_id not in mock_vms:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"VM with id {vm_id} not found"
            )

        vm_data = mock_vms[vm_id].copy()

        # Add current metrics if VM is running
        if vm_data["status"] == "running":
            try:
                metrics = await vm_metrics_service.get_vm_metrics(vm_id, vm_data["vm_type"])
                vm_data["current_metrics"] = {
                    "cpu_percent": metrics.get("cpu_percent", 0),
                    "memory_percent": metrics.get("memory_percent", 0),
                    "uptime_seconds": metrics.get("uptime_seconds", 0)
                }
            except Exception as e:
                logger.warning(f"Could not get metrics for VM {vm_id}: {e}")

        return vm_data

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting VM {vm_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get VM details"
        )

@router.post("/{vm_id}/action", response_model=VMActionResponse)
async def vm_action(vm_id: str, action_request: VMActionRequest):
    """Perform action on VM (start, stop, restart, destroy, suspend, resume)"""
    try:
        if vm_id not in mock_vms:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"VM with id {vm_id} not found"
            )

        vm_data = mock_vms[vm_id]
        action = action_request.action
        force = getattr(action_request, 'force', False)

        # Validate action
        valid_actions = ["start", "stop", "restart", "destroy", "suspend", "resume"]
        if action not in valid_actions:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid action: {action}. Valid actions: {valid_actions}"
            )

        # Perform action (mock implementation)
        old_status = vm_data["status"]

        if action == "start":
            if old_status in ["stopped", "created"]:
                vm_data["status"] = "running"
                vm_data["started_at"] = datetime.utcnow().isoformat()
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Cannot start VM in status: {old_status}"
                )

        elif action == "stop":
            if old_status == "running":
                vm_data["status"] = "stopped"
                vm_data["stopped_at"] = datetime.utcnow().isoformat()
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Cannot stop VM in status: {old_status}"
                )

        elif action == "restart":
            if old_status == "running":
                vm_data["status"] = "running"
                vm_data["restarted_at"] = datetime.utcnow().isoformat()
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Cannot restart VM in status: {old_status}"
                )

        elif action == "destroy":
            vm_data["status"] = "destroyed"
            vm_data["destroyed_at"] = datetime.utcnow().isoformat()

        elif action == "suspend":
            if old_status == "running":
                vm_data["status"] = "suspended"
                vm_data["suspended_at"] = datetime.utcnow().isoformat()
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Cannot suspend VM in status: {old_status}"
                )

        elif action == "resume":
            if old_status == "suspended":
                vm_data["status"] = "running"
                vm_data["resumed_at"] = datetime.utcnow().isoformat()
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Cannot resume VM in status: {old_status}"
                )

        # Generate mock task ID
        task_id = f"task_{uuid.uuid4().hex[:8]}"

        response = VMActionResponse(
            vm_id=vm_id,
            action=action,
            status=vm_data["status"],
            task_id=task_id,
            message=f"VM {action} {'queued' if action != 'destroy' else 'completed'}"
        )

        logger.info(f"VM {vm_id} action {action} completed: {old_status} -> {vm_data['status']}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error performing action {action_request.action} on VM {vm_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform VM action"
        )

@router.delete("/{vm_id}")
async def delete_vm(vm_id: str, force: bool = Query(False)):
    """Delete VM"""
    try:
        if vm_id not in mock_vms:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"VM with id {vm_id} not found"
            )

        vm_data = mock_vms[vm_id]

        # Check if VM can be deleted
        if vm_data["status"] == "running" and not force:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot delete running VM without force=true"
            )

        # Stop any active streams
        vm_metrics_service.stop_stream(vm_id, vm_data["vm_type"])

        # Delete VM
        del mock_vms[vm_id]

        task_id = f"task_{uuid.uuid4().hex[:8]}"

        logger.info(f"VM {vm_id} deleted successfully")
        return {
            "vm_id": vm_id,
            "message": "VM deletion queued",
            "task_id": task_id
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting VM {vm_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete VM"
        )
