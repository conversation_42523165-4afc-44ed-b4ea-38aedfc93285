"""
FastAPI application factory
"""
import logging
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from api.v1.routes import file_injection, files, vm_injection, template_injection, vm_management, vm_files
from api.middleware.ecs_logging import ECSLoggingMiddleware

logger = logging.getLogger(__name__)

def get_application() -> FastAPI:
    """Create and configure FastAPI application"""
    
    app = FastAPI(
        title="TurdParty API",
        description="VM Management and File Injection Platform",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # Add ECS logging middleware (before CORS)
    app.add_middleware(ECSLoggingMiddleware, log_level="INFO")

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint"""
        return {
            "status": "healthy",
            "service": "turdparty-api",
            "version": "1.0.0"
        }
    
    # Include routers
    app.include_router(
        file_injection.router,
        prefix="/api/v1",
        tags=["file_injection"]
    )

    app.include_router(
        files.router,
        prefix="/api/v1",
        tags=["files"]
    )

    app.include_router(
        vm_injection.router,
        prefix="/api/v1",
        tags=["vm_injection"]
    )

    app.include_router(
        template_injection.router,
        prefix="/api/v1",
        tags=["template_injection"]
    )

    app.include_router(
        vm_management.router,
        prefix="/api/v1",
        tags=["vm_management"]
    )

    app.include_router(
        vm_files.router,
        prefix="/api/v1",
        tags=["vm_files"]
    )

    logger.info("FastAPI application configured successfully")
    return app
