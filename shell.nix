{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  name = "turdparty-dev-shell";
  
  buildInputs = with pkgs; [
    # Shell and terminal tools
    zsh
    starship  # Modern shell prompt
    
    # Core development tools
    git
    git-lfs
    gh  # GitHub CLI
    
    # Python development
    python311
    python311Packages.pip
    python311Packages.virtualenv
    poetry
    
    # Node.js for frontend
    nodejs_20
    yarn
    
    # Database tools
    postgresql_15
    redis
    
    # Container and orchestration
    docker
    docker-compose
    
    # Testing and quality tools
    ruff
    mypy
    bandit
    safety
    
    # Performance and monitoring
    htop
    iotop
    nethogs
    
    # File and text processing
    jq
    yq
    ripgrep
    fd
    bat
    exa
    
    # Network tools
    curl
    wget
    httpie
    
    # Archive and compression
    unzip
    zip
    gzip
    tar
    
    # Development utilities
    fzf  # Fuzzy finder
    mc   # Midnight commander
    tree
    watch
    tmux
    
    # Security tools
    gnupg
    openssh
    
    # Build tools
    gnumake
    gcc
    
    # Documentation
    pandoc
    
    # Linting and formatting
    shellcheck
    shfmt
    
    # Monitoring and debugging
    strace
    lsof
    tcpdump
    
    # Math and calculations
    bc
    
    # Text editors (backup options)
    vim
    nano
  ];

  shellHook = ''
    # Set up zsh as the shell
    export SHELL=${pkgs.zsh}/bin/zsh
    
    # Configure zsh with git branch display
    if [ -n "$ZSH_VERSION" ]; then
      # Enable git integration
      autoload -Uz vcs_info
      precmd() { vcs_info }
      
      # Configure git info format
      zstyle ':vcs_info:git:*' formats ' (%b)'
      zstyle ':vcs_info:*' enable git
      
      # Set up the prompt with git branch
      setopt PROMPT_SUBST
      PROMPT='%F{cyan}[TurdParty]%f %F{green}%n@%m%f:%F{blue}%~%f%F{red}''${vcs_info_msg_0_}%f
%F{yellow}❯%f '
      
      # Enable command completion
      autoload -U compinit
      compinit
      
      # History configuration
      HISTSIZE=10000
      SAVEHIST=10000
      HISTFILE=~/.zsh_history
      setopt SHARE_HISTORY
      setopt HIST_IGNORE_DUPS
      setopt HIST_IGNORE_ALL_DUPS
      setopt HIST_IGNORE_SPACE
      
      # Enable useful zsh options
      setopt AUTO_CD
      setopt CORRECT
      setopt EXTENDED_GLOB
      
      # Aliases for better development experience
      alias ll='exa -la --git'
      alias ls='exa'
      alias cat='bat'
      alias grep='rg'
      alias find='fd'
      alias top='htop'
      
      # TurdParty specific aliases
      alias tp-test='./scripts/run_tests.sh'
      alias tp-test-unit='./scripts/run_tests.sh --unit-only'
      alias tp-test-fast='./scripts/run_tests.sh --unit-only --no-linting'
      alias tp-coverage='./scripts/manage_coverage.sh full'
      alias tp-ci='./scripts/ci_test_runner.sh'
      alias tp-up='docker-compose up -d'
      alias tp-down='docker-compose down'
      alias tp-logs='docker-compose logs -f'
      alias tp-shell='docker-compose exec api bash'
      
      # Git aliases
      alias gs='git status'
      alias ga='git add'
      alias gc='git commit'
      alias gp='git push'
      alias gl='git log --oneline --graph --decorate'
      alias gb='git branch'
      alias gco='git checkout'
      alias gd='git diff'
      
      # Python development aliases
      alias py='python'
      alias pip-install='pip install'
      alias pytest-watch='ptw'
      alias black-check='black --check .'
      alias ruff-check='ruff check .'
      alias mypy-check='mypy .'
      
      # Docker aliases
      alias dc='docker-compose'
      alias dcu='docker-compose up'
      alias dcd='docker-compose down'
      alias dcl='docker-compose logs'
      alias dce='docker-compose exec'
      
      echo "🚀 TurdParty Development Environment Loaded!"
      echo "📁 Project: $(basename $(pwd))"
      echo "🌿 Git Branch: $(git branch --show-current 2>/dev/null || echo 'Not a git repository')"
      echo "🐍 Python: $(python --version)"
      echo "📦 Node.js: $(node --version)"
      echo ""
      echo "🔧 Available commands:"
      echo "  tp-test        - Run all tests"
      echo "  tp-test-unit   - Run unit tests only"
      echo "  tp-test-fast   - Quick unit tests"
      echo "  tp-coverage    - Generate coverage reports"
      echo "  tp-ci          - Run CI pipeline"
      echo "  tp-up          - Start services"
      echo "  tp-down        - Stop services"
      echo ""
      echo "📚 Documentation: docs/DEVELOPMENT.md"
      echo "🧪 Testing Guide: docs/TESTING.md"
      echo ""
    fi
    
    # Set up Python environment
    export PYTHONPATH="$PWD:$PYTHONPATH"
    
    # Set up development environment variables
    export DEVELOPMENT=true
    export DEBUG=true
    export LOG_LEVEL=DEBUG
    
    # TurdParty specific environment
    export TURDPARTY_ENV=development
    export TURDPARTY_CONFIG_PATH="$PWD/config"
    
    # Docker environment
    export COMPOSE_PROJECT_NAME=turdpartycollab
    export DOCKER_BUILDKIT=1
    export COMPOSE_DOCKER_CLI_BUILD=1
    
    # Testing environment
    export TEST_MODE=false  # Only true during actual test runs
    export COVERAGE_THRESHOLD=80
    export PARALLEL_WORKERS=4
    
    # Create necessary directories
    mkdir -p logs
    mkdir -p data/uploads
    mkdir -p data/temp_files
    mkdir -p test-results
    mkdir -p coverage-archive
    
    # Set up git hooks if they don't exist
    if [ -d ".git" ] && [ ! -f ".git/hooks/pre-commit" ]; then
      echo "Setting up git hooks..."
      cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# TurdParty pre-commit hook
echo "Running pre-commit checks..."

# Run ruff linting
if command -v ruff &> /dev/null; then
    echo "Running ruff linting..."
    ruff check . || exit 1
fi

# Run unit tests
echo "Running unit tests..."
python -m pytest tests/unit/ -x --ff || exit 1

echo "Pre-commit checks passed!"
EOF
      chmod +x .git/hooks/pre-commit
      echo "✅ Git pre-commit hook installed"
    fi
    
    # Check if we're in the correct directory
    if [ ! -f "docker-compose.yml" ]; then
      echo "⚠️  Warning: docker-compose.yml not found. Make sure you're in the TurdParty project root."
    fi
    
    # Check Docker availability
    if ! command -v docker &> /dev/null; then
      echo "⚠️  Warning: Docker not found. Some development features may not work."
    fi
    
    # Start zsh if not already running
    if [ -z "$ZSH_VERSION" ]; then
      exec ${pkgs.zsh}/bin/zsh
    fi
  '';

  # Environment variables
  NIX_SHELL_PRESERVE_PROMPT = "1";
  
  # Shell configuration
  SHELL = "${pkgs.zsh}/bin/zsh";
  
  # Development tools configuration
  EDITOR = "vim";
  PAGER = "bat";
  
  # Python configuration
  PYTHONDONTWRITEBYTECODE = "1";
  PYTHONUNBUFFERED = "1";
  
  # Node.js configuration
  NODE_ENV = "development";
  
  # Git configuration
  GIT_EDITOR = "vim";
}
