import { test, expect } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

test.describe('TurdParty Current UI Workflow Tests', () => {
  // Skip authentication for now since the upload works without it
  // test.use({ storageState: 'playwright/.auth/user.json' });
  
  let tempDir: string;
  let testFilePath: string;
  let malwareFilePath: string;
  
  test.beforeAll(async () => {
    // Create temporary test files
    tempDir = fs.mkdtempSync(path.join(os.tmpdir(), 'turdparty-test-'));
    
    // Create a simple test file
    testFilePath = path.join(tempDir, 'test-sample.txt');
    fs.writeFileSync(testFilePath, 'This is a test file for TurdParty upload testing');
    
    // Create a mock malware file (JavaScript)
    malwareFilePath = path.join(tempDir, 'malware-sample.js');
    fs.writeFileSync(malwareFilePath, `
// Mock malware sample for testing
console.log("This is a test malware sample");
const fs = require('fs');
fs.writeFileSync('/tmp/malware-test.txt', 'Malware executed successfully');
console.log("Test malware execution complete");
    `.trim());
  });
  
  test.afterAll(async () => {
    // Clean up temporary files
    if (tempDir && fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
    }
  });
  
  test.beforeEach(async ({ page }) => {
    // Set up console logging to capture any errors
    page.on('console', msg => {
      if (msg.type() === 'error') {
        console.log(`Browser console error: ${msg.text()}`);
      }
    });
    
    // Set up network request logging
    page.on('request', request => {
      if (request.url().includes('/api/')) {
        console.log(`API Request: ${request.method()} ${request.url()}`);
      }
    });
    
    page.on('response', response => {
      if (response.url().includes('/api/')) {
        console.log(`API Response: ${response.status()} ${response.url()}`);
      }
    });
  });

  test('should display the main page correctly', async ({ page }) => {
    console.log('Testing main page display...');
    
    // Navigate to the main page
    await page.goto('/');
    
    // Wait for the main page to load
    await page.waitForSelector('.main-page-container', { timeout: 10000 });
    
    // Check if the welcome title is visible
    await expect(page.locator('h1:has-text("Welcome to TurdParty")')).toBeVisible();
    
    // Check if the subtitle is visible
    await expect(page.locator('text=Your one-stop solution for file management, template injection, and VM operations')).toBeVisible();
    
    // Check if the dark mode toggle is visible
    await expect(page.locator('.ant-switch')).toBeVisible();
    
    // Check if the steps are visible
    await expect(page.locator('.ant-steps')).toBeVisible();
    
    // Check if the first step (Upload) is active
    await expect(page.locator('.ant-steps-item-active .ant-steps-item-title:has-text("Upload")')).toBeVisible();
    
    // Check if the file upload section is visible
    await expect(page.locator('.upload-container')).toBeVisible();
    
    // Check if the upload type selector is visible
    await expect(page.locator('.upload-type-selector')).toBeVisible();
    await expect(page.locator('text=Single File')).toBeVisible();
    await expect(page.locator('text=Folder')).toBeVisible();
    
    console.log('✅ Main page display test passed');
  });

  test('should complete the full upload to VM injection workflow', async ({ page }) => {
    console.log('Testing complete workflow from upload to VM injection...');
    
    // Navigate to the main page
    await page.goto('/');
    await page.waitForSelector('.main-page-container', { timeout: 10000 });
    
    // ===== STEP 1: FILE UPLOAD =====
    console.log('Step 1: Testing file upload...');
    
    // Ensure we're on step 0 (Upload)
    await expect(page.locator('.ant-steps-item-active .ant-steps-item-title:has-text("Upload")')).toBeVisible();
    
    // Check if authentication token is available or get one
    const authButton = page.locator('button:has-text("Get Authentication Token")');
    if (await authButton.isVisible()) {
      console.log('Getting authentication token...');
      await authButton.click();
      await page.waitForTimeout(2000); // Wait for token to be retrieved
    }
    
    // Wait for the file upload component to be ready
    await page.waitForSelector('.file-upload-component', { timeout: 10000 });
    
    // Set up file chooser for upload
    const fileChooserPromise = page.waitForEvent('filechooser');
    
    // Click the upload area to trigger file chooser
    await page.locator('.ant-upload-drag').click();
    
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(malwareFilePath);
    
    // Add description
    await page.fill('textarea[placeholder="Enter a description for this upload"]', 'Test malware sample for VM injection workflow');
    
    // Click upload button
    await page.click('button:has-text("Upload")');
    
    // Wait for upload to complete
    await page.waitForSelector('.ant-message-success', { timeout: 30000 });
    console.log('✅ File upload completed successfully');
    
    // ===== STEP 2: TEMPLATE SELECTION =====
    console.log('Step 2: Testing template selection...');
    
    // Wait for step 2 to be active
    await expect(page.locator('.ant-steps-item-active .ant-steps-item-title:has-text("Template")')).toBeVisible({ timeout: 10000 });
    
    // Select Ubuntu template
    const ubuntuTemplate = page.locator('.template-card:has-text("Ubuntu 22.04 LTS")');
    await expect(ubuntuTemplate).toBeVisible();
    await ubuntuTemplate.click();
    
    // Verify template is selected
    await expect(page.locator('.template-card.selected:has-text("Ubuntu 22.04 LTS")')).toBeVisible();
    
    // Click continue button
    await page.click('button:has-text("Continue")');
    console.log('✅ Template selection completed');
    
    // ===== STEP 3: TARGET CONFIGURATION =====
    console.log('Step 3: Testing target configuration...');
    
    // Wait for step 3 to be active
    await expect(page.locator('.ant-steps-item-active .ant-steps-item-title:has-text("Configure")')).toBeVisible({ timeout: 10000 });
    
    // Verify selected template is displayed
    await expect(page.locator('text=Selected template: Ubuntu 22.04 LTS')).toBeVisible();
    
    // Configure target path
    const targetPathInput = page.locator('input[placeholder="/app"]');
    await targetPathInput.clear();
    await targetPathInput.fill('/tmp/malware-sample.js');
    
    // Configure permissions
    const permissionsInput = page.locator('input[placeholder="0755"]');
    await permissionsInput.clear();
    await permissionsInput.fill('0755');
    
    // Click Create Injection button
    await page.click('button:has-text("Create Injection")');
    console.log('✅ Target configuration completed');
    
    // ===== STEP 4: COMPLETION =====
    console.log('Step 4: Testing completion...');
    
    // Wait for completion step
    await expect(page.locator('.ant-steps-item-active .ant-steps-item-title:has-text("Complete")')).toBeVisible({ timeout: 30000 });
    
    // Check for completion message
    await expect(page.locator('h3:has-text("Template Injection Complete!")')).toBeVisible();
    
    // Check for completion icon
    await expect(page.locator('.anticon-check-circle')).toBeVisible();
    
    // Check for action buttons
    await expect(page.locator('button:has-text("Start New Workflow")')).toBeVisible();
    await expect(page.locator('button:has-text("Go to VM Status")')).toBeVisible();
    
    console.log('✅ Complete workflow test passed successfully!');
  });

  test('should handle file upload errors gracefully', async ({ page }) => {
    console.log('Testing file upload error handling...');

    await page.goto('/');
    await page.waitForSelector('.main-page-container', { timeout: 10000 });

    // Mock a failed upload response
    await page.route('/api/v1/files/upload', async (route) => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          detail: 'Internal server error during file upload'
        })
      });
    });

    // Wait for the file upload component
    await page.waitForSelector('.file-upload-component', { timeout: 10000 });

    // Try to upload a file
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.locator('.ant-upload-drag').click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(testFilePath);

    await page.fill('textarea[placeholder="Enter a description for this upload"]', 'Test error handling');
    await page.click('button:has-text("Upload")');

    // Wait for error message
    await page.waitForSelector('.upload-error-alert', { timeout: 10000 });
    await expect(page.locator('text=Server error occurred while processing your upload')).toBeVisible();

    console.log('✅ File upload error handling test passed');
  });

  test('should validate file size limits', async ({ page }) => {
    console.log('Testing file size validation...');

    await page.goto('/');
    await page.waitForSelector('.main-page-container', { timeout: 10000 });

    // Create a large test file (simulate > 200MB)
    const largeMockFile = {
      name: 'large-file.bin',
      mimeType: 'application/octet-stream',
      buffer: Buffer.alloc(1024) // Small buffer but we'll mock the size
    };

    // Mock file size validation
    await page.evaluate(() => {
      // Override File constructor to simulate large file
      const originalFile = window.File;
      window.File = class extends originalFile {
        constructor(fileBits: any, fileName: string, options?: any) {
          super(fileBits, fileName, options);
          // Mock size property to be larger than 200MB
          Object.defineProperty(this, 'size', {
            value: 250 * 1024 * 1024, // 250MB
            writable: false
          });
        }
      };
    });

    await page.waitForSelector('.file-upload-component', { timeout: 10000 });

    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.locator('.ant-upload-drag').click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(testFilePath);

    // Should show file size error
    await expect(page.locator('text=File too large')).toBeVisible({ timeout: 5000 });

    console.log('✅ File size validation test passed');
  });

  test('should navigate between workflow steps correctly', async ({ page }) => {
    console.log('Testing workflow step navigation...');

    await page.goto('/');
    await page.waitForSelector('.main-page-container', { timeout: 10000 });

    // Mock successful upload
    await page.route('/api/v1/files/upload', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          file_id: 'test-file-id-123',
          filename: 'test-sample.txt',
          file_size: 42,
          status: 'stored',
          message: 'File uploaded successfully'
        })
      });
    });

    // Upload a file to progress to step 2
    await page.waitForSelector('.file-upload-component', { timeout: 10000 });
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.locator('.ant-upload-drag').click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(testFilePath);

    await page.fill('textarea[placeholder="Enter a description for this upload"]', 'Navigation test file');
    await page.click('button:has-text("Upload")');

    // Wait for step 2
    await expect(page.locator('.ant-steps-item-active .ant-steps-item-title:has-text("Template")')).toBeVisible({ timeout: 10000 });

    // Select template and continue to step 3
    await page.locator('.template-card:has-text("Ubuntu 22.04 LTS")').click();
    await page.click('button:has-text("Continue")');

    // Should be on step 3
    await expect(page.locator('.ant-steps-item-active .ant-steps-item-title:has-text("Configure")')).toBeVisible({ timeout: 10000 });

    // Test back navigation
    await page.click('button:has-text("Back")');

    // Should be back on step 2
    await expect(page.locator('.ant-steps-item-active .ant-steps-item-title:has-text("Template")')).toBeVisible({ timeout: 5000 });

    // Navigate forward again
    await page.click('button:has-text("Continue")');
    await expect(page.locator('.ant-steps-item-active .ant-steps-item-title:has-text("Configure")')).toBeVisible({ timeout: 5000 });

    console.log('✅ Workflow step navigation test passed');
  });

  test('should display debug information correctly', async ({ page }) => {
    console.log('Testing debug information display...');

    await page.goto('/');
    await page.waitForSelector('.main-page-container', { timeout: 10000 });

    // Mock upload and navigate to step 3 to see debug info
    await page.route('/api/v1/files/upload', async (route) => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          file_id: 'debug-test-file-id',
          filename: 'debug-test.txt',
          file_size: 42,
          status: 'stored'
        })
      });
    });

    // Quick upload and navigation to step 3
    await page.waitForSelector('.file-upload-component', { timeout: 10000 });
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.locator('.ant-upload-drag').click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles(testFilePath);

    await page.fill('textarea[placeholder="Enter a description for this upload"]', 'Debug test');
    await page.click('button:has-text("Upload")');

    await page.locator('.template-card:has-text("Ubuntu 22.04 LTS")').click();
    await page.click('button:has-text("Continue")');

    // Check debug information is displayed
    await expect(page.locator('text=Debug Information:')).toBeVisible();
    await expect(page.locator('text=selectedTemplate')).toBeVisible();
    await expect(page.locator('text=ubuntu_2204')).toBeVisible();

    console.log('✅ Debug information display test passed');
  });

  test('should handle dark mode toggle', async ({ page }) => {
    console.log('Testing dark mode functionality...');

    await page.goto('/');
    await page.waitForSelector('.main-page-container', { timeout: 10000 });

    // Find the dark mode toggle
    const darkModeToggle = page.locator('.ant-switch');
    await expect(darkModeToggle).toBeVisible();

    // Get initial state
    const initialState = await darkModeToggle.isChecked();

    // Toggle dark mode
    await darkModeToggle.click();

    // Verify state changed
    const newState = await darkModeToggle.isChecked();
    expect(newState).toBe(!initialState);

    // Toggle back
    await darkModeToggle.click();
    const finalState = await darkModeToggle.isChecked();
    expect(finalState).toBe(initialState);

    console.log('✅ Dark mode toggle test passed');
  });
});
