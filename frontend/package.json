{"name": "turdparty-app", "version": "0.1.1", "private": true, "description": "TurdParty Application", "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "dependencies": {"@ant-design/icons": "^5.0.1", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.12", "@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "ajv": "^8.12.0", "antd": "^5.2.2", "axios": "^1.3.4", "http-proxy-middleware": "^3.0.5", "i18next": "^24.2.3", "i18next-browser-languagedetector": "^8.0.4", "i18next-http-backend": "^3.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.4.1", "react-router-dom": "^6.8.1", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "devDependencies": {"@playwright/test": "^1.51.0", "@types/axios": "^0.9.36", "@types/node": "^20.17.24", "@types/react": "^18.3.18", "@types/react-dom": "^18.2.7", "typescript": "^4.9.5"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "main": "simple.spec.js", "directories": {"test": "tests"}, "keywords": [], "author": "", "license": "ISC", "overrides": {"nth-check": ">=2.0.1", "postcss": ">=8.4.31", "svgo": ">=2.0.0", "webpack-dev-server": ">=5.2.1"}}