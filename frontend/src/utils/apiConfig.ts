/**
 * API configuration utility
 * Centralizes API URL management and versioning
 */

// API version prefix
export const API_PREFIX = '/api/v1';

/**
 * Get the full API URL for a given endpoint
 * @param endpoint - The API endpoint path (without the /api/v1 prefix)
 * @returns The full API URL with the correct prefix
 */
export const getApiUrl = (endpoint: string): string => {
  // Remove any leading slash from the endpoint
  const cleanEndpoint = endpoint.startsWith('/') ? endpoint.substring(1) : endpoint;
  
  // If the endpoint already includes the API prefix, return it as is
  if (cleanEndpoint.startsWith('api/v1/')) {
    return `/${cleanEndpoint}`;
  }
  
  // If the endpoint already includes the old /api/ prefix, replace it
  if (cleanEndpoint.startsWith('api/')) {
    return `${API_PREFIX}/${cleanEndpoint.substring(4)}`;
  }
  
  // List of endpoints that require trailing slashes
  const needsTrailingSlash = [
    'health', 
    'docs', 
    'docs/all', 
    'health/system-status',
    'health/test-runs',
    'file_upload', 
    'file_upload/folder',
    'folder_upload',
    'file_selection',
    'vagrant_vm',
    'vagrant_vm/templates',
    'vagrant_vm/os_templates',
    'vm_injection',
    'template_injection',
    'security_testing',
    'data'
  ];
  
  // Add trailing slash for endpoints that need it
  const trailingSlash = 
    (needsTrailingSlash.includes(cleanEndpoint) || 
     needsTrailingSlash.some(path => cleanEndpoint.startsWith(path + '/')) || 
     cleanEndpoint.endsWith('/')) && 
    !cleanEndpoint.endsWith('/') ? '/' : '';
  
  // Otherwise, add the API prefix
  return `${API_PREFIX}/${cleanEndpoint}${trailingSlash}`;
};

/**
 * Function to ensure an endpoint has a trailing slash if needed
 * This is used as a safety measure to guarantee correct URLs
 */
export const ensureTrailingSlash = (url: string): string => {
  if (!url) return url;
  
  // Don't add trailing slash to URLs with query parameters
  if (url.includes('?')) return url;
  
  // Don't add trailing slash to URLs that already have one
  if (url.endsWith('/')) return url;
  
  // Add trailing slash to API endpoints that require it
  if (url.includes('/api/v1/file_upload') || 
      url.includes('/api/v1/health') ||
      url.includes('/api/v1/docs') ||
      url.includes('/api/v1/security_testing') ||
      url.includes('/api/v1/data')) {
    return `${url}/`;
  }
  
  return url;
};

/**
 * API endpoints object
 * Contains all API endpoints used in the application
 */
export const API_ENDPOINTS = {
  // Auth endpoints
  AUTH: {
    TEST_TOKEN: getApiUrl('auth/test-token'),
    LOGIN: getApiUrl('auth/login'),
    REFRESH: getApiUrl('auth/refresh'),
  },
  
  // Security testing endpoints
  SECURITY_TESTING: {
    BASE: ensureTrailingSlash(getApiUrl('security_testing')),
    BY_ID: (id: string) => getApiUrl(`security_testing/${id}`),
    RUN: (id: string) => getApiUrl(`security_testing/${id}/run`),
    RESULTS: (id: string) => getApiUrl(`security_testing/${id}/results`),
  },
  
  // Isolated test endpoints
  ISOLATED_TEST: {
    BASE: getApiUrl('/isolated-test'),
    LIST: getApiUrl('/isolated-test'),
    GET: (id: string) => getApiUrl(`/isolated-test/${id}`),
    RUN: (id: string) => getApiUrl(`/isolated-test/${id}/run`),
    STOP: (id: string) => getApiUrl(`/isolated-test/${id}/stop`),
    RESULTS: (id: string) => getApiUrl(`/isolated-test/${id}/results`),
  },

  // Data management endpoints
  DATA: {
    LIST: ensureTrailingSlash(getApiUrl('data')),
    GET: getApiUrl('data/:id'),
    CREATE: ensureTrailingSlash(getApiUrl('data')),
    UPDATE: getApiUrl('data/:id'),
    DELETE: getApiUrl('data/:id'),
  },

  // File upload endpoints
  FILE_UPLOAD: {
    BASE: ensureTrailingSlash(getApiUrl('file_upload')),
    FOLDER: ensureTrailingSlash(getApiUrl('file_upload/folder')),
    DOWNLOAD: (id: string) => getApiUrl(`file_upload/download/${id}`),
    DELETE: (id: string) => getApiUrl(`file_upload/${id}`),
  },

  // VM Management endpoints
  VMS: {
    BASE: getApiUrl('vms'),
    LIST: getApiUrl('vms'),
    CREATE: getApiUrl('vms'),
    GET: (id: string) => getApiUrl(`vms/${id}`),
    UPDATE: (id: string) => getApiUrl(`vms/${id}`),
    DELETE: (id: string) => getApiUrl(`vms/${id}`),
    ACTION: (id: string) => getApiUrl(`vms/${id}/action`),
    INJECT: (id: string) => getApiUrl(`vms/${id}/inject`),
    METRICS: (id: string) => getApiUrl(`vms/${id}/metrics`),
    LOGS: (id: string) => getApiUrl(`vms/${id}/logs`),
    TEMPLATES: getApiUrl('vms/templates'),
  },

  // File Management endpoints
  FILES: {
    BASE: getApiUrl('files'),
    LIST: getApiUrl('files'),
    UPLOAD: getApiUrl('files/upload'),
    GET: (id: string) => getApiUrl(`files/${id}`),
    DOWNLOAD: (id: string) => getApiUrl(`files/${id}/download`),
    DELETE: (id: string) => getApiUrl(`files/${id}`),
  },

  // Health endpoints
  HEALTH: {
    BASE: getApiUrl('health'),
    SYSTEM: getApiUrl('health'),
  },

  SUCCESS_CRITERIA: '/api/success-criteria',
};

// Log API configuration for debugging
console.debug('API Configuration loaded with prefix:', API_PREFIX);
console.debug('Example endpoints:', {
  auth: API_ENDPOINTS.AUTH.TEST_TOKEN,
  securityTesting: API_ENDPOINTS.SECURITY_TESTING.BASE,
  isolated: API_ENDPOINTS.ISOLATED_TEST.BASE,
  data: API_ENDPOINTS.DATA.LIST,
}); 