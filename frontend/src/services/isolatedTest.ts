import axios from 'axios';
import { API_ENDPOINTS } from '../utils/apiConfig';

export interface IsolatedTestResult {
  id: string;
  name: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  timestamp: string;
  details: string;
  failures?: {
    description: string;
    error: string;
  }[];
}

class IsolatedTestService {
  private token: string;

  constructor(token: string) {
    this.token = token;
  }

  private getHeaders() {
    return {
      Authorization: `Bearer ${this.token}`,
      'Content-Type': 'application/json',
    };
  }

  async getTests(): Promise<IsolatedTestResult[]> {
    const response = await axios.get(
      API_ENDPOINTS.ISOLATED_TEST.LIST,
      { headers: this.getHeaders() }
    );
    return response.data;
  }

  async getTestById(id: string): Promise<IsolatedTestResult> {
    const response = await axios.get(
      API_ENDPOINTS.ISOLATED_TEST.GET(id),
      { headers: this.getHeaders() }
    );
    return response.data;
  }

  async runTest(id: string): Promise<void> {
    await axios.post(
      API_ENDPOINTS.ISOLATED_TEST.RUN(id),
      {},
      { headers: this.getHeaders() }
    );
  }

  async stopTest(id: string): Promise<void> {
    await axios.post(
      API_ENDPOINTS.ISOLATED_TEST.STOP(id),
      {},
      { headers: this.getHeaders() }
    );
  }

  async getTestResults(id: string): Promise<IsolatedTestResult> {
    const response = await axios.get(
      API_ENDPOINTS.ISOLATED_TEST.RESULTS(id),
      { headers: this.getHeaders() }
    );
    return response.data;
  }
}

export default IsolatedTestService; 