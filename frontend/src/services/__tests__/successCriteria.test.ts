import axios from 'axios';
import { successCriteriaService } from '../successCriteria';
import { API_ENDPOINTS } from '../../utils/apiConfig';
import type { SuccessCriteriaFilters } from '../successCriteria';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('SuccessCriteriaService', () => {
  const mockCriteria = {
    id: '1',
    name: 'Test Scenarios',
    description: 'All test scenarios pass consistently',
    status: 'success' as const,
    category: 'functionality' as const,
    details: ['Unit tests passing', 'Integration tests passing'],
    lastUpdated: new Date().toISOString(),
  };

  const mockResponse = {
    data: [mockCriteria],
    total: 1,
    page: 1,
    pageSize: 10,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getSuccessCriteria', () => {
    it('fetches success criteria with default filters', async () => {
      mockedAxios.get.mockResolvedValueOnce({ data: mockResponse });

      const result = await successCriteriaService.getSuccessCriteria();

      expect(mockedAxios.get).toHaveBeenCalledWith(
        `${API_ENDPOINTS.SUCCESS_CRITERIA}`,
        {
          params: {
            page: 1,
            pageSize: 10,
            status: undefined,
            category: undefined,
          },
        }
      );
      expect(result).toEqual(mockResponse);
    });

    it('fetches success criteria with custom filters', async () => {
      mockedAxios.get.mockResolvedValueOnce({ data: mockResponse });

      const filters: SuccessCriteriaFilters = {
        page: 2,
        pageSize: 20,
        status: 'success',
        category: 'functionality',
      };

      const result = await successCriteriaService.getSuccessCriteria(filters);

      expect(mockedAxios.get).toHaveBeenCalledWith(
        `${API_ENDPOINTS.SUCCESS_CRITERIA}`,
        {
          params: filters,
        }
      );
      expect(result).toEqual(mockResponse);
    });

    it('handles API errors', async () => {
      const error = new Error('API Error');
      mockedAxios.get.mockRejectedValueOnce(error);

      await expect(successCriteriaService.getSuccessCriteria()).rejects.toThrow('API Error');
    });
  });

  describe('getSuccessCriteriaById', () => {
    it('fetches a single success criterion by ID', async () => {
      mockedAxios.get.mockResolvedValueOnce({ data: mockCriteria });

      const result = await successCriteriaService.getSuccessCriteriaById('1');

      expect(mockedAxios.get).toHaveBeenCalledWith(
        `${API_ENDPOINTS.SUCCESS_CRITERIA}/1`
      );
      expect(result).toEqual(mockCriteria);
    });

    it('handles API errors', async () => {
      const error = new Error('API Error');
      mockedAxios.get.mockRejectedValueOnce(error);

      await expect(successCriteriaService.getSuccessCriteriaById('1')).rejects.toThrow('API Error');
    });
  });

  describe('updateSuccessCriteriaStatus', () => {
    it('updates the status of a success criterion', async () => {
      const updatedCriteria = {
        ...mockCriteria,
        status: 'failed' as const,
      };
      mockedAxios.patch.mockResolvedValueOnce({ data: updatedCriteria });

      const result = await successCriteriaService.updateSuccessCriteriaStatus('1', 'failed');

      expect(mockedAxios.patch).toHaveBeenCalledWith(
        `${API_ENDPOINTS.SUCCESS_CRITERIA}/1/status`,
        { status: 'failed' }
      );
      expect(result).toEqual(updatedCriteria);
    });

    it('handles API errors', async () => {
      const error = new Error('API Error');
      mockedAxios.patch.mockRejectedValueOnce(error);

      await expect(successCriteriaService.updateSuccessCriteriaStatus('1', 'failed')).rejects.toThrow('API Error');
    });
  });

  describe('addSuccessCriteriaDetail', () => {
    it('adds a new detail to a success criterion', async () => {
      const newDetail = 'New test added';
      const updatedCriteria = {
        ...mockCriteria,
        details: [...mockCriteria.details, newDetail],
      };
      mockedAxios.post.mockResolvedValueOnce({ data: updatedCriteria });

      const result = await successCriteriaService.addSuccessCriteriaDetail('1', newDetail);

      expect(mockedAxios.post).toHaveBeenCalledWith(
        `${API_ENDPOINTS.SUCCESS_CRITERIA}/1/details`,
        { detail: newDetail }
      );
      expect(result).toEqual(updatedCriteria);
    });

    it('handles API errors', async () => {
      const error = new Error('API Error');
      mockedAxios.post.mockRejectedValueOnce(error);

      await expect(successCriteriaService.addSuccessCriteriaDetail('1', 'New test')).rejects.toThrow('API Error');
    });
  });
}); 