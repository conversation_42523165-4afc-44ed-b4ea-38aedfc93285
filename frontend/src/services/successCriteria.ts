import axios from 'axios';
import { API_ENDPOINTS } from '../utils/apiConfig';

export interface SuccessCriteria {
  id: string;
  name: string;
  description: string;
  status: 'success' | 'pending' | 'failed';
  category: 'functionality' | 'performance' | 'security';
  details: string[];
  lastUpdated: string;
}

export interface SuccessCriteriaResponse {
  data: SuccessCriteria[];
  total: number;
  page: number;
  pageSize: number;
}

export interface SuccessCriteriaFilters {
  category?: 'functionality' | 'performance' | 'security';
  status?: 'success' | 'pending' | 'failed';
  page?: number;
  pageSize?: number;
}

class SuccessCriteriaService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = API_ENDPOINTS.SUCCESS_CRITERIA;
  }

  async getSuccessCriteria(filters?: SuccessCriteriaFilters): Promise<SuccessCriteriaResponse> {
    try {
      const response = await axios.get<SuccessCriteriaResponse>(this.baseUrl, {
        params: filters,
      });
      return response.data;
    } catch (error) {
      console.error('Error fetching success criteria:', error);
      throw error;
    }
  }

  async getSuccessCriteriaById(id: string): Promise<SuccessCriteria> {
    try {
      const response = await axios.get<SuccessCriteria>(`${this.baseUrl}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching success criteria with id ${id}:`, error);
      throw error;
    }
  }

  async updateSuccessCriteriaStatus(
    id: string,
    status: 'success' | 'pending' | 'failed'
  ): Promise<SuccessCriteria> {
    try {
      const response = await axios.patch<SuccessCriteria>(`${this.baseUrl}/${id}`, {
        status,
      });
      return response.data;
    } catch (error) {
      console.error(`Error updating success criteria status for id ${id}:`, error);
      throw error;
    }
  }

  async addSuccessCriteriaDetail(
    id: string,
    detail: string
  ): Promise<SuccessCriteria> {
    try {
      const response = await axios.post<SuccessCriteria>(`${this.baseUrl}/${id}/details`, {
        detail,
      });
      return response.data;
    } catch (error) {
      console.error(`Error adding detail to success criteria with id ${id}:`, error);
      throw error;
    }
  }
}

export const successCriteriaService = new SuccessCriteriaService(); 