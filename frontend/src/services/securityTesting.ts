import axios from 'axios';
import { API_ENDPOINTS } from '../utils/apiConfig';

export interface SecurityTest {
  id: string;
  name: string;
  type: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  lastRun?: string;
  result?: string;
}

export interface SecurityTestResult {
  timestamp: string;
  status: 'passed' | 'failed' | 'warning';
  details: string;
  vulnerabilities?: {
    severity: 'low' | 'medium' | 'high' | 'critical';
    description: string;
    recommendation: string;
  }[];
}

export interface SecurityTestListResponse {
  items: SecurityTest[];
}

export interface SecurityTestResponse {
  test: SecurityTest;
  result?: SecurityTestResult;
}

class SecurityTestingService {
  private token: string;

  constructor(token: string) {
    this.token = token;
  }

  private getHeaders() {
    return {
      Authorization: `Bearer ${this.token}`,
    };
  }

  async getTests(): Promise<SecurityTestListResponse> {
    const response = await axios.get<SecurityTestListResponse>(
      API_ENDPOINTS.SECURITY_TESTING.BASE,
      { headers: this.getHeaders() }
    );
    return response.data;
  }

  async getTestById(id: string): Promise<SecurityTestResponse> {
    const response = await axios.get<SecurityTestResponse>(
      API_ENDPOINTS.SECURITY_TESTING.BY_ID(id),
      { headers: this.getHeaders() }
    );
    return response.data;
  }

  async runTest(id: string): Promise<SecurityTestResponse> {
    const response = await axios.post<SecurityTestResponse>(
      API_ENDPOINTS.SECURITY_TESTING.RUN(id),
      {},
      { headers: this.getHeaders() }
    );
    return response.data;
  }

  async getTestResults(id: string): Promise<SecurityTestResult> {
    const response = await axios.get<SecurityTestResult>(
      API_ENDPOINTS.SECURITY_TESTING.RESULTS(id),
      { headers: this.getHeaders() }
    );
    return response.data;
  }
}

export default SecurityTestingService; 