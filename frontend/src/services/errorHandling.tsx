import React from 'react';
import { SnackbarKey } from 'notistack';
import { Button } from '@mui/material';

export interface PerformanceMetric {
  type: 'page_load' | 'api_response' | 'component_render';
  name: string;
  duration: number;
  timestamp: string;
  additionalInfo?: Record<string, any>;
  user?: {
    user_id: string;
    username: string;
  };
}

export interface ErrorDetails {
  code?: string;
  message: string;
  timestamp: string;
  component?: string;
  stack?: string;
  context?: Record<string, any>;
}

export interface ErrorHandlerOptions {
  showNotification?: boolean;
  logToConsole?: boolean;
  logToServer?: boolean;
  retryable?: boolean;
  context?: Record<string, any>;
}

class ErrorHandlingService {
  private static instance: ErrorHandlingService;
  private enqueueSnackbar?: (message: string, options: any) => SnackbarKey;

  private constructor() {}

  static getInstance(): ErrorHandlingService {
    if (!ErrorHandlingService.instance) {
      ErrorHandlingService.instance = new ErrorHandlingService();
    }
    return ErrorHandlingService.instance;
  }

  setSnackbar(enqueueSnackbar: (message: string, options: any) => SnackbarKey) {
    this.enqueueSnackbar = enqueueSnackbar;
  }

  private formatErrorDetails(error: any, component?: string, context?: Record<string, any>): ErrorDetails {
    const details: ErrorDetails = {
      message: error?.message || 'An unknown error occurred',
      timestamp: new Date().toISOString(),
      component,
      context,
    };

    if (error?.response) {
      details.code = error.response.status.toString();
      if (!details.context) details.context = {};
      details.context = {
        ...details.context,
        status: error.response.status,
        data: error.response.data,
        headers: error.response.headers,
      };
    }

    if (error?.stack) {
      details.stack = error.stack;
    }

    return details;
  }

  private getErrorMessage(error: any): string {
    if (error?.response) {
      const status = error.response.status;
      if (status === 400) {
        return error.response.data?.detail || 'Invalid request format';
      } else if (status === 401) {
        return 'Your session has expired. Please log in again';
      } else if (status === 403) {
        return 'You do not have permission to perform this action';
      } else if (status === 404) {
        return 'The requested resource was not found';
      } else if (status === 429) {
        return 'Too many requests. Please try again later';
      } else if (status >= 500) {
        return 'A server error occurred. Please try again later';
      }
    }

    if (error?.message?.includes('Network Error')) {
      return 'Network error. Please check your internet connection';
    }

    if (error?.message?.includes('timeout')) {
      return 'The request timed out. Please try again';
    }

    return error?.message || 'An unknown error occurred';
  }

  private logToConsole(details: ErrorDetails) {
    console.group(`[Error] ${details.component || 'Unknown Component'}`);
    console.error('Error Details:', details);
    console.groupEnd();
  }

  private async logToServer(details: ErrorDetails) {
    try {
      await fetch('/api/v1/logs/error', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(details),
      });
    } catch (err) {
      console.error('Failed to log error to server:', err);
    }
  }

  private showNotification(message: string, options: any = {}) {
    if (this.enqueueSnackbar) {
      this.enqueueSnackbar(message, {
        variant: 'error',
        autoHideDuration: 5000,
        ...options,
      });
    }
  }

  async handleError(
    error: any,
    component?: string,
    options: ErrorHandlerOptions = {}
  ): Promise<ErrorDetails> {
    const {
      showNotification = true,
      logToConsole = true,
      logToServer = true,
      retryable = false,
      context,
    } = options;

    const details = this.formatErrorDetails(error, component, context);
    const message = this.getErrorMessage(error);

    if (logToConsole) {
      this.logToConsole(details);
    }

    if (logToServer) {
      await this.logToServer(details);
    }

    if (showNotification) {
      this.showNotification(message, {
        action: retryable
          ? (key: SnackbarKey) => {
              return (
                <Button
                  size="small"
                  color="inherit"
                  onClick={() => {
                    console.log('Retry clicked', key);
                  }}
                >
                  Retry
                </Button>
              );
            }
          : undefined,
      });
    }

    return details;
  }

  createErrorBoundaryHandler(component: string) {
    return async (error: Error, errorInfo: React.ErrorInfo) => {
      return this.handleError(error, component, {
        context: { componentStack: errorInfo.componentStack },
      });
    };
  }
}

export const errorHandlingService = ErrorHandlingService.getInstance(); 