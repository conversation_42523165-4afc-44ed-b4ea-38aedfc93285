import { API_ENDPOINTS } from '../utils/apiConfig';

export interface DataItem {
  id: string;
  name: string;
  description: string;
  type: string;
  created_at: string;
  updated_at: string;
}

export interface CreateDataItemDto {
  name: string;
  description: string;
  type: string;
}

export interface UpdateDataItemDto extends CreateDataItemDto {
  id: string;
}

class DataManagementService {
  async getAllItems(): Promise<DataItem[]> {
    const response = await fetch(API_ENDPOINTS.DATA.LIST);
    if (!response.ok) {
      throw new Error('Failed to fetch data items');
    }
    return response.json();
  }

  async getItemById(id: string): Promise<DataItem> {
    const response = await fetch(API_ENDPOINTS.DATA.GET.replace(':id', id));
    if (!response.ok) {
      throw new Error('Failed to fetch data item');
    }
    return response.json();
  }

  async createItem(data: CreateDataItemDto): Promise<DataItem> {
    const response = await fetch(API_ENDPOINTS.DATA.CREATE, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error('Failed to create data item');
    }
    return response.json();
  }

  async updateItem(data: UpdateDataItemDto): Promise<DataItem> {
    const response = await fetch(API_ENDPOINTS.DATA.UPDATE.replace(':id', data.id), {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error('Failed to update data item');
    }
    return response.json();
  }

  async deleteItem(id: string): Promise<void> {
    const response = await fetch(API_ENDPOINTS.DATA.DELETE.replace(':id', id), {
      method: 'DELETE',
    });
    if (!response.ok) {
      throw new Error('Failed to delete data item');
    }
  }
}

export const dataManagementService = new DataManagementService(); 