import React, { useEffect, useState } from 'react';
import { 
  AppBar, 
  Toolbar, 
  Typography, 
  Container, 
  Card, 
  CardContent, 
  Grid, 
  Box,
  Chip,
  CircularProgress
} from '@mui/material';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import axios from 'axios';

const theme = createTheme({
  palette: {
    mode: 'light',
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

interface HealthStatus {
  status: string;
  timestamp: string;
  version?: string;
}

function App() {
  const [healthStatus, setHealthStatus] = useState<HealthStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkHealth = async () => {
      try {
        const response = await axios.get('/api/v1/health');
        setHealthStatus(response.data);
        setError(null);
      } catch (err) {
        setError('Failed to connect to API');
        console.error('Health check failed:', err);
      } finally {
        setLoading(false);
      }
    };

    checkHealth();
    const interval = setInterval(checkHealth, 30000); // Check every 30 seconds

    return () => clearInterval(interval);
  }, []);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AppBar position="static">
        <Toolbar>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            TurdParty - VM Management Platform
          </Typography>
          {healthStatus && (
            <Chip 
              label={healthStatus.status === 'healthy' ? 'Online' : 'Offline'} 
              color={healthStatus.status === 'healthy' ? 'success' : 'error'}
              variant="outlined"
            />
          )}
        </Toolbar>
      </AppBar>

      <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
        <Grid container spacing={3}>
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h4" component="h1" gutterBottom>
                  Welcome to TurdParty
                </Typography>
                <Typography variant="body1" color="text.secondary" paragraph>
                  A powerful platform for VM management, file handling, and AppImage deployment.
                </Typography>
                
                <Box sx={{ mt: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    System Status
                  </Typography>
                  {loading ? (
                    <Box display="flex" alignItems="center" gap={2}>
                      <CircularProgress size={20} />
                      <Typography>Checking system status...</Typography>
                    </Box>
                  ) : error ? (
                    <Chip label={error} color="error" />
                  ) : healthStatus ? (
                    <Box>
                      <Chip 
                        label={`API: ${healthStatus.status}`} 
                        color={healthStatus.status === 'healthy' ? 'success' : 'error'}
                        sx={{ mr: 1 }}
                      />
                      {healthStatus.version && (
                        <Chip 
                          label={`Version: ${healthStatus.version}`} 
                          variant="outlined"
                        />
                      )}
                    </Box>
                  ) : null}
                </Box>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  VM Management
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Create, start, stop, and manage Vagrant VMs with ease.
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  File Handling
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Upload, download, and manage files with MinIO storage.
                </Typography>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  AppImage Deployment
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Deploy and execute AppImages on virtual machines.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Container>
    </ThemeProvider>
  );
}

export default App;
