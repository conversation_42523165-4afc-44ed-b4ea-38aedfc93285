import React, { useState, useEffect } from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Button,
  IconButton,
  Typography,
  Box,
  Chip,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  PlayArrow,
  Stop,
  Pause,
  PlayArrow as Resume,
  Settings,
  Refresh,
} from '@mui/icons-material';
import { useSnackbar } from 'notistack';
import { useNavigate } from 'react-router-dom';
import { API_ENDPOINTS } from '../../utils/apiConfig';

interface VMInfo {
  vm_id: string;
  name: string;
  template: string;
  vm_type: string;
  status: string;
  memory_mb: number;
  cpus: number;
  runtime_minutes: number;
  is_expired: boolean;
  ip_address?: string;
  created_at: string;
  started_at?: string;
}

interface VMActionResult {
  vm_id: string;
  action: string;
  status: string;
  message: string;
}

const VMList: React.FC = () => {
  const [vms, setVms] = useState<VMInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedVM, setSelectedVM] = useState<VMInfo | null>(null);
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const { enqueueSnackbar } = useSnackbar();
  const navigate = useNavigate();

  const fetchVMs = async () => {
    try {
      const response = await fetch(API_ENDPOINTS.VMS.LIST);
      if (!response.ok) throw new Error('Failed to fetch VMs');
      const data = await response.json();
      setVms(data.vms || data); // Handle both paginated and direct array responses
    } catch (error) {
      enqueueSnackbar('Error fetching VMs', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchVMs();
    const interval = setInterval(fetchVMs, 5000); // Refresh every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const handleVMOperation = async (vmId: string, operation: string) => {
    try {
      const response = await fetch(API_ENDPOINTS.VMS.ACTION(vmId), {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: operation,
          force: false
        }),
      });

      if (!response.ok) throw new Error(`Failed to ${operation} VM`);
      const result: VMActionResult = await response.json();

      enqueueSnackbar(`VM ${operation} ${result.message}`, { variant: 'success' });
      fetchVMs();
    } catch (error) {
      enqueueSnackbar(`Error ${operation}ing VM: ${error instanceof Error ? error.message : 'Unknown error'}`, {
        variant: 'error',
      });
    }
  };

  const handleVMClick = (vmId: string) => {
    navigate(`/vms/${vmId}`);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'running':
        return 'success';
      case 'creating':
        return 'info';
      case 'terminating':
        return 'warning';
      case 'terminated':
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  const filteredVMs = vms.filter((vm: VMInfo) =>
    vm.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
        <Typography variant="h4">Virtual Machines</Typography>
        <Box>
          <TextField
            label="Search VMs"
            variant="outlined"
            size="small"
            value={searchTerm}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
            sx={{ mr: 2 }}
          />
          <IconButton onClick={fetchVMs} disabled={loading}>
            <Refresh />
          </IconButton>
        </Box>
      </Box>

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Template</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Resources</TableCell>
              <TableCell>Runtime</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {filteredVMs.map((vm: VMInfo) => (
              <TableRow
                key={vm.vm_id}
                hover
                onClick={() => handleVMClick(vm.vm_id)}
                sx={{ cursor: 'pointer' }}
              >
                <TableCell>{vm.name}</TableCell>
                <TableCell>
                  <Chip
                    label={vm.status}
                    color={getStatusColor(vm.status)}
                    size="small"
                  />
                  {vm.is_expired && (
                    <Chip
                      label="EXPIRED"
                      color="error"
                      size="small"
                      sx={{ ml: 1 }}
                    />
                  )}
                </TableCell>
                <TableCell>{vm.template}</TableCell>
                <TableCell>
                  <Chip
                    label={vm.vm_type}
                    variant="outlined"
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  {vm.memory_mb}MB / {vm.cpus} CPU{vm.cpus > 1 ? 's' : ''}
                </TableCell>
                <TableCell>
                  {Math.round(vm.runtime_minutes)}m
                  {vm.ip_address && (
                    <Typography variant="caption" display="block">
                      {vm.ip_address}
                    </Typography>
                  )}
                </TableCell>
                <TableCell>
                  <IconButton
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      handleVMOperation(vm.vm_id, 'start');
                    }}
                    disabled={vm.status === 'running' || vm.status === 'creating'}
                  >
                    <PlayArrow />
                  </IconButton>
                  <IconButton
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      handleVMOperation(vm.vm_id, 'stop');
                    }}
                    disabled={vm.status === 'terminated' || vm.status === 'terminating'}
                  >
                    <Stop />
                  </IconButton>
                  <IconButton
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      handleVMOperation(vm.vm_id, 'restart');
                    }}
                    disabled={vm.status !== 'running'}
                  >
                    <Refresh />
                  </IconButton>
                  <IconButton
                    onClick={(e: React.MouseEvent) => {
                      e.stopPropagation();
                      setSelectedVM(vm);
                      setConfigDialogOpen(true);
                    }}
                  >
                    <Settings />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* VM Configuration Dialog */}
      <Dialog
        open={configDialogOpen}
        onClose={() => setConfigDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>VM Configuration</DialogTitle>
        <DialogContent>
          {selectedVM && (
            <Box sx={{ pt: 2 }}>
              <Typography variant="body1" gutterBottom>
                <strong>Name:</strong> {selectedVM.name}
              </Typography>
              <Typography variant="body1" gutterBottom>
                <strong>Template:</strong> {selectedVM.template}
              </Typography>
              <Typography variant="body1" gutterBottom>
                <strong>Type:</strong> {selectedVM.vm_type}
              </Typography>
              <Typography variant="body1" gutterBottom>
                <strong>Resources:</strong> {selectedVM.memory_mb}MB RAM, {selectedVM.cpus} CPU{selectedVM.cpus > 1 ? 's' : ''}
              </Typography>
              <Typography variant="body1" gutterBottom>
                <strong>Runtime:</strong> {Math.round(selectedVM.runtime_minutes)} minutes
              </Typography>
              {selectedVM.ip_address && (
                <Typography variant="body1" gutterBottom>
                  <strong>IP Address:</strong> {selectedVM.ip_address}
                </Typography>
              )}
              <Typography variant="body1" gutterBottom>
                <strong>Created:</strong> {new Date(selectedVM.created_at).toLocaleString()}
              </Typography>
              {selectedVM.started_at && (
                <Typography variant="body1" gutterBottom>
                  <strong>Started:</strong> {new Date(selectedVM.started_at).toLocaleString()}
                </Typography>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfigDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default VMList; 