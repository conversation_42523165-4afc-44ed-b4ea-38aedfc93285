import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  IconButton,
  Chip,
  CircularProgress,
  Divider,
} from '@mui/material';
import {
  PlayArrow,
  Stop,
  Pause,
  PlayArrow as Resume,
  Refresh,
} from '@mui/icons-material';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';
import { useSnackbar } from 'notistack';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend
);

interface VagrantVMInfo {
  vm_id: string;
  state: string;
  name: string;
  provider: string;
  directory: string;
  network: {
    private_network: string;
  };
}

interface VagrantCommandResult {
  success: boolean;
  message: string;
  error: string | null;
}

interface VMStatus {
  cpu_usage: number;
  memory_usage: number;
  disk_usage: number;
  network_usage: number;
  timestamp: string;
}

const VMDetail: React.FC = () => {
  const { vmId } = useParams<{ vmId: string }>();
  const [vm, setVM] = useState<VagrantVMInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [metrics, setMetrics] = useState<VMStatus[]>([]);
  const { enqueueSnackbar } = useSnackbar();

  const fetchVMDetails = async () => {
    if (!vmId) return;
    try {
      const response = await fetch(`/api/vagrant/vms/${vmId}`);
      if (!response.ok) throw new Error('Failed to fetch VM details');
      const data = await response.json();
      setVM(data);
    } catch (error) {
      enqueueSnackbar('Error fetching VM details', { variant: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const fetchMetrics = async () => {
    if (!vmId) return;
    try {
      const response = await fetch(`/api/vagrant/vms/${vmId}/status`);
      if (!response.ok) throw new Error('Failed to fetch VM metrics');
      const data = await response.json();
      setMetrics(data);
    } catch (error) {
      enqueueSnackbar('Error fetching VM metrics', { variant: 'error' });
    }
  };

  useEffect(() => {
    fetchVMDetails();
    fetchMetrics();
    const interval = setInterval(() => {
      fetchVMDetails();
      fetchMetrics();
    }, 5000); // Refresh every 5 seconds
    return () => clearInterval(interval);
  }, [vmId]);

  const handleVMOperation = async (operation: string) => {
    if (!vmId) return;
    try {
      const endpoint = operation === 'start' ? 'up' : 
                      operation === 'stop' ? 'halt' :
                      operation === 'pause' ? 'suspend' : 'resume';
      
      const response = await fetch(`/api/vagrant/vms/${vmId}/${endpoint}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ provision: true }),
      });
      
      if (!response.ok) throw new Error(`Failed to ${operation} VM`);
      const result: VagrantCommandResult = await response.json();
      
      if (result.success) {
        enqueueSnackbar(`VM ${operation}ed successfully`, { variant: 'success' });
        fetchVMDetails();
      } else {
        throw new Error(result.message || result.error || 'Operation failed');
      }
    } catch (error) {
      enqueueSnackbar(`Error ${operation}ing VM: ${error instanceof Error ? error.message : 'Unknown error'}`, {
        variant: 'error',
      });
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!vm) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h5" color="error">VM not found</Typography>
      </Box>
    );
  }

  const chartData = {
    labels: metrics.map((m: VMStatus) => new Date(m.timestamp).toLocaleTimeString()),
    datasets: [
      {
        label: 'CPU Usage (%)',
        data: metrics.map((m: VMStatus) => m.cpu_usage),
        borderColor: 'rgb(75, 192, 192)',
        tension: 0.1,
      },
      {
        label: 'Memory Usage (%)',
        data: metrics.map((m: VMStatus) => m.memory_usage),
        borderColor: 'rgb(255, 99, 132)',
        tension: 0.1,
      },
      {
        label: 'Disk Usage (%)',
        data: metrics.map((m: VMStatus) => m.disk_usage),
        borderColor: 'rgb(53, 162, 235)',
        tension: 0.1,
      },
      {
        label: 'Network Usage (MB/s)',
        data: metrics.map((m: VMStatus) => m.network_usage),
        borderColor: 'rgb(255, 205, 86)',
        tension: 0.1,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'VM Resource Usage',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 100,
      },
    },
  };

  return (
    <Box sx={{ p: 3 }}>
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h4">{vm.name}</Typography>
          <Box>
            <IconButton
              onClick={() => handleVMOperation('start')}
              disabled={vm.state === 'running'}
              sx={{ mr: 1 }}
            >
              <PlayArrow />
            </IconButton>
            <IconButton
              onClick={() => handleVMOperation('stop')}
              disabled={vm.state === 'poweroff' || vm.state === 'stopped'}
              sx={{ mr: 1 }}
            >
              <Stop />
            </IconButton>
            <IconButton
              onClick={() => handleVMOperation('pause')}
              disabled={vm.state !== 'running'}
              sx={{ mr: 1 }}
            >
              <Pause />
            </IconButton>
            <IconButton
              onClick={() => handleVMOperation('resume')}
              disabled={vm.state !== 'suspended'}
              sx={{ mr: 1 }}
            >
              <Resume />
            </IconButton>
            <IconButton onClick={fetchVMDetails}>
              <Refresh />
            </IconButton>
          </Box>
        </Box>

        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <Typography variant="subtitle1" gutterBottom>
              <strong>Status:</strong>{' '}
              <Chip
                label={vm.state}
                color={
                  vm.state === 'running'
                    ? 'success'
                    : vm.state === 'poweroff' || vm.state === 'stopped'
                    ? 'error'
                    : vm.state === 'suspended'
                    ? 'warning'
                    : 'default'
                }
                size="small"
              />
            </Typography>
            <Typography variant="subtitle1" gutterBottom>
              <strong>Provider:</strong> {vm.provider}
            </Typography>
            <Typography variant="subtitle1" gutterBottom>
              <strong>Directory:</strong> {vm.directory}
            </Typography>
            <Typography variant="subtitle1" gutterBottom>
              <strong>Network:</strong> {vm.network.private_network}
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      <Paper sx={{ p: 3 }}>
        <Typography variant="h5" gutterBottom>
          Resource Usage
        </Typography>
        <Divider sx={{ mb: 3 }} />
        <Line options={chartOptions} data={chartData} />
      </Paper>
    </Box>
  );
};

export default VMDetail; 