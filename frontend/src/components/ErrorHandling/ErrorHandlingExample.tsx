import React, { useState } from 'react';
import {
  Box,
  Button,
  Typography,
  Paper,
  Stack,
} from '@mui/material';
import { ErrorBoundary, ErrorDisplay } from './';
import { useErrorHandling } from '../../hooks/useErrorHandling';

// Example component that might throw an error
const ErrorProneComponent: React.FC = () => {
  const [shouldError, setShouldError] = useState(false);

  if (shouldError) {
    throw new Error('This is an example error!');
  }

  return (
    <Button
      variant="contained"
      color="error"
      onClick={() => setShouldError(true)}
    >
      Trigger Error
    </Button>
  );
};

// Example component that uses the error handling hook
const ApiCallExample: React.FC = () => {
  const { handleError } = useErrorHandling();

  const handleApiCall = async () => {
    try {
      // Simulate an API call that might fail
      const response = await fetch('https://api.example.com/data');
      if (!response.ok) {
        throw new Error('API call failed');
      }
    } catch (error) {
      await handleError(error, 'ApiCallExample', {
        retryable: true,
        context: { endpoint: 'https://api.example.com/data' },
      });
    }
  };

  return (
    <Button
      variant="contained"
      color="primary"
      onClick={handleApiCall}
    >
      Make API Call
    </Button>
  );
};

const ErrorHandlingExample: React.FC = () => {
  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h5" gutterBottom>
        Error Handling Examples
      </Typography>

      <Stack spacing={3}>
        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            1. Error Boundary Example
          </Typography>
          <Typography variant="body2" paragraph>
            This example shows how to use the ErrorBoundary component to catch and display errors
            in a specific part of your application.
          </Typography>
          <ErrorBoundary
            componentName="ErrorProneComponent"
            showDetails={true}
          >
            <ErrorProneComponent />
          </ErrorBoundary>
        </Paper>

        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            2. Error Handling Hook Example
          </Typography>
          <Typography variant="body2" paragraph>
            This example demonstrates how to use the useErrorHandling hook to handle errors
            from API calls or other async operations.
          </Typography>
          <ApiCallExample />
        </Paper>

        <Paper sx={{ p: 2 }}>
          <Typography variant="h6" gutterBottom>
            3. Error Display Example
          </Typography>
          <Typography variant="body2" paragraph>
            This example shows how to use the ErrorDisplay component directly to show
            custom error messages.
          </Typography>
          <ErrorDisplay
            title="Custom Error"
            message="This is a custom error message"
            type="error"
            details="Additional error details can be shown here"
            showDetails={true}
            onRetry={() => console.log('Retry clicked')}
          />
        </Paper>
      </Stack>
    </Box>
  );
};

export default ErrorHandlingExample; 