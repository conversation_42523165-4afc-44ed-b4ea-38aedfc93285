import React, { Component, ErrorInfo, ReactNode } from 'react';
import { errorHandlingService } from '../../services/errorHandling';
import ErrorDisplay from './ErrorDisplay';

interface Props {
  children: ReactNode;
  componentName?: string;
  fallback?: ReactNode;
  showDetails?: boolean;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    this.setState({ errorInfo });

    // Log error using our error handling service
    errorHandlingService.handleError(error, this.props.componentName, {
      showNotification: false, // Don't show notification since we're showing the error boundary
      context: { componentStack: errorInfo.componentStack },
    });

    // Call the optional onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = (): void => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });
  };

  render(): ReactNode {
    const { hasError, error, errorInfo } = this.state;
    const { children, fallback, componentName, showDetails } = this.props;

    if (hasError) {
      if (fallback) {
        return fallback;
      }

      return (
        <ErrorDisplay
          title={`Error in ${componentName || 'Component'}`}
          message={error?.message || 'An unexpected error occurred'}
          type="error"
          details={errorInfo?.componentStack || undefined}
          stack={error?.stack}
          timestamp={new Date().toISOString()}
          onRetry={this.handleRetry}
          showDetails={showDetails}
        />
      );
    }

    return children;
  }
}

export default ErrorBoundary; 