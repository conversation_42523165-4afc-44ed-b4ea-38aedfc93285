import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>ertT<PERSON>le,
  Box,
  Button,
  Card,
  CardContent,
  Collapse,
  IconButton,
  Typography,
} from '@mui/material';
import {
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Refresh as RefreshIcon,
  ExpandMore as ExpandMoreIcon,
} from '@mui/icons-material';

export interface ErrorDisplayProps {
  title?: string;
  message: string;
  type?: 'error' | 'warning' | 'info';
  code?: string;
  details?: string;
  stack?: string;
  timestamp?: string;
  onRetry?: () => void;
  onClose?: () => void;
  showDetails?: boolean;
}

const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  title,
  message,
  type = 'error',
  code,
  details,
  stack,
  timestamp,
  onRetry,
  onClose,
  showDetails = false,
}) => {
  const [expanded, setExpanded] = React.useState(false);

  const getIcon = () => {
    switch (type) {
      case 'error':
        return <ErrorIcon />;
      case 'warning':
        return <WarningIcon />;
      case 'info':
        return <InfoIcon />;
      default:
        return <ErrorIcon />;
    }
  };

  const getDefaultTitle = () => {
    switch (type) {
      case 'error':
        return 'Error';
      case 'warning':
        return 'Warning';
      case 'info':
        return 'Information';
      default:
        return 'Error';
    }
  };

  return (
    <Card variant="outlined" sx={{ mb: 2 }}>
      <Alert
        severity={type}
        icon={getIcon()}
        action={
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {onRetry && (
              <IconButton
                color="inherit"
                size="small"
                onClick={onRetry}
                sx={{ mr: 1 }}
              >
                <RefreshIcon />
              </IconButton>
            )}
            {showDetails && (
              <IconButton
                size="small"
                onClick={() => setExpanded(!expanded)}
                sx={{
                  transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
                  transition: 'transform 0.2s',
                }}
              >
                <ExpandMoreIcon />
              </IconButton>
            )}
          </Box>
        }
        onClose={onClose}
      >
        <AlertTitle>{title || getDefaultTitle()}</AlertTitle>
        {message}
        {code && (
          <Typography variant="caption" display="block" sx={{ mt: 1 }}>
            Error Code: {code}
          </Typography>
        )}
      </Alert>

      {showDetails && (
        <Collapse in={expanded}>
          <CardContent>
            {details && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Details
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {details}
                </Typography>
              </Box>
            )}

            {stack && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Stack Trace
                </Typography>
                <Typography
                  variant="body2"
                  component="pre"
                  sx={{
                    p: 1,
                    bgcolor: 'background.default',
                    borderRadius: 1,
                    overflow: 'auto',
                    fontSize: '0.75rem',
                  }}
                >
                  {stack}
                </Typography>
              </Box>
            )}

            {timestamp && (
              <Typography variant="caption" color="text.secondary">
                Occurred at: {new Date(timestamp).toLocaleString()}
              </Typography>
            )}

            {onRetry && (
              <Box sx={{ mt: 2 }}>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<RefreshIcon />}
                  onClick={onRetry}
                >
                  Retry
                </Button>
              </Box>
            )}
          </CardContent>
        </Collapse>
      )}
    </Card>
  );
};

export default ErrorDisplay; 