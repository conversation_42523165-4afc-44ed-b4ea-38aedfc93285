import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Refresh as RefreshIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';
import IsolatedTestService from '../../services/isolatedTest';
import IsolatedTestDetails from './IsolatedTestDetails';

interface IsolatedTest {
  id: string;
  name: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startTime?: string;
  endTime?: string;
  duration?: string;
  result?: string;
}

const IsolatedTestDashboard: React.FC = () => {
  const [tests, setTests] = useState<IsolatedTest[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedTest, setSelectedTest] = useState<IsolatedTest | null>(null);
  const [detailsOpen, setDetailsOpen] = useState(false);
  const { token } = useAuth();
  const isolatedTestService = React.useMemo(() => {
    if (!token) return null;
    return new IsolatedTestService(token);
  }, [token]);

  useEffect(() => {
    if (!isolatedTestService) {
      setError('Authentication required');
      return;
    }
    loadTests();
  }, [isolatedTestService]);

  const loadTests = async () => {
    if (!isolatedTestService) return;
    
    setLoading(true);
    try {
      const testList = await isolatedTestService.getTests();
      setTests(testList);
      setError(null);
    } catch (err) {
      setError('Failed to load tests');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleRunTest = async (testId: string) => {
    if (!isolatedTestService) return;
    
    try {
      await isolatedTestService.runTest(testId);
      loadTests(); // Refresh the list
    } catch (err) {
      setError('Failed to run test');
      console.error(err);
    }
  };

  const handleStopTest = async (testId: string) => {
    if (!isolatedTestService) return;
    
    try {
      await isolatedTestService.stopTest(testId);
      loadTests(); // Refresh the list
    } catch (err) {
      setError('Failed to stop test');
      console.error(err);
    }
  };

  const handleViewDetails = (test: IsolatedTest) => {
    setSelectedTest(test);
    setDetailsOpen(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'primary';
      case 'completed':
        return 'success';
      case 'failed':
        return 'error';
      default:
        return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <Typography variant="h5" sx={{ flexGrow: 1 }}>
              Isolated Tests
            </Typography>
            <Button
              variant="contained"
              startIcon={<RefreshIcon />}
              onClick={loadTests}
            >
              Refresh
            </Button>
          </Box>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Start Time</TableCell>
                  <TableCell>Duration</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {tests.map((test) => (
                  <TableRow key={test.id}>
                    <TableCell>{test.name}</TableCell>
                    <TableCell>
                      <Chip
                        label={test.status.toUpperCase()}
                        color={getStatusColor(test.status)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{test.startTime || '-'}</TableCell>
                    <TableCell>{test.duration || '-'}</TableCell>
                    <TableCell>
                      <IconButton
                        onClick={() => handleViewDetails(test)}
                        size="small"
                      >
                        <ViewIcon />
                      </IconButton>
                      {test.status === 'pending' && (
                        <IconButton
                          onClick={() => handleRunTest(test.id)}
                          size="small"
                          color="primary"
                        >
                          <PlayIcon />
                        </IconButton>
                      )}
                      {test.status === 'running' && (
                        <IconButton
                          onClick={() => handleStopTest(test.id)}
                          size="small"
                          color="error"
                        >
                          <StopIcon />
                        </IconButton>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Grid>
      </Grid>

      <Dialog
        open={detailsOpen}
        onClose={() => setDetailsOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Test Details</DialogTitle>
        <DialogContent>
          {selectedTest && (
            <IsolatedTestDetails
              testId={selectedTest.id}
              onClose={() => setDetailsOpen(false)}
              onRerun={() => {
                handleRunTest(selectedTest.id);
                setDetailsOpen(false);
              }}
            />
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default IsolatedTestDashboard; 