import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  List,
  ListItem,
  ListItemText,
  Chip,
  Alert,
  CircularProgress,
  Divider,
} from '@mui/material';
import {
  Schedule as ScheduleIcon,
  BugReport as BugIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';
import IsolatedTestService, { IsolatedTestResult } from '../../services/isolatedTest';

interface IsolatedTestDetailsProps {
  testId: string;
  onClose?: () => void;
  onRerun?: () => void;
}

const IsolatedTestDetails: React.FC<IsolatedTestDetailsProps> = ({
  testId,
  onClose,
  onRerun,
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [testDetails, setTestDetails] = useState<IsolatedTestResult | null>(null);
  const { token } = useAuth();
  const isolatedTestService = React.useMemo(() => {
    if (!token) return null;
    return new IsolatedTestService(token);
  }, [token]);

  useEffect(() => {
    if (!isolatedTestService) {
      setError('Authentication required');
      return;
    }
    loadTestDetails();
  }, [isolatedTestService, testId]);

  const loadTestDetails = async () => {
    if (!isolatedTestService) return;
    
    setLoading(true);
    try {
      const details = await isolatedTestService.getTestResults(testId);
      setTestDetails(details);
      setError(null);
    } catch (err) {
      setError('Failed to load test details');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <SuccessIcon color="success" />;
      case 'failed':
        return <ErrorIcon color="error" />;
      default:
        return <BugIcon color="action" />;
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!testDetails) {
    return (
      <Alert severity="info">
        No test details available
      </Alert>
    );
  }

  return (
    <Box sx={{ p: 2 }}>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            {getStatusIcon(testDetails.status)}
            <Typography variant="h6" sx={{ ml: 1 }}>
              Test Details
            </Typography>
            <Chip
              label={testDetails.status.toUpperCase()}
              color={testDetails.status === 'completed' ? 'success' : 'error'}
              size="small"
              sx={{ ml: 2 }}
            />
          </Box>
          <Divider sx={{ mb: 2 }} />
        </Grid>

        <Grid item xs={12}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <ScheduleIcon sx={{ mr: 1 }} />
            <Typography variant="subtitle1">
              Last Run: {testDetails.timestamp}
            </Typography>
          </Box>
        </Grid>

        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Test Results
          </Typography>
          <Typography>{testDetails.details}</Typography>
        </Grid>

        {testDetails.failures && testDetails.failures.length > 0 && (
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <ErrorIcon sx={{ mr: 1, color: 'error' }} />
              Test Failures
            </Typography>
            <List>
              {testDetails.failures.map((failure, index) => (
                <ListItem
                  key={index}
                  sx={{
                    flexDirection: 'column',
                    alignItems: 'flex-start',
                    bgcolor: 'background.paper',
                    borderRadius: 1,
                    mb: 1,
                  }}
                >
                  <ListItemText
                    primary={failure.description}
                    secondary={`Error: ${failure.error}`}
                  />
                </ListItem>
              ))}
            </List>
          </Grid>
        )}

        <Grid item xs={12}>
          <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
            <Button
              variant="contained"
              onClick={onRerun}
              startIcon={<BugIcon />}
            >
              Rerun Test
            </Button>
            <Button
              variant="outlined"
              onClick={onClose}
            >
              Close
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default IsolatedTestDetails; 