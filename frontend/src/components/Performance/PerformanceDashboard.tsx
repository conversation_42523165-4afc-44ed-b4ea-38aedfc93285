import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Grid,
  Typography,
  CircularProgress,
  Alert,
  Button,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  TextField,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import { useErrorHandling } from '../../hooks/useErrorHandling';
import { PerformanceMetric } from '../../services/errorHandling';
import PerformanceChart from './PerformanceChart';

interface PerformanceSummary {
  total_metrics: number;
  average_duration: number;
}

const PerformanceDashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [metricType, setMetricType] = useState<string>('');
  const [page, setPage] = useState<string>('');
  const [limit, setLimit] = useState<number>(10);
  const { handleError } = useErrorHandling();

  const fetchPerformanceData = async () => {
    try {
      setLoading(true);
      setError(null);
      const params = new URLSearchParams();
      if (limit) params.append('limit', limit.toString());
      if (metricType) params.append('type', metricType);
      if (page) params.append('page', page);
      const queryString = params.toString();
      const url = `/api/performance${queryString ? `?${queryString}` : ''}`;
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Failed to load performance data');
      }
      const data = await response.json();
      setMetrics(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPerformanceData();
  }, [metricType, page, limit]);

  const calculateSummary = (): PerformanceSummary => {
    const filteredMetrics = metrics.filter(metric => 
      (!metricType || metric.type === metricType) &&
      (!page || metric.name === page)
    ).slice(0, limit);

    const totalDuration = filteredMetrics.reduce((sum, metric) => sum + metric.duration, 0);
    return {
      total_metrics: filteredMetrics.length,
      average_duration: filteredMetrics.length > 0 ? totalDuration / filteredMetrics.length : 0
    };
  };

  const summary = calculateSummary();
  const uniqueMetricTypes = Array.from(new Set(metrics.map(m => m.type)));
  const uniquePages = Array.from(new Set(metrics.map(m => m.name)));

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert
        severity="error"
        action={
          <Button color="inherit" size="small" onClick={fetchPerformanceData}>
            Retry
          </Button>
        }
      >
        {error}
      </Alert>
    );
  }

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Performance Dashboard
      </Typography>
      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Paper sx={{ p: 2 }}>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <InputLabel id="metric-type-label">Metric Type</InputLabel>
                  <Select
                    labelId="metric-type-label"
                    id="metric-type"
                    value={metricType}
                    label="Metric Type"
                    onChange={(e) => setMetricType(e.target.value)}
                  >
                    <MenuItem value="">All</MenuItem>
                    {uniqueMetricTypes.map((type) => (
                      <MenuItem key={type} value={type}>{type}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={4}>
                <FormControl fullWidth>
                  <InputLabel id="page-label">Page/Endpoint</InputLabel>
                  <Select
                    labelId="page-label"
                    id="page"
                    value={page}
                    label="Page/Endpoint"
                    onChange={(e) => setPage(e.target.value)}
                  >
                    <MenuItem value="">All</MenuItem>
                    {uniquePages.map((pageName) => (
                      <MenuItem key={pageName} value={pageName}>{pageName}</MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} sm={4}>
                <TextField
                  fullWidth
                  type="number"
                  label="Limit"
                  value={limit}
                  onChange={(e) => setLimit(Number(e.target.value))}
                  inputProps={{ min: 1 }}
                />
              </Grid>
            </Grid>
          </Paper>
        </Grid>
        <Grid item xs={12} sm={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="textSecondary">
                Total Metrics
              </Typography>
              <Typography variant="h3">
                {summary.total_metrics}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="textSecondary">
                Average Duration
              </Typography>
              <Typography variant="h3">
                {summary.average_duration.toFixed(2)} ms
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12}>
          <PerformanceChart metrics={metrics} title="Performance Metrics" />
        </Grid>
        <Grid item xs={12}>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Timestamp</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Name</TableCell>
                  <TableCell>Duration (ms)</TableCell>
                  <TableCell>User</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {metrics
                  .filter(metric => 
                    (!metricType || metric.type === metricType) &&
                    (!page || metric.name === page)
                  )
                  .slice(0, limit)
                  .map((metric, index) => (
                    <TableRow key={index}>
                      <TableCell>{new Date(metric.timestamp).toLocaleString()}</TableCell>
                      <TableCell>{metric.type}</TableCell>
                      <TableCell>{metric.name}</TableCell>
                      <TableCell>{metric.duration}</TableCell>
                      <TableCell>{metric.user?.username}</TableCell>
                    </TableRow>
                  ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Grid>
      </Grid>
    </Box>
  );
};

export default PerformanceDashboard; 