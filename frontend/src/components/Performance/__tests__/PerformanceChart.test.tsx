import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material';
import PerformanceChart from '../PerformanceChart';
import { PerformanceMetric } from '../../../services/errorHandling';

// Mock Chart.js
jest.mock('chart.js', () => ({
  Chart: {
    register: jest.fn(),
    defaults: {
      font: { family: 'Roboto' }
    }
  },
  CategoryScale: jest.fn(),
  LinearScale: jest.fn(),
  BarElement: jest.fn(),
  Title: jest.fn(),
  Tooltip: jest.fn(),
  Legend: jest.fn()
}));

// Mock react-chartjs-2
jest.mock('react-chartjs-2', () => ({
  Bar: () => <div data-testid="mock-chart">Chart</div>
}));

const mockMetrics: PerformanceMetric[] = [
  {
    type: 'api_response',
    name: '/api/users',
    duration: 100,
    timestamp: '2024-03-20T10:00:00Z',
    user: {
      user_id: '123',
      username: 'user1'
    }
  },
  {
    type: 'api_response',
    name: '/api/users',
    duration: 200,
    timestamp: '2024-03-20T10:01:00Z',
    user: {
      user_id: '124',
      username: 'user2'
    }
  }
];

const theme = createTheme();

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('PerformanceChart', () => {
  it('renders chart with metrics data', () => {
    renderWithTheme(<PerformanceChart metrics={mockMetrics} title="Performance Metrics" />);

    expect(screen.getByTestId('mock-chart')).toBeInTheDocument();
  });

  it('renders empty state when no metrics provided', () => {
    renderWithTheme(<PerformanceChart metrics={[]} title="Performance Metrics" />);

    expect(screen.getByTestId('mock-chart')).toBeInTheDocument();
  });

  it('renders chart with single metric', () => {
    renderWithTheme(<PerformanceChart metrics={[mockMetrics[0]]} title="Performance Metrics" />);

    expect(screen.getByTestId('mock-chart')).toBeInTheDocument();
  });
}); 