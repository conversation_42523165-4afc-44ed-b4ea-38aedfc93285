import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import { ThemeProvider, createTheme } from '@mui/material';
import PerformanceDashboard from '../PerformanceDashboard';
import { PerformanceMetric } from '../../../services/errorHandling';

// Mock Chart.js
jest.mock('chart.js', () => ({
  Chart: {
    register: jest.fn(),
    defaults: {
      font: { family: 'Roboto' }
    }
  },
  CategoryScale: jest.fn(),
  LinearScale: jest.fn(),
  BarElement: jest.fn(),
  Title: jest.fn(),
  Tooltip: jest.fn(),
  Legend: jest.fn()
}));

// Mock react-chartjs-2
jest.mock('react-chartjs-2', () => ({
  Bar: () => <div data-testid="mock-chart">Chart</div>
}));

const mockMetrics: PerformanceMetric[] = [
  {
    type: 'api_response',
    name: '/api/users',
    duration: 100,
    timestamp: '2024-03-20T10:00:00Z',
    user: {
      user_id: '123',
      username: 'user1'
    }
  },
  {
    type: 'api_response',
    name: '/api/users',
    duration: 200,
    timestamp: '2024-03-20T10:01:00Z',
    user: {
      user_id: '124',
      username: 'user2'
    }
  }
];

const theme = createTheme();

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={theme}>
      {component}
    </ThemeProvider>
  );
};

describe('PerformanceDashboard', () => {
  beforeEach(() => {
    global.fetch = jest.fn();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  it('displays loading state initially', async () => {
    (global.fetch as jest.Mock).mockImplementation(() => 
      new Promise(() => {})
    );

    renderWithTheme(<PerformanceDashboard />);
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('displays performance data after loading', async () => {
    (global.fetch as jest.Mock).mockImplementation(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockMetrics)
      })
    );

    renderWithTheme(<PerformanceDashboard />);

    await waitFor(() => {
      expect(screen.getByTestId('mock-chart')).toBeInTheDocument();
    });

    expect(screen.getAllByText('/api/users')[0]).toBeInTheDocument();
    expect(screen.getByText((content) => content.includes('150'))).toBeInTheDocument(); // Average duration
  });

  it('displays error state when fetch fails', async () => {
    (global.fetch as jest.Mock).mockImplementation(() =>
      Promise.reject(new Error('Failed to fetch'))
    );

    renderWithTheme(<PerformanceDashboard />);

    await waitFor(() => {
      expect(screen.getByText('Failed to fetch')).toBeInTheDocument();
    });
  });

  it('allows filtering by metric type', async () => {
    (global.fetch as jest.Mock).mockImplementation(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockMetrics)
      })
    );

    renderWithTheme(<PerformanceDashboard />);

    await waitFor(() => {
      expect(screen.getByLabelText('Metric Type')).toBeInTheDocument();
    });

    await act(async () => {
      fireEvent.mouseDown(screen.getByLabelText('Metric Type'));
      const options = screen.getAllByText((content) => content.includes('api_response'));
      fireEvent.click(options[0]);
    });

    expect(screen.getAllByText('/api/users')[0]).toBeInTheDocument();
  });

  it('allows filtering by page/endpoint', async () => {
    (global.fetch as jest.Mock).mockImplementation(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockMetrics)
      })
    );

    renderWithTheme(<PerformanceDashboard />);

    await waitFor(() => {
      expect(screen.getByLabelText('Page/Endpoint')).toBeInTheDocument();
    });

    await act(async () => {
      fireEvent.mouseDown(screen.getByLabelText('Page/Endpoint'));
      const options = screen.getAllByText('/api/users');
      fireEvent.click(options[0]);
    });

    expect(screen.getAllByText('/api/users')[0]).toBeInTheDocument();
  });

  it('allows changing the limit of displayed metrics', async () => {
    (global.fetch as jest.Mock).mockImplementation(() =>
      Promise.resolve({
        ok: true,
        json: () => Promise.resolve(mockMetrics)
      })
    );

    renderWithTheme(<PerformanceDashboard />);

    await waitFor(() => {
      expect(screen.getByLabelText('Limit')).toBeInTheDocument();
    });

    await act(async () => {
      fireEvent.change(screen.getByLabelText('Limit'), { target: { value: '50' } });
    });

    await waitFor(() => {
      const fetchCalls = (global.fetch as jest.Mock).mock.calls;
      expect(fetchCalls[fetchCalls.length - 1][0]).toContain('limit=50');
    });
  });
}); 