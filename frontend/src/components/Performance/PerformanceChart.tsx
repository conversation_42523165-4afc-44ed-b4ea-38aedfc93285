import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  useTheme,
} from '@mui/material';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
} from 'chart.js';
import { Bar } from 'react-chartjs-2';
import { PerformanceMetric } from '../../services/errorHandling';

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface ChartDataItem {
  name: string;
  avgDuration: number;
  count: number;
}

interface PerformanceChartProps {
  metrics: PerformanceMetric[];
  title: string;
}

const PerformanceChart: React.FC<PerformanceChartProps> = ({ metrics, title }) => {
  const theme = useTheme();

  // Prepare data for charts
  const prepareChartData = (): ChartDataItem[] => {
    // Group by name and calculate average duration
    const groupedData: Record<string, { name: string; count: number; totalDuration: number }> = {};

    metrics.forEach((metric) => {
      if (!groupedData[metric.name]) {
        groupedData[metric.name] = {
          name: metric.name,
          count: 0,
          totalDuration: 0,
        };
      }

      groupedData[metric.name].count += 1;
      groupedData[metric.name].totalDuration += metric.duration;
    });

    return Object.values(groupedData).map((item) => ({
      name: item.name,
      avgDuration: item.totalDuration / item.count,
      count: item.count,
    }));
  };

  const chartData = prepareChartData();

  const options: ChartOptions<'bar'> = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: title,
      },
    },
    scales: {
      x: {
        ticks: {
          maxRotation: 45,
          minRotation: 45,
        },
      },
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Average Duration (ms)',
        },
      },
      y1: {
        beginAtZero: true,
        position: 'right' as const,
        title: {
          display: true,
          text: 'Count',
        },
      },
    },
  };

  const data = {
    labels: chartData.map((item) => item.name),
    datasets: [
      {
        label: 'Average Duration (ms)',
        data: chartData.map((item) => item.avgDuration),
        backgroundColor: theme.palette.primary.main,
        yAxisID: 'y',
      },
      {
        label: 'Count',
        data: chartData.map((item) => item.count),
        backgroundColor: theme.palette.secondary.main,
        yAxisID: 'y1',
      },
    ],
  };

  return (
    <Card>
      <CardContent>
        <Box sx={{ height: 300, width: '100%' }}>
          <Bar options={options} data={data} />
        </Box>
      </CardContent>
    </Card>
  );
};

export default PerformanceChart; 