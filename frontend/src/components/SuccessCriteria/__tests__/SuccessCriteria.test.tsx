import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import SuccessCriteria from '../index';
import { successCriteriaService } from '../../../services/successCriteria';

// Mock the success criteria service
jest.mock('../../../services/successCriteria', () => ({
  successCriteriaService: {
    getSuccessCriteria: jest.fn(),
    updateSuccessCriteriaStatus: jest.fn(),
    addSuccessCriteriaDetail: jest.fn(),
  },
}));

const theme = createTheme();

const mockCriteria = [
  {
    id: '1',
    name: 'Test Scenarios',
    description: 'All test scenarios pass consistently',
    status: 'success' as const,
    category: 'functionality' as const,
    details: ['Unit tests passing', 'Integration tests passing'],
    lastUpdated: new Date().toISOString(),
  },
  {
    id: '2',
    name: 'Performance Metrics',
    description: 'File upload to injection completion time < 60 seconds',
    status: 'pending' as const,
    category: 'performance' as const,
    details: ['Average completion time: 45 seconds'],
    lastUpdated: new Date().toISOString(),
  },
];

describe('SuccessCriteria Component', () => {
  beforeEach(() => {
    (successCriteriaService.getSuccessCriteria as jest.Mock).mockResolvedValue({
      data: mockCriteria,
      total: 2,
      page: 1,
      pageSize: 10,
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders loading state initially', () => {
    render(
      <ThemeProvider theme={theme}>
        <SuccessCriteria />
      </ThemeProvider>
    );
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('renders success criteria cards after loading', async () => {
    render(
      <ThemeProvider theme={theme}>
        <SuccessCriteria />
      </ThemeProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Scenarios')).toBeInTheDocument();
      expect(screen.getByText('Performance Metrics')).toBeInTheDocument();
    });
  });

  it('handles error state', async () => {
    (successCriteriaService.getSuccessCriteria as jest.Mock).mockRejectedValue(new Error('API Error'));

    render(
      <ThemeProvider theme={theme}>
        <SuccessCriteria />
      </ThemeProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('Failed to fetch success criteria')).toBeInTheDocument();
    });
  });

  it('updates status when clicking status buttons', async () => {
    const updatedCriteria = {
      ...mockCriteria[0],
      status: 'failed' as const,
    };
    (successCriteriaService.updateSuccessCriteriaStatus as jest.Mock).mockResolvedValue(updatedCriteria);

    render(
      <ThemeProvider theme={theme}>
        <SuccessCriteria />
      </ThemeProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Scenarios')).toBeInTheDocument();
    });

    const markFailedButton = screen.getByText('Mark Failed');
    fireEvent.click(markFailedButton);

    await waitFor(() => {
      expect(successCriteriaService.updateSuccessCriteriaStatus).toHaveBeenCalledWith('1', 'failed');
    });
  });

  it('opens dialog and adds new detail', async () => {
    const updatedCriteria = {
      ...mockCriteria[0],
      details: [...mockCriteria[0].details, 'New test added'],
    };
    (successCriteriaService.addSuccessCriteriaDetail as jest.Mock).mockResolvedValue(updatedCriteria);

    render(
      <ThemeProvider theme={theme}>
        <SuccessCriteria />
      </ThemeProvider>
    );

    await waitFor(() => {
      expect(screen.getByText('Test Scenarios')).toBeInTheDocument();
    });

    const addDetailButton = screen.getByText('Add Detail');
    fireEvent.click(addDetailButton);

    const dialog = screen.getByRole('dialog');
    expect(dialog).toBeInTheDocument();

    const textField = screen.getByLabelText('New Detail');
    fireEvent.change(textField, { target: { value: 'New test added' } });

    const addButton = screen.getByText('Add');
    fireEvent.click(addButton);

    await waitFor(() => {
      expect(successCriteriaService.addSuccessCriteriaDetail).toHaveBeenCalledWith('1', 'New test added');
    });
  });
}); 