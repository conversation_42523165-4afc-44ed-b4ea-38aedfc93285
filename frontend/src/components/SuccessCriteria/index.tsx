import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  CircularProgress,
  Alert,
  Divider,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import { successCriteriaService } from '../../services/successCriteria';
import type { SuccessCriteria } from '../../services/successCriteria';

// Styled components
const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  transition: 'transform 0.2s ease-in-out',
  '&:hover': {
    transform: 'translateY(-4px)',
  },
}));

const StatusChip = styled(Chip)(({ theme, color }) => ({
  marginLeft: theme.spacing(1),
  backgroundColor: color === 'success' ? theme.palette.success.light : theme.palette.error.light,
  color: color === 'success' ? theme.palette.success.dark : theme.palette.error.dark,
}));

const SuccessCriteria: React.FC = () => {
  const [criteria, setCriteria] = useState<SuccessCriteria[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCriteria, setSelectedCriteria] = useState<SuccessCriteria | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [newDetail, setNewDetail] = useState('');

  useEffect(() => {
    fetchCriteria();
  }, []);

  const fetchCriteria = async () => {
    try {
      setLoading(true);
      const response = await successCriteriaService.getSuccessCriteria();
      setCriteria(response.data);
    } catch (err) {
      setError('Failed to fetch success criteria');
      console.error('Error fetching success criteria:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusUpdate = async (id: string, newStatus: 'success' | 'pending' | 'failed') => {
    try {
      const updatedCriteria = await successCriteriaService.updateSuccessCriteriaStatus(id, newStatus);
      setCriteria(criteria.map(item => 
        item.id === id ? updatedCriteria : item
      ));
    } catch (err) {
      console.error('Error updating status:', err);
    }
  };

  const handleAddDetail = async () => {
    if (!selectedCriteria || !newDetail.trim()) return;

    try {
      const updatedCriteria = await successCriteriaService.addSuccessCriteriaDetail(
        selectedCriteria.id,
        newDetail
      );
      setCriteria(criteria.map(item => 
        item.id === selectedCriteria.id ? updatedCriteria : item
      ));
      setNewDetail('');
      setIsDialogOpen(false);
    } catch (err) {
      console.error('Error adding detail:', err);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box m={3}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Box p={3}>
      <Typography variant="h4" gutterBottom>
        Success Criteria
      </Typography>
      <Typography variant="subtitle1" color="textSecondary" paragraph>
        Overview of system performance and compliance metrics
      </Typography>

      <Grid container spacing={3}>
        {criteria.map((item) => (
          <Grid item xs={12} md={6} lg={4} key={item.id}>
            <StyledCard>
              <CardContent>
                <Box display="flex" alignItems="center" mb={2}>
                  <Typography variant="h6" component="h2">
                    {item.name}
                  </Typography>
                  <StatusChip
                    label={item.status}
                    color={item.status === 'success' ? 'success' : 'error'}
                    size="small"
                  />
                </Box>

                <Typography color="textSecondary" gutterBottom>
                  {item.description}
                </Typography>

                <Chip
                  label={item.category}
                  size="small"
                  sx={{ mb: 2 }}
                />

                <Divider sx={{ my: 2 }} />

                <Typography variant="body2" color="textSecondary">
                  Details:
                </Typography>
                <Box component="ul" pl={2}>
                  {item.details.map((detail, index) => (
                    <Typography
                      component="li"
                      variant="body2"
                      key={index}
                      color="textSecondary"
                    >
                      {detail}
                    </Typography>
                  ))}
                </Box>

                <Box mt={2} display="flex" gap={1}>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => {
                      setSelectedCriteria(item);
                      setIsDialogOpen(true);
                    }}
                  >
                    Add Detail
                  </Button>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => handleStatusUpdate(item.id, 'success')}
                    color="success"
                  >
                    Mark Success
                  </Button>
                  <Button
                    size="small"
                    variant="outlined"
                    onClick={() => handleStatusUpdate(item.id, 'failed')}
                    color="error"
                  >
                    Mark Failed
                  </Button>
                </Box>

                <Typography
                  variant="caption"
                  color="textSecondary"
                  sx={{ display: 'block', mt: 2 }}
                >
                  Last updated: {new Date(item.lastUpdated).toLocaleString()}
                </Typography>
              </CardContent>
            </StyledCard>
          </Grid>
        ))}
      </Grid>

      <Dialog open={isDialogOpen} onClose={() => setIsDialogOpen(false)}>
        <DialogTitle>Add Detail to {selectedCriteria?.name}</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="New Detail"
            fullWidth
            multiline
            rows={4}
            value={newDetail}
            onChange={(e) => setNewDetail(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleAddDetail} color="primary">
            Add
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SuccessCriteria; 