import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Divider,
  List,
  ListItem,
  ListItemText,
  Chip,
  Button,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Security as SecurityIcon,
  Schedule as ScheduleIcon,
  BugReport as BugIcon,
} from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';
import SecurityTestingService, { SecurityTestResult } from '../../services/securityTesting';

interface SecurityTestDetailsProps {
  testId: string;
  onClose?: () => void;
  onRerun?: () => void;
}

const SecurityTestDetails: React.FC<SecurityTestDetailsProps> = ({
  testId,
  onClose,
  onRerun,
}) => {
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState<string | null>(null);
  const [testDetails, setTestDetails] = React.useState<SecurityTestResult | null>(null);
  const { token } = useAuth();
  const securityService = React.useMemo(() => {
    if (!token) return null;
    return new SecurityTestingService(token);
  }, [token]);

  React.useEffect(() => {
    if (!securityService) {
      setError('Authentication required');
      return;
    }
    loadTestDetails();
  }, [securityService, testId]);

  const loadTestDetails = async () => {
    if (!securityService) return;
    
    setLoading(true);
    try {
      const details = await securityService.getTestResults(testId);
      setTestDetails(details);
      setError(null);
    } catch (err) {
      setError('Failed to load test details');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'error';
      case 'high':
        return 'error';
      case 'medium':
        return 'warning';
      case 'low':
        return 'info';
      default:
        return 'default';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'passed':
        return 'success';
      case 'failed':
        return 'error';
      case 'warning':
        return 'warning';
      default:
        return 'info';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!testDetails) {
    return (
      <Alert severity="info">
        No test details available
      </Alert>
    );
  }

  return (
    <Paper sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <SecurityIcon sx={{ mr: 1 }} />
        <Typography variant="h5">Security Test Details</Typography>
      </Box>

      <Grid container spacing={3}>
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
            <ScheduleIcon />
            <Typography variant="subtitle1">
              Last Run: {testDetails.timestamp}
            </Typography>
            <Chip
              label={testDetails.status.toUpperCase()}
              color={getStatusColor(testDetails.status)}
              sx={{ ml: 'auto' }}
            />
          </Box>
          <Divider />
        </Grid>

        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Test Results
          </Typography>
          <Typography>{testDetails.details}</Typography>
        </Grid>

        {testDetails.vulnerabilities && testDetails.vulnerabilities.length > 0 && (
          <Grid item xs={12}>
            <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
              <BugIcon sx={{ mr: 1 }} />
              Identified Vulnerabilities
            </Typography>
            <List>
              {testDetails.vulnerabilities.map((vuln, index) => (
                <ListItem
                  key={index}
                  sx={{
                    flexDirection: 'column',
                    alignItems: 'flex-start',
                    bgcolor: 'background.paper',
                    borderRadius: 1,
                    mb: 1,
                  }}
                >
                  <Box sx={{ display: 'flex', width: '100%', mb: 1 }}>
                    <ListItemText
                      primary={vuln.description}
                      secondary={`Recommendation: ${vuln.recommendation}`}
                    />
                    <Chip
                      label={vuln.severity.toUpperCase()}
                      color={getSeverityColor(vuln.severity)}
                      size="small"
                      sx={{ ml: 2 }}
                    />
                  </Box>
                </ListItem>
              ))}
            </List>
          </Grid>
        )}

        <Grid item xs={12}>
          <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
            <Button
              variant="contained"
              onClick={onRerun}
              startIcon={<SecurityIcon />}
            >
              Rerun Test
            </Button>
            <Button
              variant="outlined"
              onClick={onClose}
            >
              Close
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default SecurityTestDetails; 