import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import axios, { AxiosRequestHeaders } from 'axios';
import { API_ENDPOINTS } from '../utils/apiConfig';

interface AuthContextType {
  token: string | null;
  user: any | null;
  login: (token: string, user: any) => void;
  logout: () => void;
  isAuthenticated: boolean;
  refreshToken: () => Promise<boolean>;
  authError: string | null;
  isAuthLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [token, setToken] = useState<string | null>(null);
  const [user, setUser] = useState<any | null>(null);
  const [authError, setAuthError] = useState<string | null>(null);
  const [isAuthLoading, setIsAuthLoading] = useState(true);

  useEffect(() => {
    // Check for stored token on mount
    const storedToken = localStorage.getItem('token');
    if (storedToken) {
      setToken(storedToken);
      // Verify token validity
      verifyToken(storedToken);
    } else {
      setIsAuthLoading(false);
    }
  }, []);

  const verifyToken = async (tokenToVerify: string) => {
    try {
      const response = await axios.get(API_ENDPOINTS.AUTH.TEST_TOKEN, {
        headers: {
          Authorization: `Bearer ${tokenToVerify}`,
        },
      });
      setUser(response.data.user);
      setAuthError(null);
    } catch (error) {
      console.error('Token verification failed:', error);
      logout();
    } finally {
      setIsAuthLoading(false);
    }
  };

  const login = (newToken: string, userData: any) => {
    setToken(newToken);
    setUser(userData);
    localStorage.setItem('token', newToken);
    setAuthError(null);
  };

  const logout = () => {
    setToken(null);
    setUser(null);
    localStorage.removeItem('token');
    setAuthError(null);
  };

  const refreshToken = async (): Promise<boolean> => {
    try {
      const response = await axios.post(API_ENDPOINTS.AUTH.REFRESH, {}, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });
      const newToken = response.data.access_token;
      setToken(newToken);
      localStorage.setItem('token', newToken);
      return true;
    } catch (error) {
      console.error('Token refresh failed:', error);
      logout();
      return false;
    }
  };

  const value = {
    token,
    user,
    login,
    logout,
    isAuthenticated: !!token,
    refreshToken,
    authError,
    isAuthLoading,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}; 