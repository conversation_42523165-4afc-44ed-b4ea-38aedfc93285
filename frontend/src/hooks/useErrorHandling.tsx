import { useSnackbar } from 'notistack';
import { useEffect } from 'react';
import { errorHandlingService, ErrorHandlerOptions } from '../services/errorHandling';

export const useErrorHandling = () => {
  const { enqueueSnackbar } = useSnackbar();

  useEffect(() => {
    errorHandlingService.setSnackbar(enqueueSnackbar);
  }, [enqueueSnackbar]);

  const handleError = async (
    error: any,
    component?: string,
    options: ErrorHandlerOptions = {}
  ) => {
    return errorHandlingService.handleError(error, component, options);
  };

  const createErrorBoundaryHandler = (component: string) => {
    return errorHandlingService.createErrorBoundaryHandler(component);
  };

  return {
    handleError,
    createErrorBoundaryHandler,
  };
}; 